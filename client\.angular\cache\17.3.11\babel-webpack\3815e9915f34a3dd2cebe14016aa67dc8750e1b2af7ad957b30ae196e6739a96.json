{"ast": null, "code": "import { Subject, interval, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./import.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/confirmdialog\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/tabmenu\";\nimport * as i8 from \"primeng/tooltip\";\nimport * as i9 from \"primeng/table\";\nimport * as i10 from \"primeng/progressbar\";\nimport * as i11 from \"primeng/toast\";\nimport * as i12 from \"primeng/breadcrumb\";\nconst _c0 = () => ({\n  height: \"30px\"\n});\nfunction ImportComponent_p_tabMenu_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabMenu\", 4);\n    i0.ɵɵtwoWayListener(\"activeItemChange\", function ImportComponent_p_tabMenu_5_Template_p_tabMenu_activeItemChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.activeSubItem, $event) || (ctx_r1.activeSubItem = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"model\", ctx_r1.activeItem[\"subItems\"]);\n    i0.ɵɵtwoWayProperty(\"activeItem\", ctx_r1.activeSubItem);\n    i0.ɵɵproperty(\"styleClass\", \"flexible-tabs border-1 border-round border-50 overflow-hidden mb-3\");\n  }\n}\nfunction ImportComponent_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h4\", 22);\n    i0.ɵɵtext(2, \"Upload Your File Here\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 22);\n    i0.ɵɵtext(4, \"File Supported: CSV,XLSX\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 22);\n    i0.ɵɵtext(6, \"Maximum File Size:1 MB\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ImportComponent_label_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 23)(1, \"i\", 24);\n    i0.ɵɵtext(2, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Add File \");\n    i0.ɵɵelementStart(4, \"input\", 25);\n    i0.ɵɵlistener(\"change\", function ImportComponent_label_26_Template_input_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileSelect($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.selectedFile);\n  }\n}\nfunction ImportComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"p-progressBar\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(4, _c0));\n    i0.ɵɵproperty(\"value\", ctx_r1.state_data == null ? null : ctx_r1.state_data.progress)(\"showValue\", true);\n  }\n}\nfunction ImportComponent_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function ImportComponent_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.uploadFile());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ImportComponent_ng_container_30_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 29)(2, \"ul\", 30)(3, \"li\", 31)(4, \"span\", 32);\n    i0.ɵɵtext(5, \"File Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \": \");\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\", 31)(10, \"span\", 32);\n    i0.ɵɵtext(11, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \": \");\n    i0.ɵɵelementStart(13, \"span\", 33);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementStart(15, \"i\", 24);\n    i0.ɵɵtext(16, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p-button\", 34);\n    i0.ɵɵlistener(\"click\", function ImportComponent_ng_container_30_ng_container_1_Template_p_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.downloadFile(ctx_r1.state_data == null ? null : ctx_r1.state_data.id));\n    });\n    i0.ɵɵelementStart(18, \"i\", 35);\n    i0.ɵɵtext(19, \"download\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"li\", 31)(21, \"span\", 32);\n    i0.ɵɵtext(22, \"Size\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \": \");\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"li\", 31)(28, \"span\", 32);\n    i0.ɵɵtext(29, \"Creator\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \": \");\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \"-\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"li\", 31)(34, \"span\", 32);\n    i0.ɵɵtext(35, \"Created at\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(36, \": \");\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38);\n    i0.ɵɵpipe(39, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"li\", 31)(41, \"span\", 32);\n    i0.ɵɵtext(42, \"Last Modified\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(43, \": \");\n    i0.ɵɵelementStart(44, \"span\");\n    i0.ɵɵtext(45);\n    i0.ɵɵpipe(46, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.state_data == null ? null : ctx_r1.state_data.file_name);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.state_data == null ? null : ctx_r1.state_data.file_status, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"rounded\", true)(\"styleClass\", \"p-button-icon-only\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(26, 7, (ctx_r1.state_data == null ? null : ctx_r1.state_data.file_size) / 1024, \"1.0-2\"), \" KB\");\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(39, 10, ctx_r1.state_data == null ? null : ctx_r1.state_data.createdAt, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(46, 13, ctx_r1.state_data == null ? null : ctx_r1.state_data.updatedAt, \"dd/MM/yyyy\"));\n  }\n}\nfunction ImportComponent_ng_container_30_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" No records found. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ImportComponent_ng_container_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ImportComponent_ng_container_30_ng_container_1_Template, 47, 16, \"ng-container\", 17)(2, ImportComponent_ng_container_30_ng_container_2_Template, 2, 0, \"ng-container\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.state_data == null ? null : ctx_r1.state_data.file_status);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.state_data == null ? null : ctx_r1.state_data.file_status));\n  }\n}\nfunction ImportComponent_ng_container_31_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2, \"File Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Success\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Failed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Created Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\");\n    i0.ɵɵtext(18, \"Remove\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImportComponent_ng_container_31_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\")(17, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ImportComponent_ng_container_31_ng_template_3_Template_button_click_17_listener() {\n      const log_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.downloadFile(log_r7 == null ? null : log_r7.id));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\")(19, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ImportComponent_ng_container_31_ng_template_3_Template_button_click_19_listener($event) {\n      const log_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r1.confirmRemove(log_r7));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const log_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r7 == null ? null : log_r7.file_name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", (log_r7 == null ? null : log_r7.completed_count) / (log_r7 == null ? null : log_r7.total_count) * 100, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r7 == null ? null : log_r7.total_count);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r7 == null ? null : log_r7.success_count);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r7 == null ? null : log_r7.failed_count);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 7, log_r7 == null ? null : log_r7.createdAt, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(log_r7 == null ? null : log_r7.file_status);\n  }\n}\nfunction ImportComponent_ng_container_31_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 42);\n    i0.ɵɵtext(2, \"No records found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImportComponent_ng_container_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-table\", 36);\n    i0.ɵɵtemplate(2, ImportComponent_ng_container_31_ng_template_2_Template, 19, 0, \"ng-template\", 37)(3, ImportComponent_ng_container_31_ng_template_3_Template, 20, 10, \"ng-template\", 38)(4, ImportComponent_ng_container_31_ng_template_4_Template, 3, 0, \"ng-template\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r1.log_data)(\"paginator\", false)(\"rows\", 5)(\"sortMode\", \"multiple\");\n  }\n}\nexport class ImportComponent {\n  constructor(route, flexiblegroupuploadservice, messageservice, confirmationservice, cdr) {\n    this.route = route;\n    this.flexiblegroupuploadservice = flexiblegroupuploadservice;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.cdr = cdr;\n    this.items = [{\n      label: 'Prospect',\n      icon: 'pi pi-list',\n      routerLink: ['/store/import', 'Prospect'],\n      subItems: [{\n        label: 'Sub1',\n        slug: 'sub1',\n        routerLink: ['/store/import', 'Prospect', 'sub1']\n      }, {\n        label: 'Sub2',\n        slug: 'sub2',\n        routerLink: ['/store/import', 'Prospect', 'sub2']\n      }]\n    }, {\n      label: 'Account',\n      icon: 'pi pi-users',\n      routerLink: ['/store/import', 'Account'],\n      subItems: [{\n        label: 'Sub1',\n        slug: 'sub1',\n        routerLink: ['/store/import', 'Account', 'sub1']\n      }, {\n        label: 'Sub2',\n        slug: 'sub2',\n        routerLink: ['/store/import', 'Account', 'sub2']\n      }]\n    }, {\n      label: 'Contact',\n      icon: 'pi pi-building',\n      routerLink: ['/store/import', 'Contact'],\n      subItems: [{\n        label: 'Sub1',\n        slug: 'sub1',\n        routerLink: ['/store/import', 'Contact', 'sub1']\n      }, {\n        label: 'Sub2',\n        slug: 'sub2',\n        routerLink: ['/store/import', 'Contact', 'sub2']\n      }]\n    }, {\n      label: 'Activities',\n      icon: 'pi pi-briefcase',\n      routerLink: ['/store/import', 'Activities'],\n      subItems: [{\n        label: 'Sub1',\n        slug: 'sub1',\n        routerLink: ['/store/import', 'Activities', 'sub1']\n      }, {\n        label: 'Sub2',\n        slug: 'sub2',\n        routerLink: ['/store/import', 'Activities', 'sub2']\n      }]\n    }, {\n      label: 'Opportunities',\n      icon: 'pi pi-briefcase',\n      routerLink: ['/store/import', 'Opportunities'],\n      subItems: [{\n        label: 'Sub1',\n        slug: 'sub1',\n        routerLink: ['/store/import', 'Opportunities', 'sub1']\n      }, {\n        label: 'Sub2',\n        slug: 'sub2',\n        routerLink: ['/store/import', 'Opportunities', 'sub2']\n      }]\n    }];\n    this.activeItem = {};\n    this.activeSubItem = {};\n    this.id = '';\n    this.subId = '';\n    this.bitems = [{\n      label: 'Import',\n      routerLink: ['/store/import']\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/']\n    };\n    this.unsubscribe$ = new Subject();\n    this.intervalSubscription = null;\n    this.selectedFile = null;\n    this.apiurl = ``;\n    this.fileurl = ``;\n    this.exporturl = ``;\n    this.table_name = '';\n    this.state_data = {\n      progress: 10\n    };\n    this.log_data = [];\n    this.activeUploadItem = {};\n    this.uploadItems = [{\n      label: 'File Details',\n      icon: 'pi pi-file',\n      slug: 'file_details'\n    }, {\n      label: 'File Log',\n      icon: 'pi pi-file',\n      slug: 'file_log'\n    }];\n    this.paramMapSubscription = null;\n  }\n  ngOnInit() {\n    this.paramMapSubscription = this.route.paramMap.subscribe(params => {\n      this.id = params.get('id') || '';\n      this.subId = params.get('sub-id') || '';\n      const found = this.items.find(item => item.label && item.label.toLowerCase() === this.id.toLowerCase());\n      this.activeItem = found || this.items[0];\n      const subItems = this.activeItem && this.activeItem['subItems'] ? this.activeItem['subItems'] : [];\n      if (subItems.length && this.subId) {\n        const foundSub = subItems.find(sub => sub.slug && sub.slug.toLowerCase() === this.subId.toLowerCase());\n        this.activeSubItem = foundSub || subItems[0];\n      } else if (subItems.length) {\n        this.activeSubItem = subItems[0];\n      } else {\n        this.activeSubItem = {};\n      }\n      this.cdr.detectChanges();\n      this.initUpload();\n    });\n  }\n  initUpload() {\n    this.activeUploadItem = this.uploadItems[0];\n    this.fetchFilelog();\n    this.fetchProgresstatus();\n  }\n  startInterval() {\n    if (!this.intervalSubscription) {\n      this.intervalSubscription = interval(5000).subscribe(() => {\n        this.fetchProgresstatus();\n      });\n    }\n  }\n  stopInterval() {\n    if (this.intervalSubscription) {\n      console.log('STOP INTERVAL');\n      this.intervalSubscription.unsubscribe(); // Unsubscribe directly\n      this.intervalSubscription = null; // Optionally reset the subscription\n    }\n  }\n  onFileSelect(event) {\n    const file = event.target.files[0];\n    const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'];\n    const maxSize = 1 * 1024 * 1024;\n    if (file && file.size <= maxSize && allowedTypes.includes(file.type)) {\n      this.selectedFile = file;\n    } else {\n      this.selectedFile = null;\n      this.messageservice.add({\n        severity: 'error',\n        detail: 'Please upload a file in one of the following formats: .csv,.xlsx, and ensure the file size is less than 1 MB.'\n      });\n    }\n  }\n  uploadFile() {\n    if (!this.selectedFile) return;\n    const formData = new FormData();\n    formData.append('file', this.selectedFile);\n    this.state_data = {\n      ...this.state_data,\n      progress: 2,\n      file_name: this.selectedFile?.name,\n      file_size: this.selectedFile?.size,\n      file_status: 'IN_PROGRESS',\n      file_type: this.selectedFile?.type\n    };\n    this.flexiblegroupuploadservice.save(this.apiurl, formData).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        if (response.status === 'PROGRESS') {\n          this.selectedFile = null;\n          this.startInterval();\n        }\n      },\n      error: error => {\n        console.error('File upload error:', error);\n      }\n    });\n  }\n  fetchProgresstatus() {\n    this.flexiblegroupuploadservice.getProgessStatus(this.fileurl, this.table_name).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        if (response?.data.length) {\n          const state_data = response?.data?.[0] || null;\n          if (state_data) {\n            state_data.progress = state_data?.total_count ? Math.round(state_data?.completed_count / state_data?.total_count * 100) : 2;\n          }\n          if (['DONE', 'FAILD'].includes(state_data.file_status)) {\n            this.stopInterval();\n            this.state_data = state_data;\n          } else {\n            this.startInterval();\n            this.state_data = state_data;\n          }\n        }\n      },\n      error: error => {\n        console.error('Error fetching records:', error);\n      }\n    });\n  }\n  fetchFilelog() {\n    this.flexiblegroupuploadservice.getFilelog(this.fileurl, this.table_name).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        if (response?.data) {\n          this.log_data = response?.data;\n        } else {\n          console.error('No Records Availble.');\n        }\n      },\n      error: error => {\n        console.error('Error fetching records:', error);\n      }\n    });\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    const deleteurl = this.fileurl + '/' + item.documentId;\n    this.flexiblegroupuploadservice.delete(deleteurl).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: res => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.refresh();\n      },\n      error: err => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  downloadFile(id) {\n    const exporturl = this.exporturl + '/' + id;\n    const tabname = 'fg_relationship';\n    this.flexiblegroupuploadservice.export(id, exporturl, tabname).then(response => {\n      this.messageservice.add({\n        severity: 'success',\n        detail: 'File Downloaded Successfully!'\n      });\n    }).catch(error => {\n      this.messageservice.add({\n        severity: 'error',\n        detail: 'Error while processing your request.'\n      });\n    });\n  }\n  refresh() {\n    this.fetchFilelog();\n  }\n  ngOnDestroy() {\n    this.stopInterval();\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n    if (this.paramMapSubscription) {\n      this.paramMapSubscription.unsubscribe();\n      this.paramMapSubscription = null;\n    }\n  }\n  static {\n    this.ɵfac = function ImportComponent_Factory(t) {\n      return new (t || ImportComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ImportService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImportComponent,\n      selectors: [[\"app-import\"]],\n      decls: 33,\n      vars: 17,\n      consts: [[\"position\", \"top-right\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\", \"surface-card\"], [1, \"all-page-title-sec\", \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [3, \"model\", \"home\", \"styleClass\"], [3, \"activeItemChange\", \"model\", \"activeItem\", \"styleClass\"], [3, \"model\", \"activeItem\", \"styleClass\", \"activeItemChange\", 4, \"ngIf\"], [1, \"tab-cnt\", \"px-4\", \"pt-3\"], [1, \"file-upload\", \"mb-5\"], [1, \"grid\"], [1, \"col-12\", \"md:col-6\"], [1, \"gap-2\", \"border-1\", \"border-round\", \"border-dashed\", \"border-100\", \"p-2\", \"h-full\"], [1, \"p-2\"], [1, \"p-1\"], [1, \"pi\", \"pi-arrow-right\"], [\"href\", \"assets/files/fg-relationship.xlsx\", 2, \"text-decoration\", \"underline\"], [1, \"file-upload-box\", \"py-4\", \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\", \"gap-3\", \"border-1\", \"border-round\", \"border-dashed\", \"border-100\"], [1, \"material-symbols-rounded\", \"text-primary\", \"text-7xl\"], [4, \"ngIf\"], [\"for\", \"file-upload\", \"class\", \"p-element p-ripple p-button p-button-outlined p-component w-9rem justify-content-center font-semibold gap-2\", 4, \"ngIf\"], [\"class\", \"w-10rem\", 4, \"ngIf\"], [\"label\", \"Upload\", \"pButton\", \"\", \"type\", \"button\", \"class\", \"p-button-lg\", 3, \"click\", 4, \"ngIf\"], [3, \"activeItemChange\", \"click\", \"model\", \"activeItem\", \"styleClass\"], [1, \"m-0\"], [\"for\", \"file-upload\", 1, \"p-element\", \"p-ripple\", \"p-button\", \"p-button-outlined\", \"p-component\", \"w-9rem\", \"justify-content-center\", \"font-semibold\", \"gap-2\"], [1, \"material-symbols-rounded\"], [\"type\", \"file\", \"name\", \"file\", \"accept\", \".csv,.xlsx\", \"id\", \"file-upload\", 2, \"display\", \"none\", 3, \"change\", \"disabled\"], [1, \"w-10rem\"], [3, \"value\", \"showValue\"], [\"label\", \"Upload\", \"pButton\", \"\", \"type\", \"button\", 1, \"p-button-lg\", 3, \"click\"], [1, \"file-details\"], [1, \"m-0\", \"p-4\", \"list-none\", \"flex\", \"flex-column\", \"gap-4\", \"surface-50\", \"border-round\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"flex\", \"w-9rem\", \"font-semibold\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"text-green-400\"], [\"pTooltip\", \"Export\", 1, \"ml-auto\", 3, \"click\", \"rounded\", \"styleClass\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [3, \"value\", \"paginator\", \"rows\", \"sortMode\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-cloud-download\", \"pTooltip\", \"Export\", 1, \"p-button-sm\", \"p-button-primary\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [\"colspan\", \"8\"]],\n      template: function ImportComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p-tabMenu\", 4);\n          i0.ɵɵtwoWayListener(\"activeItemChange\", function ImportComponent_Template_p_tabMenu_activeItemChange_4_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.activeItem, $event) || (ctx.activeItem = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, ImportComponent_p_tabMenu_5_Template, 1, 3, \"p-tabMenu\", 5);\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"h5\");\n          i0.ɵɵtext(9, \"Add File\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"form\")(11, \"div\", 8)(12, \"div\", 9)(13, \"div\", 10)(14, \"h6\", 11);\n          i0.ɵɵtext(15, \"The excel file should list the flexible group relationship details in the following format: \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\", 12);\n          i0.ɵɵelement(17, \"i\", 13);\n          i0.ɵɵtext(18, \" Template: \");\n          i0.ɵɵelementStart(19, \"a\", 14);\n          i0.ɵɵtext(20, \"Download\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"div\", 9)(22, \"div\", 15)(23, \"i\", 16);\n          i0.ɵɵtext(24, \"cloud_upload\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, ImportComponent_ng_container_25_Template, 7, 0, \"ng-container\", 17)(26, ImportComponent_label_26_Template, 5, 1, \"label\", 18)(27, ImportComponent_div_27_Template, 2, 5, \"div\", 19)(28, ImportComponent_button_28_Template, 1, 0, \"button\", 20);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(29, \"p-tabMenu\", 21);\n          i0.ɵɵtwoWayListener(\"activeItemChange\", function ImportComponent_Template_p_tabMenu_activeItemChange_29_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.activeItem, $event) || (ctx.activeItem = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"click\", function ImportComponent_Template_p_tabMenu_click_29_listener() {\n            return ctx.refresh();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(30, ImportComponent_ng_container_30_Template, 3, 2, \"ng-container\", 17)(31, ImportComponent_ng_container_31_Template, 5, 4, \"ng-container\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(32, \"p-confirmDialog\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.bitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"model\", ctx.items);\n          i0.ɵɵtwoWayProperty(\"activeItem\", ctx.activeItem);\n          i0.ɵɵproperty(\"styleClass\", \"flexible-tabs border-1 border-round border-50 overflow-hidden\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeItem[\"subItems\"]);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"ngIf\", (ctx.state_data == null ? null : ctx.state_data.file_status) === \"DONE\" || !(ctx.state_data == null ? null : ctx.state_data.file_status));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.selectedFile && ((ctx.state_data == null ? null : ctx.state_data.file_status) === \"DONE\" || !(ctx.state_data == null ? null : ctx.state_data.file_status)));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.state_data == null ? null : ctx.state_data.file_status) === \"IN_PROGRESS\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedFile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"model\", ctx.uploadItems);\n          i0.ɵɵtwoWayProperty(\"activeItem\", ctx.activeItem);\n          i0.ɵɵproperty(\"styleClass\", \"flexible-tabs border-1 border-round border-50 overflow-hidden mb-3\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.activeUploadItem == null ? null : ctx.activeUploadItem.slug) === \"file_details\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.activeUploadItem == null ? null : ctx.activeUploadItem.slug) === \"file_log\");\n        }\n      },\n      dependencies: [i4.NgIf, i5.ConfirmDialog, i6.ButtonDirective, i6.Button, i3.PrimeTemplate, i7.TabMenu, i8.Tooltip, i9.Table, i10.ProgressBar, i11.Toast, i12.Breadcrumb, i4.DecimalPipe, i4.DatePipe],\n      styles: [\".upload-container[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  margin: 20px auto;\\n  text-align: center;\\n}\\n\\ntable[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  margin-top: 20px;\\n}\\n\\ntable[_ngcontent-%COMP%], th[_ngcontent-%COMP%], td[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n}\\n\\nth[_ngcontent-%COMP%], td[_ngcontent-%COMP%] {\\n  padding: 8px;\\n  text-align: left;\\n}\\n\\n[_nghost-%COMP%]     .flexible-tabs .p-tabview-nav-container {\\n  background: var(--surface-c);\\n  padding: 4px 4px 0 4px;\\n}\\n[_nghost-%COMP%]     .flexible-tabs .p-tabview-nav-container li {\\n  margin: 0;\\n}\\n[_nghost-%COMP%]     .flexible-tabs .p-tabview-nav-container li .p-tabview-nav-link {\\n  padding: 12px 20px;\\n  font-weight: 600;\\n}\\n[_nghost-%COMP%]     .flexible-tabs .p-tabview-nav-container li.p-highlight .p-tabview-nav-link {\\n  background: var(--primary-color);\\n  color: var(--surface-0);\\n}\\n[_nghost-%COMP%]     .flexible-tabs .p-tabview-nav-container .p-tabview-ink-bar {\\n  display: none !important;\\n}\\n[_nghost-%COMP%]     .flexible-tabs .p-tabmenu-nav-container {\\n  background: var(--surface-c);\\n  padding: 4px 4px 0 4px;\\n}\\n[_nghost-%COMP%]     .flexible-tabs .p-tabmenu-nav-container li {\\n  margin: 0;\\n}\\n[_nghost-%COMP%]     .flexible-tabs .p-tabmenu-nav-container li .p-menuitem-link {\\n  padding: 12px 20px;\\n  font-weight: 600;\\n}\\n[_nghost-%COMP%]     .flexible-tabs .p-tabmenu-nav-container li.p-tabmenuitem.p-highlight .p-menuitem-link {\\n  background: var(--primary-color);\\n  color: var(--surface-0);\\n}\\n[_nghost-%COMP%]     .flexible-tabs .p-tabmenu-nav-container .p-tabmenu-ink-bar {\\n  display: none !important;\\n}\\n[_nghost-%COMP%]     .p-tabmenu .p-tabmenu-nav-btn.p-link {\\n  background: var(--surface-0);\\n}\\n[_nghost-%COMP%]     .uploaded-file-list {\\n  max-width: 600px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "interval", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "ImportComponent_p_tabMenu_5_Template_p_tabMenu_activeItemChange_0_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "activeSubItem", "ɵɵresetView", "ɵɵelementEnd", "ɵɵproperty", "activeItem", "ɵɵtwoWayProperty", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵlistener", "ImportComponent_label_26_Template_input_change_4_listener", "_r3", "onFileSelect", "ɵɵadvance", "selectedFile", "ɵɵelement", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "state_data", "progress", "ImportComponent_button_28_Template_button_click_0_listener", "_r4", "uploadFile", "ImportComponent_ng_container_30_ng_container_1_Template_p_button_click_17_listener", "_r5", "downloadFile", "id", "ɵɵtextInterpolate", "file_name", "ɵɵtextInterpolate1", "file_status", "ɵɵpipeBind2", "file_size", "createdAt", "updatedAt", "ɵɵtemplate", "ImportComponent_ng_container_30_ng_container_1_Template", "ImportComponent_ng_container_30_ng_container_2_Template", "ImportComponent_ng_container_31_ng_template_3_Template_button_click_17_listener", "log_r7", "_r6", "$implicit", "ImportComponent_ng_container_31_ng_template_3_Template_button_click_19_listener", "stopPropagation", "confirmRemove", "completed_count", "total_count", "success_count", "failed_count", "ImportComponent_ng_container_31_ng_template_2_Template", "ImportComponent_ng_container_31_ng_template_3_Template", "ImportComponent_ng_container_31_ng_template_4_Template", "log_data", "ImportComponent", "constructor", "route", "flexiblegroupuploadservice", "messageservice", "confirmationservice", "cdr", "items", "label", "icon", "routerLink", "subItems", "slug", "subId", "bitems", "home", "unsubscribe$", "intervalSubscription", "a<PERSON><PERSON><PERSON>", "fileurl", "exporturl", "table_name", "activeUploadItem", "uploadItems", "paramMapSubscription", "ngOnInit", "paramMap", "subscribe", "params", "get", "found", "find", "item", "toLowerCase", "length", "foundSub", "sub", "detectChanges", "initUpload", "fetchFilelog", "fetchProgresstatus", "startInterval", "stopInterval", "console", "log", "unsubscribe", "event", "file", "target", "files", "allowedTypes", "maxSize", "size", "includes", "type", "add", "severity", "detail", "formData", "FormData", "append", "name", "file_type", "save", "pipe", "next", "response", "status", "error", "getProgessStatus", "data", "Math", "round", "getFilelog", "confirm", "message", "header", "accept", "remove", "deleteurl", "documentId", "delete", "res", "refresh", "err", "tabname", "export", "then", "catch", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "ImportService", "i3", "MessageService", "ConfirmationService", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "ImportComponent_Template", "rf", "ctx", "ImportComponent_Template_p_tabMenu_activeItemChange_4_listener", "ImportComponent_p_tabMenu_5_Template", "ImportComponent_ng_container_25_Template", "ImportComponent_label_26_Template", "ImportComponent_div_27_Template", "ImportComponent_button_28_Template", "ImportComponent_Template_p_tabMenu_activeItemChange_29_listener", "ImportComponent_Template_p_tabMenu_click_29_listener", "ImportComponent_ng_container_30_Template", "ImportComponent_ng_container_31_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\import\\import.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\import\\import.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit } from '@angular/core';\r\nimport { ConfirmationService, MenuItem, MessageService } from 'primeng/api';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { Subject, Subscription, interval, takeUntil } from 'rxjs';\r\nimport { ImportService } from './import.service';\r\n\r\n@Component({\r\n  selector: 'app-import',\r\n  templateUrl: './import.component.html',\r\n  styleUrl: './import.component.scss',\r\n})\r\nexport class ImportComponent implements OnInit {\r\n  public items: MenuItem[] = [\r\n    {\r\n      label: 'Prospect',\r\n      icon: 'pi pi-list',\r\n      routerLink: ['/store/import', 'Prospect'],\r\n      subItems: [\r\n        { label: 'Sub1', slug: 'sub1', routerLink: ['/store/import', 'Prospect', 'sub1'] },\r\n        { label: 'Sub2', slug: 'sub2', routerLink: ['/store/import', 'Prospect', 'sub2'] },\r\n      ],\r\n    },\r\n    {\r\n      label: 'Account',\r\n      icon: 'pi pi-users',\r\n      routerLink: ['/store/import', 'Account'],\r\n      subItems: [\r\n        { label: 'Sub1', slug: 'sub1', routerLink: ['/store/import', 'Account', 'sub1'] },\r\n        { label: 'Sub2', slug: 'sub2', routerLink: ['/store/import', 'Account', 'sub2'] },\r\n      ],\r\n    },\r\n    {\r\n      label: 'Contact',\r\n      icon: 'pi pi-building',\r\n      routerLink: ['/store/import', 'Contact'],\r\n      subItems: [\r\n        { label: 'Sub1', slug: 'sub1', routerLink: ['/store/import', 'Contact', 'sub1'] },\r\n        { label: 'Sub2', slug: 'sub2', routerLink: ['/store/import', 'Contact', 'sub2'] },\r\n      ],\r\n    },\r\n    {\r\n      label: 'Activities',\r\n      icon: 'pi pi-briefcase',\r\n      routerLink: ['/store/import', 'Activities'],\r\n      subItems: [\r\n        { label: 'Sub1', slug: 'sub1', routerLink: ['/store/import', 'Activities', 'sub1'] },\r\n        { label: 'Sub2', slug: 'sub2', routerLink: ['/store/import', 'Activities', 'sub2'] },\r\n      ],\r\n    },\r\n    {\r\n      label: 'Opportunities',\r\n      icon: 'pi pi-briefcase',\r\n      routerLink: ['/store/import', 'Opportunities'],\r\n      subItems: [\r\n        { label: 'Sub1', slug: 'sub1', routerLink: ['/store/import', 'Opportunities', 'sub1'] },\r\n        { label: 'Sub2', slug: 'sub2', routerLink: ['/store/import', 'Opportunities', 'sub2'] },\r\n      ],\r\n    },\r\n  ];\r\n  public activeItem: MenuItem = {};\r\n  public activeSubItem: MenuItem = {};\r\n\r\n  public id: string = '';\r\n  public subId: string = '';\r\n\r\n  bitems: MenuItem[] | any = [\r\n    { label: 'Import', routerLink: ['/store/import'] },\r\n  ];\r\n  home: MenuItem | any = { icon: 'pi pi-home', routerLink: ['/'] };\r\n\r\n  private unsubscribe$ = new Subject<void>();\r\n  private intervalSubscription: Subscription | null = null;\r\n  public selectedFile: File | null = null;\r\n  public apiurl: string = ``;\r\n  public fileurl: string = ``;\r\n  public exporturl: string = ``;\r\n  public table_name: string = '';\r\n  public state_data: any = { progress: 10 };\r\n  public log_data: any[] = [];\r\n  public activeUploadItem: any = {};\r\n  public uploadItems: MenuItem[] = [\r\n    {\r\n      label: 'File Details',\r\n      icon: 'pi pi-file',\r\n      slug: 'file_details',\r\n    },\r\n    {\r\n      label: 'File Log',\r\n      icon: 'pi pi-file',\r\n      slug: 'file_log',\r\n    },\r\n  ];\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private flexiblegroupuploadservice: ImportService,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService,\r\n    private cdr: ChangeDetectorRef\r\n  ) { }\r\n\r\n  private paramMapSubscription: Subscription | null = null;\r\n\r\n  ngOnInit() {\r\n    this.paramMapSubscription = this.route.paramMap.subscribe(params => {\r\n      this.id = params.get('id') || '';\r\n      this.subId = params.get('sub-id') || '';\r\n      const found = this.items.find(item => item.label && item.label.toLowerCase() === this.id.toLowerCase());\r\n      this.activeItem = found || this.items[0];\r\n      const subItems = this.activeItem && this.activeItem['subItems'] ? this.activeItem['subItems'] : [];\r\n      if (subItems.length && this.subId) {\r\n        const foundSub = subItems.find((sub: any) => sub.slug && sub.slug.toLowerCase() === this.subId.toLowerCase());\r\n        this.activeSubItem = foundSub || subItems[0];\r\n      } else if (subItems.length) {\r\n        this.activeSubItem = subItems[0];\r\n      } else {\r\n        this.activeSubItem = {} as MenuItem;\r\n      }\r\n      this.cdr.detectChanges();\r\n      this.initUpload();\r\n    });\r\n  }\r\n\r\n  initUpload() {\r\n    this.activeUploadItem = this.uploadItems[0];\r\n    this.fetchFilelog();\r\n    this.fetchProgresstatus();\r\n  }\r\n\r\n  startInterval() {\r\n    if (!this.intervalSubscription) {\r\n      this.intervalSubscription = interval(5000).subscribe(() => {\r\n        this.fetchProgresstatus();\r\n      });\r\n    }\r\n  }\r\n\r\n  stopInterval() {\r\n    if (this.intervalSubscription) {\r\n      console.log('STOP INTERVAL');\r\n      this.intervalSubscription.unsubscribe(); // Unsubscribe directly\r\n      this.intervalSubscription = null; // Optionally reset the subscription\r\n    }\r\n  }\r\n\r\n  onFileSelect(event: any) {\r\n    const file = event.target.files[0];\r\n    const allowedTypes = [\r\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n      'text/csv',\r\n    ];\r\n    const maxSize = 1 * 1024 * 1024;\r\n    if (file && file.size <= maxSize && allowedTypes.includes(file.type)) {\r\n      this.selectedFile = file;\r\n    } else {\r\n      this.selectedFile = null;\r\n      this.messageservice.add({\r\n        severity: 'error',\r\n        detail:\r\n          'Please upload a file in one of the following formats: .csv,.xlsx, and ensure the file size is less than 1 MB.',\r\n      });\r\n    }\r\n  }\r\n\r\n  uploadFile() {\r\n    if (!this.selectedFile) return;\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', this.selectedFile);\r\n\r\n    this.state_data = {\r\n      ...this.state_data,\r\n      progress: 2,\r\n      file_name: this.selectedFile?.name,\r\n      file_size: this.selectedFile?.size,\r\n      file_status: 'IN_PROGRESS',\r\n      file_type: this.selectedFile?.type,\r\n    };\r\n\r\n    this.flexiblegroupuploadservice\r\n      .save(this.apiurl, formData)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (response.status === 'PROGRESS') {\r\n            this.selectedFile = null;\r\n            this.startInterval();\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('File upload error:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  fetchProgresstatus() {\r\n    this.flexiblegroupuploadservice\r\n      .getProgessStatus(this.fileurl, this.table_name)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (response?.data.length) {\r\n            const state_data = response?.data?.[0] || null;\r\n            if (state_data) {\r\n              state_data.progress = state_data?.total_count\r\n                ? Math.round(\r\n                  (state_data?.completed_count / state_data?.total_count) *\r\n                  100\r\n                )\r\n                : 2;\r\n            }\r\n            if (['DONE', 'FAILD'].includes(state_data.file_status)) {\r\n              this.stopInterval();\r\n              this.state_data = state_data;\r\n            } else {\r\n              this.startInterval();\r\n              this.state_data = state_data;\r\n            }\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching records:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  fetchFilelog() {\r\n    this.flexiblegroupuploadservice\r\n      .getFilelog(this.fileurl, this.table_name)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (response?.data) {\r\n            this.log_data = response?.data;\r\n          } else {\r\n            console.error('No Records Availble.');\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching records:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    const deleteurl = this.fileurl + '/' + item.documentId;\r\n    this.flexiblegroupuploadservice\r\n      .delete(deleteurl)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (res) => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.refresh();\r\n        },\r\n        error: (err) => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  downloadFile(id: any) {\r\n    const exporturl = this.exporturl + '/' + id;\r\n    const tabname = 'fg_relationship';\r\n    this.flexiblegroupuploadservice\r\n      .export(id, exporturl, tabname)\r\n      .then((response) => {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: 'File Downloaded Successfully!',\r\n        });\r\n      })\r\n      .catch((error) => {\r\n        this.messageservice.add({\r\n          severity: 'error',\r\n          detail: 'Error while processing your request.',\r\n        });\r\n      });\r\n  }\r\n\r\n  refresh() {\r\n    this.fetchFilelog();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.stopInterval();\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n    if (this.paramMapSubscription) {\r\n      this.paramMapSubscription.unsubscribe();\r\n      this.paramMapSubscription = null;\r\n    }\r\n  }\r\n\r\n}\r\n", "<p-toast position=\"top-right\" [life]=\"3000\"></p-toast>\r\n<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg surface-card\">\r\n  <div class=\"all-page-title-sec flex justify-content-between align-items-center mb-4\">\r\n    <p-breadcrumb [model]=\"bitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n  </div>\r\n  <p-tabMenu [model]=\"items\" [(activeItem)]=\"activeItem\" [styleClass]=\"\r\n      'flexible-tabs border-1 border-round border-50 overflow-hidden'\r\n    \"></p-tabMenu>\r\n  <p-tabMenu\r\n    *ngIf=\"activeItem['subItems']\"\r\n    [model]=\"activeItem['subItems']\"\r\n    [(activeItem)]=\"activeSubItem\"\r\n    [styleClass]=\"'flexible-tabs border-1 border-round border-50 overflow-hidden mb-3'\"\r\n  ></p-tabMenu>\r\n  <div class=\"tab-cnt px-4 pt-3\">\r\n    <div class=\"file-upload mb-5\">\r\n      <h5>Add File</h5>\r\n      <form>\r\n        <div class=\"grid\">\r\n          <div class=\"col-12 md:col-6\">\r\n            <div class=\"gap-2 border-1 border-round border-dashed border-100 p-2 h-full\">\r\n              <h6 class=\"p-2\">The excel file should list the flexible group relationship details in the following format:\r\n              </h6>\r\n              <p class=\"p-1\"><i class=\"pi pi-arrow-right\"></i> Template: <a href=\"assets/files/fg-relationship.xlsx\"\r\n                  style=\"text-decoration: underline;\">Download</a></p>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-12 md:col-6\">\r\n            <div\r\n              class=\"file-upload-box py-4 flex flex-column align-items-center justify-content-center gap-3 border-1 border-round border-dashed border-100\">\r\n              <i class=\"material-symbols-rounded text-primary text-7xl\">cloud_upload</i>\r\n              <ng-container *ngIf=\"state_data?.file_status === 'DONE' || !state_data?.file_status\">\r\n                <h4 class=\"m-0\">Upload Your File Here</h4>\r\n                <p class=\"m-0\">File Supported: CSV,XLSX</p>\r\n                <p class=\"m-0\">Maximum File Size:1 MB</p>\r\n              </ng-container>\r\n              <label *ngIf=\"\r\n                !selectedFile &&\r\n                (state_data?.file_status === 'DONE' || !state_data?.file_status)\r\n              \" for=\"file-upload\"\r\n                class=\"p-element p-ripple p-button p-button-outlined p-component w-9rem justify-content-center font-semibold gap-2\">\r\n                <i class=\"material-symbols-rounded\">add</i> Add File\r\n                <input type=\"file\" name=\"file\" (change)=\"onFileSelect($event)\" accept=\".csv,.xlsx\" id=\"file-upload\"\r\n                  style=\"display: none\" [disabled]=\"selectedFile\" />\r\n              </label>\r\n              <div class=\"w-10rem\" *ngIf=\"state_data?.file_status === 'IN_PROGRESS'\">\r\n                <p-progressBar [value]=\"state_data?.progress\" [showValue]=\"true\"\r\n                  [style]=\"{ height: '30px' }\"></p-progressBar>\r\n              </div>\r\n              <button *ngIf=\"selectedFile\" (click)=\"uploadFile()\" label=\"Upload\" pButton type=\"button\"\r\n                class=\"p-button-lg\"></button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n  \r\n      </form>\r\n    </div>\r\n    <p-tabMenu [model]=\"uploadItems\" [(activeItem)]=\"activeItem\" (click)=\"refresh()\" [styleClass]=\"\r\n        'flexible-tabs border-1 border-round border-50 overflow-hidden mb-3'\r\n      \"></p-tabMenu>\r\n    <ng-container *ngIf=\"activeUploadItem?.slug === 'file_details'\">\r\n      <ng-container *ngIf=\"state_data?.file_status\">\r\n        <div class=\"file-details\">\r\n          <ul class=\"m-0 p-4 list-none flex flex-column gap-4 surface-50 border-round\">\r\n            <li class=\"flex align-items-center gap-3\">\r\n              <span class=\"flex w-9rem font-semibold\">File Name</span>:\r\n              <span>{{ state_data?.file_name }}</span>\r\n            </li>\r\n            <li class=\"flex align-items-center gap-3\">\r\n              <span class=\"flex w-9rem font-semibold\">Status</span>:\r\n              <span class=\"flex align-items-center gap-2 text-green-400\">\r\n                {{ state_data?.file_status }}\r\n                <i class=\"material-symbols-rounded\">check_circle</i>\r\n                <p-button [rounded]=\"true\" (click)=\"downloadFile(state_data?.id)\" class=\"ml-auto\"\r\n                  [styleClass]=\"'p-button-icon-only'\" pTooltip=\"Export\">\r\n                  <i class=\"material-symbols-rounded text-2xl\">download</i>\r\n                </p-button>\r\n              </span>\r\n            </li>\r\n            <li class=\"flex align-items-center gap-3\">\r\n              <span class=\"flex w-9rem font-semibold\">Size</span>:\r\n              <span>{{ state_data?.file_size / 1024 | number : \"1.0-2\" }} KB</span>\r\n            </li>\r\n            <li class=\"flex align-items-center gap-3\">\r\n              <span class=\"flex w-9rem font-semibold\">Creator</span>:\r\n              <span>-</span>\r\n            </li>\r\n            <li class=\"flex align-items-center gap-3\">\r\n              <span class=\"flex w-9rem font-semibold\">Created at</span>:\r\n              <span>{{ state_data?.createdAt | date : \"dd/MM/yyyy\" }}</span>\r\n            </li>\r\n            <li class=\"flex align-items-center gap-3\">\r\n              <span class=\"flex w-9rem font-semibold\">Last Modified</span>:\r\n              <span>{{ state_data?.updatedAt | date : \"dd/MM/yyyy\" }}</span>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </ng-container>\r\n      <ng-container *ngIf=\"!state_data?.file_status\">\r\n        No records found.\r\n      </ng-container>\r\n    </ng-container>\r\n    <ng-container *ngIf=\"activeUploadItem?.slug === 'file_log'\">\r\n      <p-table [value]=\"log_data\" [paginator]=\"false\" [rows]=\"5\" [sortMode]=\"'multiple'\">\r\n        <ng-template pTemplate=\"header\">\r\n          <tr>\r\n            <th>File Name</th>\r\n            <th>Progress</th>\r\n            <th>Total</th>\r\n            <th>Success</th>\r\n            <th>Failed</th>\r\n            <th>Created Date</th>\r\n            <th>Status</th>\r\n            <th>Summary</th>\r\n            <th>Remove</th>\r\n          </tr>\r\n        </ng-template>\r\n        <ng-template pTemplate=\"body\" let-log>\r\n          <tr>\r\n            <td>{{ log?.file_name }}</td>\r\n            <td>{{ (log?.completed_count / log?.total_count) * 100 }}%</td>\r\n            <td>{{ log?.total_count }}</td>\r\n            <td>{{ log?.success_count }}</td>\r\n            <td>{{ log?.failed_count }}</td>\r\n            <td>{{ log?.createdAt | date : \"dd/MM/yyyy\" }}</td>\r\n            <td>{{ log?.file_status }}</td>\r\n            <td>\r\n              <button pButton type=\"button\" icon=\"pi pi-cloud-download\" class=\"p-button-sm p-button-primary\"\r\n                (click)=\"downloadFile(log?.id)\" pTooltip=\"Export\"></button>\r\n            </td>\r\n            <td>\r\n              <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                (click)=\"$event.stopPropagation(); confirmRemove(log)\"></button>\r\n            </td>\r\n          </tr>\r\n        </ng-template>\r\n        <ng-template pTemplate=\"emptymessage\">\r\n          <tr>\r\n            <td colspan=\"8\">No records found.</td>\r\n          </tr>\r\n        </ng-template>\r\n      </p-table>\r\n    </ng-container>\r\n  </div>\r\n  <p-confirmDialog></p-confirmDialog>\r\n</div>"], "mappings": "AAGA,SAASA,OAAO,EAAgBC,QAAQ,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;ICK/DC,EAAA,CAAAC,cAAA,mBAKC;IAFCD,EAAA,CAAAE,gBAAA,8BAAAC,2EAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAG,aAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,aAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA8B;IAE/BJ,EAAA,CAAAY,YAAA,EAAY;;;;IAHXZ,EAAA,CAAAa,UAAA,UAAAN,MAAA,CAAAO,UAAA,aAAgC;IAChCd,EAAA,CAAAe,gBAAA,eAAAR,MAAA,CAAAG,aAAA,CAA8B;IAC9BV,EAAA,CAAAa,UAAA,oFAAmF;;;;;IAmBzEb,EAAA,CAAAgB,uBAAA,GAAqF;IACnFhB,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAiB,MAAA,4BAAqB;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IAC1CZ,EAAA,CAAAC,cAAA,YAAe;IAAAD,EAAA,CAAAiB,MAAA,+BAAwB;IAAAjB,EAAA,CAAAY,YAAA,EAAI;IAC3CZ,EAAA,CAAAC,cAAA,YAAe;IAAAD,EAAA,CAAAiB,MAAA,6BAAsB;IAAAjB,EAAA,CAAAY,YAAA,EAAI;;;;;;;IAOzCZ,EALF,CAAAC,cAAA,gBAIsH,YAChF;IAAAD,EAAA,CAAAiB,MAAA,UAAG;IAAAjB,EAAA,CAAAY,YAAA,EAAI;IAACZ,EAAA,CAAAiB,MAAA,iBAC5C;IAAAjB,EAAA,CAAAC,cAAA,gBACoD;IADrBD,EAAA,CAAAkB,UAAA,oBAAAC,0DAAAf,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAUJ,MAAA,CAAAc,YAAA,CAAAjB,MAAA,CAAoB;IAAA,EAAC;IAEhEJ,EAFE,CAAAY,YAAA,EACoD,EAC9C;;;;IADkBZ,EAAA,CAAAsB,SAAA,GAAyB;IAAzBtB,EAAA,CAAAa,UAAA,aAAAN,MAAA,CAAAgB,YAAA,CAAyB;;;;;IAEnDvB,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAwB,SAAA,wBAC+C;IACjDxB,EAAA,CAAAY,YAAA,EAAM;;;;IADFZ,EAAA,CAAAsB,SAAA,EAA4B;IAA5BtB,EAAA,CAAAyB,UAAA,CAAAzB,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAA4B;IADgB3B,EAA/B,CAAAa,UAAA,UAAAN,MAAA,CAAAqB,UAAA,kBAAArB,MAAA,CAAAqB,UAAA,CAAAC,QAAA,CAA8B,mBAAmB;;;;;;IAGlE7B,EAAA,CAAAC,cAAA,iBACsB;IADOD,EAAA,CAAAkB,UAAA,mBAAAY,2DAAA;MAAA9B,EAAA,CAAAK,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAAyB,UAAA,EAAY;IAAA,EAAC;IAC7BhC,EAAA,CAAAY,YAAA,EAAS;;;;;;IAWvCZ,EAAA,CAAAgB,uBAAA,GAA8C;IAItChB,EAHN,CAAAC,cAAA,cAA0B,aACqD,aACjC,eACA;IAAAD,EAAA,CAAAiB,MAAA,gBAAS;IAAAjB,EAAA,CAAAY,YAAA,EAAO;IAAAZ,EAAA,CAAAiB,MAAA,SACxD;IAAAjB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAiB,MAAA,GAA2B;IACnCjB,EADmC,CAAAY,YAAA,EAAO,EACrC;IAEHZ,EADF,CAAAC,cAAA,aAA0C,gBACA;IAAAD,EAAA,CAAAiB,MAAA,cAAM;IAAAjB,EAAA,CAAAY,YAAA,EAAO;IAAAZ,EAAA,CAAAiB,MAAA,UACrD;IAAAjB,EAAA,CAAAC,cAAA,gBAA2D;IACzDD,EAAA,CAAAiB,MAAA,IACA;IAAAjB,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAiB,MAAA,oBAAY;IAAAjB,EAAA,CAAAY,YAAA,EAAI;IACpDZ,EAAA,CAAAC,cAAA,oBACwD;IAD7BD,EAAA,CAAAkB,UAAA,mBAAAe,mFAAA;MAAAjC,EAAA,CAAAK,aAAA,CAAA6B,GAAA;MAAA,MAAA3B,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAA4B,YAAA,CAAA5B,MAAA,CAAAqB,UAAA,kBAAArB,MAAA,CAAAqB,UAAA,CAAAQ,EAAA,CAA4B;IAAA,EAAC;IAE/DpC,EAAA,CAAAC,cAAA,aAA6C;IAAAD,EAAA,CAAAiB,MAAA,gBAAQ;IAG3DjB,EAH2D,CAAAY,YAAA,EAAI,EAChD,EACN,EACJ;IAEHZ,EADF,CAAAC,cAAA,cAA0C,gBACA;IAAAD,EAAA,CAAAiB,MAAA,YAAI;IAAAjB,EAAA,CAAAY,YAAA,EAAO;IAAAZ,EAAA,CAAAiB,MAAA,UACnD;IAAAjB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAiB,MAAA,IAAwD;;IAChEjB,EADgE,CAAAY,YAAA,EAAO,EAClE;IAEHZ,EADF,CAAAC,cAAA,cAA0C,gBACA;IAAAD,EAAA,CAAAiB,MAAA,eAAO;IAAAjB,EAAA,CAAAY,YAAA,EAAO;IAAAZ,EAAA,CAAAiB,MAAA,UACtD;IAAAjB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAiB,MAAA,SAAC;IACTjB,EADS,CAAAY,YAAA,EAAO,EACX;IAEHZ,EADF,CAAAC,cAAA,cAA0C,gBACA;IAAAD,EAAA,CAAAiB,MAAA,kBAAU;IAAAjB,EAAA,CAAAY,YAAA,EAAO;IAAAZ,EAAA,CAAAiB,MAAA,UACzD;IAAAjB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAiB,MAAA,IAAiD;;IACzDjB,EADyD,CAAAY,YAAA,EAAO,EAC3D;IAEHZ,EADF,CAAAC,cAAA,cAA0C,gBACA;IAAAD,EAAA,CAAAiB,MAAA,qBAAa;IAAAjB,EAAA,CAAAY,YAAA,EAAO;IAAAZ,EAAA,CAAAiB,MAAA,UAC5D;IAAAjB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAiB,MAAA,IAAiD;;IAG7DjB,EAH6D,CAAAY,YAAA,EAAO,EAC3D,EACF,EACD;;;;;IA9BMZ,EAAA,CAAAsB,SAAA,GAA2B;IAA3BtB,EAAA,CAAAqC,iBAAA,CAAA9B,MAAA,CAAAqB,UAAA,kBAAArB,MAAA,CAAAqB,UAAA,CAAAU,SAAA,CAA2B;IAK/BtC,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuC,kBAAA,MAAAhC,MAAA,CAAAqB,UAAA,kBAAArB,MAAA,CAAAqB,UAAA,CAAAY,WAAA,MACA;IACUxC,EAAA,CAAAsB,SAAA,GAAgB;IACxBtB,EADQ,CAAAa,UAAA,iBAAgB,oCACW;IAOjCb,EAAA,CAAAsB,SAAA,GAAwD;IAAxDtB,EAAA,CAAAuC,kBAAA,KAAAvC,EAAA,CAAAyC,WAAA,SAAAlC,MAAA,CAAAqB,UAAA,kBAAArB,MAAA,CAAAqB,UAAA,CAAAc,SAAA,0BAAwD;IAQxD1C,EAAA,CAAAsB,SAAA,IAAiD;IAAjDtB,EAAA,CAAAqC,iBAAA,CAAArC,EAAA,CAAAyC,WAAA,SAAAlC,MAAA,CAAAqB,UAAA,kBAAArB,MAAA,CAAAqB,UAAA,CAAAe,SAAA,gBAAiD;IAIjD3C,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAqC,iBAAA,CAAArC,EAAA,CAAAyC,WAAA,SAAAlC,MAAA,CAAAqB,UAAA,kBAAArB,MAAA,CAAAqB,UAAA,CAAAgB,SAAA,gBAAiD;;;;;IAK/D5C,EAAA,CAAAgB,uBAAA,GAA+C;IAC7ChB,EAAA,CAAAiB,MAAA,0BACF;;;;;;IAxCFjB,EAAA,CAAAgB,uBAAA,GAAgE;IAsC9DhB,EArCA,CAAA6C,UAAA,IAAAC,uDAAA,6BAA8C,IAAAC,uDAAA,2BAqCC;;;;;IArChC/C,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAa,UAAA,SAAAN,MAAA,CAAAqB,UAAA,kBAAArB,MAAA,CAAAqB,UAAA,CAAAY,WAAA,CAA6B;IAqC7BxC,EAAA,CAAAsB,SAAA,EAA8B;IAA9BtB,EAAA,CAAAa,UAAA,WAAAN,MAAA,CAAAqB,UAAA,kBAAArB,MAAA,CAAAqB,UAAA,CAAAY,WAAA,EAA8B;;;;;IAQvCxC,EADF,CAAAC,cAAA,SAAI,SACE;IAAAD,EAAA,CAAAiB,MAAA,gBAAS;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IAClBZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAiB,MAAA,eAAQ;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IACjBZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAiB,MAAA,YAAK;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IACdZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAiB,MAAA,cAAO;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IAChBZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAiB,MAAA,cAAM;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IACfZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAiB,MAAA,oBAAY;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IACrBZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAiB,MAAA,cAAM;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IACfZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAiB,MAAA,eAAO;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IAChBZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAiB,MAAA,cAAM;IACZjB,EADY,CAAAY,YAAA,EAAK,EACZ;;;;;;IAIHZ,EADF,CAAAC,cAAA,SAAI,SACE;IAAAD,EAAA,CAAAiB,MAAA,GAAoB;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IAC7BZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAiB,MAAA,GAAsD;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IAC/DZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAiB,MAAA,GAAsB;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IAC/BZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAiB,MAAA,GAAwB;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IACjCZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAiB,MAAA,IAAuB;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IAChCZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAiB,MAAA,IAA0C;;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IACnDZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAiB,MAAA,IAAsB;IAAAjB,EAAA,CAAAY,YAAA,EAAK;IAE7BZ,EADF,CAAAC,cAAA,UAAI,kBAEkD;IAAlDD,EAAA,CAAAkB,UAAA,mBAAA8B,gFAAA;MAAA,MAAAC,MAAA,GAAAjD,EAAA,CAAAK,aAAA,CAAA6C,GAAA,EAAAC,SAAA;MAAA,MAAA5C,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAA4B,YAAA,CAAAc,MAAA,kBAAAA,MAAA,CAAAb,EAAA,CAAqB;IAAA,EAAC;IACnCpC,EADsD,CAAAY,YAAA,EAAS,EAC1D;IAEHZ,EADF,CAAAC,cAAA,UAAI,kBAEuD;IAAvDD,EAAA,CAAAkB,UAAA,mBAAAkC,gFAAAhD,MAAA;MAAA,MAAA6C,MAAA,GAAAjD,EAAA,CAAAK,aAAA,CAAA6C,GAAA,EAAAC,SAAA;MAAA,MAAA5C,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAASJ,MAAA,CAAAiD,eAAA,EAAwB;MAAA,OAAArD,EAAA,CAAAW,WAAA,CAAEJ,MAAA,CAAA+C,aAAA,CAAAL,MAAA,CAAkB;IAAA,EAAC;IAE5DjD,EAF6D,CAAAY,YAAA,EAAS,EAC/D,EACF;;;;IAfCZ,EAAA,CAAAsB,SAAA,GAAoB;IAApBtB,EAAA,CAAAqC,iBAAA,CAAAY,MAAA,kBAAAA,MAAA,CAAAX,SAAA,CAAoB;IACpBtC,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAuC,kBAAA,MAAAU,MAAA,kBAAAA,MAAA,CAAAM,eAAA,KAAAN,MAAA,kBAAAA,MAAA,CAAAO,WAAA,aAAsD;IACtDxD,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAqC,iBAAA,CAAAY,MAAA,kBAAAA,MAAA,CAAAO,WAAA,CAAsB;IACtBxD,EAAA,CAAAsB,SAAA,GAAwB;IAAxBtB,EAAA,CAAAqC,iBAAA,CAAAY,MAAA,kBAAAA,MAAA,CAAAQ,aAAA,CAAwB;IACxBzD,EAAA,CAAAsB,SAAA,GAAuB;IAAvBtB,EAAA,CAAAqC,iBAAA,CAAAY,MAAA,kBAAAA,MAAA,CAAAS,YAAA,CAAuB;IACvB1D,EAAA,CAAAsB,SAAA,GAA0C;IAA1CtB,EAAA,CAAAqC,iBAAA,CAAArC,EAAA,CAAAyC,WAAA,QAAAQ,MAAA,kBAAAA,MAAA,CAAAN,SAAA,gBAA0C;IAC1C3C,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAqC,iBAAA,CAAAY,MAAA,kBAAAA,MAAA,CAAAT,WAAA,CAAsB;;;;;IAa1BxC,EADF,CAAAC,cAAA,SAAI,aACc;IAAAD,EAAA,CAAAiB,MAAA,wBAAiB;IACnCjB,EADmC,CAAAY,YAAA,EAAK,EACnC;;;;;IArCXZ,EAAA,CAAAgB,uBAAA,GAA4D;IAC1DhB,EAAA,CAAAC,cAAA,kBAAmF;IAiCjFD,EAhCA,CAAA6C,UAAA,IAAAc,sDAAA,2BAAgC,IAAAC,sDAAA,4BAaM,IAAAC,sDAAA,0BAmBA;IAKxC7D,EAAA,CAAAY,YAAA,EAAU;;;;;IAtCDZ,EAAA,CAAAsB,SAAA,EAAkB;IAAgCtB,EAAlD,CAAAa,UAAA,UAAAN,MAAA,CAAAuD,QAAA,CAAkB,oBAAoB,WAAW,wBAAwB;;;AD5FxF,OAAM,MAAOC,eAAe;EAkF1BC,YACUC,KAAqB,EACrBC,0BAAyC,EACzCC,cAA8B,EAC9BC,mBAAwC,EACxCC,GAAsB;IAJtB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,0BAA0B,GAA1BA,0BAA0B;IAC1B,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,GAAG,GAAHA,GAAG;IAtFN,KAAAC,KAAK,GAAe,CACzB;MACEC,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAE,YAAY;MAClBC,UAAU,EAAE,CAAC,eAAe,EAAE,UAAU,CAAC;MACzCC,QAAQ,EAAE,CACR;QAAEH,KAAK,EAAE,MAAM;QAAEI,IAAI,EAAE,MAAM;QAAEF,UAAU,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,MAAM;MAAC,CAAE,EAClF;QAAEF,KAAK,EAAE,MAAM;QAAEI,IAAI,EAAE,MAAM;QAAEF,UAAU,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,MAAM;MAAC,CAAE;KAErF,EACD;MACEF,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,aAAa;MACnBC,UAAU,EAAE,CAAC,eAAe,EAAE,SAAS,CAAC;MACxCC,QAAQ,EAAE,CACR;QAAEH,KAAK,EAAE,MAAM;QAAEI,IAAI,EAAE,MAAM;QAAEF,UAAU,EAAE,CAAC,eAAe,EAAE,SAAS,EAAE,MAAM;MAAC,CAAE,EACjF;QAAEF,KAAK,EAAE,MAAM;QAAEI,IAAI,EAAE,MAAM;QAAEF,UAAU,EAAE,CAAC,eAAe,EAAE,SAAS,EAAE,MAAM;MAAC,CAAE;KAEpF,EACD;MACEF,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,gBAAgB;MACtBC,UAAU,EAAE,CAAC,eAAe,EAAE,SAAS,CAAC;MACxCC,QAAQ,EAAE,CACR;QAAEH,KAAK,EAAE,MAAM;QAAEI,IAAI,EAAE,MAAM;QAAEF,UAAU,EAAE,CAAC,eAAe,EAAE,SAAS,EAAE,MAAM;MAAC,CAAE,EACjF;QAAEF,KAAK,EAAE,MAAM;QAAEI,IAAI,EAAE,MAAM;QAAEF,UAAU,EAAE,CAAC,eAAe,EAAE,SAAS,EAAE,MAAM;MAAC,CAAE;KAEpF,EACD;MACEF,KAAK,EAAE,YAAY;MACnBC,IAAI,EAAE,iBAAiB;MACvBC,UAAU,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC;MAC3CC,QAAQ,EAAE,CACR;QAAEH,KAAK,EAAE,MAAM;QAAEI,IAAI,EAAE,MAAM;QAAEF,UAAU,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,MAAM;MAAC,CAAE,EACpF;QAAEF,KAAK,EAAE,MAAM;QAAEI,IAAI,EAAE,MAAM;QAAEF,UAAU,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,MAAM;MAAC,CAAE;KAEvF,EACD;MACEF,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,iBAAiB;MACvBC,UAAU,EAAE,CAAC,eAAe,EAAE,eAAe,CAAC;MAC9CC,QAAQ,EAAE,CACR;QAAEH,KAAK,EAAE,MAAM;QAAEI,IAAI,EAAE,MAAM;QAAEF,UAAU,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,MAAM;MAAC,CAAE,EACvF;QAAEF,KAAK,EAAE,MAAM;QAAEI,IAAI,EAAE,MAAM;QAAEF,UAAU,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,MAAM;MAAC,CAAE;KAE1F,CACF;IACM,KAAA3D,UAAU,GAAa,EAAE;IACzB,KAAAJ,aAAa,GAAa,EAAE;IAE5B,KAAA0B,EAAE,GAAW,EAAE;IACf,KAAAwC,KAAK,GAAW,EAAE;IAEzB,KAAAC,MAAM,GAAqB,CACzB;MAAEN,KAAK,EAAE,QAAQ;MAAEE,UAAU,EAAE,CAAC,eAAe;IAAC,CAAE,CACnD;IACD,KAAAK,IAAI,GAAmB;MAAEN,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAExD,KAAAM,YAAY,GAAG,IAAIlF,OAAO,EAAQ;IAClC,KAAAmF,oBAAoB,GAAwB,IAAI;IACjD,KAAAzD,YAAY,GAAgB,IAAI;IAChC,KAAA0D,MAAM,GAAW,EAAE;IACnB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAxD,UAAU,GAAQ;MAAEC,QAAQ,EAAE;IAAE,CAAE;IAClC,KAAAiC,QAAQ,GAAU,EAAE;IACpB,KAAAuB,gBAAgB,GAAQ,EAAE;IAC1B,KAAAC,WAAW,GAAe,CAC/B;MACEf,KAAK,EAAE,cAAc;MACrBC,IAAI,EAAE,YAAY;MAClBG,IAAI,EAAE;KACP,EACD;MACEJ,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAE,YAAY;MAClBG,IAAI,EAAE;KACP,CACF;IAUO,KAAAY,oBAAoB,GAAwB,IAAI;EAFpD;EAIJC,QAAQA,CAAA;IACN,IAAI,CAACD,oBAAoB,GAAG,IAAI,CAACtB,KAAK,CAACwB,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACjE,IAAI,CAACvD,EAAE,GAAGuD,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;MAChC,IAAI,CAAChB,KAAK,GAAGe,MAAM,CAACC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;MACvC,MAAMC,KAAK,GAAG,IAAI,CAACvB,KAAK,CAACwB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACxB,KAAK,IAAIwB,IAAI,CAACxB,KAAK,CAACyB,WAAW,EAAE,KAAK,IAAI,CAAC5D,EAAE,CAAC4D,WAAW,EAAE,CAAC;MACvG,IAAI,CAAClF,UAAU,GAAG+E,KAAK,IAAI,IAAI,CAACvB,KAAK,CAAC,CAAC,CAAC;MACxC,MAAMI,QAAQ,GAAG,IAAI,CAAC5D,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC,UAAU,CAAC,GAAG,IAAI,CAACA,UAAU,CAAC,UAAU,CAAC,GAAG,EAAE;MAClG,IAAI4D,QAAQ,CAACuB,MAAM,IAAI,IAAI,CAACrB,KAAK,EAAE;QACjC,MAAMsB,QAAQ,GAAGxB,QAAQ,CAACoB,IAAI,CAAEK,GAAQ,IAAKA,GAAG,CAACxB,IAAI,IAAIwB,GAAG,CAACxB,IAAI,CAACqB,WAAW,EAAE,KAAK,IAAI,CAACpB,KAAK,CAACoB,WAAW,EAAE,CAAC;QAC7G,IAAI,CAACtF,aAAa,GAAGwF,QAAQ,IAAIxB,QAAQ,CAAC,CAAC,CAAC;MAC9C,CAAC,MAAM,IAAIA,QAAQ,CAACuB,MAAM,EAAE;QAC1B,IAAI,CAACvF,aAAa,GAAGgE,QAAQ,CAAC,CAAC,CAAC;MAClC,CAAC,MAAM;QACL,IAAI,CAAChE,aAAa,GAAG,EAAc;MACrC;MACA,IAAI,CAAC2D,GAAG,CAAC+B,aAAa,EAAE;MACxB,IAAI,CAACC,UAAU,EAAE;IACnB,CAAC,CAAC;EACJ;EAEAA,UAAUA,CAAA;IACR,IAAI,CAAChB,gBAAgB,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACgB,YAAY,EAAE;IACnB,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACxB,oBAAoB,EAAE;MAC9B,IAAI,CAACA,oBAAoB,GAAGlF,QAAQ,CAAC,IAAI,CAAC,CAAC4F,SAAS,CAAC,MAAK;QACxD,IAAI,CAACa,kBAAkB,EAAE;MAC3B,CAAC,CAAC;IACJ;EACF;EAEAE,YAAYA,CAAA;IACV,IAAI,IAAI,CAACzB,oBAAoB,EAAE;MAC7B0B,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B,IAAI,CAAC3B,oBAAoB,CAAC4B,WAAW,EAAE,CAAC,CAAC;MACzC,IAAI,CAAC5B,oBAAoB,GAAG,IAAI,CAAC,CAAC;IACpC;EACF;EAEA3D,YAAYA,CAACwF,KAAU;IACrB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,MAAMC,YAAY,GAAG,CACnB,mEAAmE,EACnE,UAAU,CACX;IACD,MAAMC,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;IAC/B,IAAIJ,IAAI,IAAIA,IAAI,CAACK,IAAI,IAAID,OAAO,IAAID,YAAY,CAACG,QAAQ,CAACN,IAAI,CAACO,IAAI,CAAC,EAAE;MACpE,IAAI,CAAC9F,YAAY,GAAGuF,IAAI;IAC1B,CAAC,MAAM;MACL,IAAI,CAACvF,YAAY,GAAG,IAAI;MACxB,IAAI,CAAC4C,cAAc,CAACmD,GAAG,CAAC;QACtBC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EACJ;OACH,CAAC;IACJ;EACF;EAEAxF,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACT,YAAY,EAAE;IAExB,MAAMkG,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAI,CAACpG,YAAY,CAAC;IAE1C,IAAI,CAACK,UAAU,GAAG;MAChB,GAAG,IAAI,CAACA,UAAU;MAClBC,QAAQ,EAAE,CAAC;MACXS,SAAS,EAAE,IAAI,CAACf,YAAY,EAAEqG,IAAI;MAClClF,SAAS,EAAE,IAAI,CAACnB,YAAY,EAAE4F,IAAI;MAClC3E,WAAW,EAAE,aAAa;MAC1BqF,SAAS,EAAE,IAAI,CAACtG,YAAY,EAAE8F;KAC/B;IAED,IAAI,CAACnD,0BAA0B,CAC5B4D,IAAI,CAAC,IAAI,CAAC7C,MAAM,EAAEwC,QAAQ,CAAC,CAC3BM,IAAI,CAAChI,SAAS,CAAC,IAAI,CAACgF,YAAY,CAAC,CAAC,CAClCW,SAAS,CAAC;MACTsC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,MAAM,KAAK,UAAU,EAAE;UAClC,IAAI,CAAC3G,YAAY,GAAG,IAAI;UACxB,IAAI,CAACiF,aAAa,EAAE;QACtB;MACF,CAAC;MACD2B,KAAK,EAAGA,KAAU,IAAI;QACpBzB,OAAO,CAACyB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;KACD,CAAC;EACN;EAEA5B,kBAAkBA,CAAA;IAChB,IAAI,CAACrC,0BAA0B,CAC5BkE,gBAAgB,CAAC,IAAI,CAAClD,OAAO,EAAE,IAAI,CAACE,UAAU,CAAC,CAC/C2C,IAAI,CAAChI,SAAS,CAAC,IAAI,CAACgF,YAAY,CAAC,CAAC,CAClCW,SAAS,CAAC;MACTsC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAEI,IAAI,CAACpC,MAAM,EAAE;UACzB,MAAMrE,UAAU,GAAGqG,QAAQ,EAAEI,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI;UAC9C,IAAIzG,UAAU,EAAE;YACdA,UAAU,CAACC,QAAQ,GAAGD,UAAU,EAAE4B,WAAW,GACzC8E,IAAI,CAACC,KAAK,CACT3G,UAAU,EAAE2B,eAAe,GAAG3B,UAAU,EAAE4B,WAAW,GACtD,GAAG,CACJ,GACC,CAAC;UACP;UACA,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC4D,QAAQ,CAACxF,UAAU,CAACY,WAAW,CAAC,EAAE;YACtD,IAAI,CAACiE,YAAY,EAAE;YACnB,IAAI,CAAC7E,UAAU,GAAGA,UAAU;UAC9B,CAAC,MAAM;YACL,IAAI,CAAC4E,aAAa,EAAE;YACpB,IAAI,CAAC5E,UAAU,GAAGA,UAAU;UAC9B;QACF;MACF,CAAC;MACDuG,KAAK,EAAGA,KAAU,IAAI;QACpBzB,OAAO,CAACyB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;KACD,CAAC;EACN;EAEA7B,YAAYA,CAAA;IACV,IAAI,CAACpC,0BAA0B,CAC5BsE,UAAU,CAAC,IAAI,CAACtD,OAAO,EAAE,IAAI,CAACE,UAAU,CAAC,CACzC2C,IAAI,CAAChI,SAAS,CAAC,IAAI,CAACgF,YAAY,CAAC,CAAC,CAClCW,SAAS,CAAC;MACTsC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAEI,IAAI,EAAE;UAClB,IAAI,CAACvE,QAAQ,GAAGmE,QAAQ,EAAEI,IAAI;QAChC,CAAC,MAAM;UACL3B,OAAO,CAACyB,KAAK,CAAC,sBAAsB,CAAC;QACvC;MACF,CAAC;MACDA,KAAK,EAAGA,KAAU,IAAI;QACpBzB,OAAO,CAACyB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;KACD,CAAC;EACN;EAEA7E,aAAaA,CAACyC,IAAS;IACrB,IAAI,CAAC3B,mBAAmB,CAACqE,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChEC,MAAM,EAAE,SAAS;MACjBnE,IAAI,EAAE,4BAA4B;MAClCoE,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAAC9C,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEA8C,MAAMA,CAAC9C,IAAS;IACd,MAAM+C,SAAS,GAAG,IAAI,CAAC5D,OAAO,GAAG,GAAG,GAAGa,IAAI,CAACgD,UAAU;IACtD,IAAI,CAAC7E,0BAA0B,CAC5B8E,MAAM,CAACF,SAAS,CAAC,CACjBf,IAAI,CAAChI,SAAS,CAAC,IAAI,CAACgF,YAAY,CAAC,CAAC,CAClCW,SAAS,CAAC;MACTsC,IAAI,EAAGiB,GAAG,IAAI;QACZ,IAAI,CAAC9E,cAAc,CAACmD,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAC0B,OAAO,EAAE;MAChB,CAAC;MACDf,KAAK,EAAGgB,GAAG,IAAI;QACb,IAAI,CAAChF,cAAc,CAACmD,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEArF,YAAYA,CAACC,EAAO;IAClB,MAAM+C,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,GAAG,GAAG/C,EAAE;IAC3C,MAAMgH,OAAO,GAAG,iBAAiB;IACjC,IAAI,CAAClF,0BAA0B,CAC5BmF,MAAM,CAACjH,EAAE,EAAE+C,SAAS,EAAEiE,OAAO,CAAC,CAC9BE,IAAI,CAAErB,QAAQ,IAAI;MACjB,IAAI,CAAC9D,cAAc,CAACmD,GAAG,CAAC;QACtBC,QAAQ,EAAE,SAAS;QACnBC,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,CAAC,CACD+B,KAAK,CAAEpB,KAAK,IAAI;MACf,IAAI,CAAChE,cAAc,CAACmD,GAAG,CAAC;QACtBC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,CAAC;EACN;EAEA0B,OAAOA,CAAA;IACL,IAAI,CAAC5C,YAAY,EAAE;EACrB;EAEAkD,WAAWA,CAAA;IACT,IAAI,CAAC/C,YAAY,EAAE;IACnB,IAAI,CAAC1B,YAAY,CAACiD,IAAI,EAAE;IACxB,IAAI,CAACjD,YAAY,CAAC0E,QAAQ,EAAE;IAC5B,IAAI,IAAI,CAAClE,oBAAoB,EAAE;MAC7B,IAAI,CAACA,oBAAoB,CAACqB,WAAW,EAAE;MACvC,IAAI,CAACrB,oBAAoB,GAAG,IAAI;IAClC;EACF;;;uBAzSWxB,eAAe,EAAA/D,EAAA,CAAA0J,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5J,EAAA,CAAA0J,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAA9J,EAAA,CAAA0J,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAhK,EAAA,CAAA0J,iBAAA,CAAAK,EAAA,CAAAE,mBAAA,GAAAjK,EAAA,CAAA0J,iBAAA,CAAA1J,EAAA,CAAAkK,iBAAA;IAAA;EAAA;;;YAAfnG,eAAe;MAAAoG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX5BzK,EAAA,CAAAwB,SAAA,iBAAsD;UAEpDxB,EADF,CAAAC,cAAA,aAA2E,aACY;UACnFD,EAAA,CAAAwB,SAAA,sBAAsF;UACxFxB,EAAA,CAAAY,YAAA,EAAM;UACNZ,EAAA,CAAAC,cAAA,mBAEI;UAFuBD,EAAA,CAAAE,gBAAA,8BAAAyK,+DAAAvK,MAAA;YAAAJ,EAAA,CAAAS,kBAAA,CAAAiK,GAAA,CAAA5J,UAAA,EAAAV,MAAA,MAAAsK,GAAA,CAAA5J,UAAA,GAAAV,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAElDJ,EAAA,CAAAY,YAAA,EAAY;UAChBZ,EAAA,CAAA6C,UAAA,IAAA+H,oCAAA,uBAKC;UAGG5K,EAFJ,CAAAC,cAAA,aAA+B,aACC,SACxB;UAAAD,EAAA,CAAAiB,MAAA,eAAQ;UAAAjB,EAAA,CAAAY,YAAA,EAAK;UAKTZ,EAJR,CAAAC,cAAA,YAAM,cACc,cACa,eACkD,cAC3D;UAAAD,EAAA,CAAAiB,MAAA,oGAChB;UAAAjB,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAC,cAAA,aAAe;UAAAD,EAAA,CAAAwB,SAAA,aAAiC;UAACxB,EAAA,CAAAiB,MAAA,mBAAU;UAAAjB,EAAA,CAAAC,cAAA,aACnB;UAAAD,EAAA,CAAAiB,MAAA,gBAAQ;UAEpDjB,EAFoD,CAAAY,YAAA,EAAI,EAAI,EACpD,EACF;UAIFZ,EAHJ,CAAAC,cAAA,cAA6B,eAEoH,aACnF;UAAAD,EAAA,CAAAiB,MAAA,oBAAY;UAAAjB,EAAA,CAAAY,YAAA,EAAI;UAmB1EZ,EAlBA,CAAA6C,UAAA,KAAAgI,wCAAA,2BAAqF,KAAAC,iCAAA,oBASiC,KAAAC,+BAAA,kBAK/C,KAAAC,kCAAA,qBAKjD;UAMhChL,EALQ,CAAAY,YAAA,EAAM,EACF,EACF,EAED,EACH;UACNZ,EAAA,CAAAC,cAAA,qBAEI;UAF6BD,EAAA,CAAAE,gBAAA,8BAAA+K,gEAAA7K,MAAA;YAAAJ,EAAA,CAAAS,kBAAA,CAAAiK,GAAA,CAAA5J,UAAA,EAAAV,MAAA,MAAAsK,GAAA,CAAA5J,UAAA,GAAAV,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAACJ,EAAA,CAAAkB,UAAA,mBAAAgK,qDAAA;YAAA,OAASR,GAAA,CAAAxB,OAAA,EAAS;UAAA,EAAC;UAE5ElJ,EAAA,CAAAY,YAAA,EAAY;UA2ChBZ,EA1CA,CAAA6C,UAAA,KAAAsI,wCAAA,2BAAgE,KAAAC,wCAAA,2BA0CJ;UAyC9DpL,EAAA,CAAAY,YAAA,EAAM;UACNZ,EAAA,CAAAwB,SAAA,uBAAmC;UACrCxB,EAAA,CAAAY,YAAA,EAAM;;;UAjJwBZ,EAAA,CAAAa,UAAA,cAAa;UAGzBb,EAAA,CAAAsB,SAAA,GAAgB;UAAetB,EAA/B,CAAAa,UAAA,UAAA6J,GAAA,CAAA7F,MAAA,CAAgB,SAAA6F,GAAA,CAAA5F,IAAA,CAAc,uCAAuC;UAE1E9E,EAAA,CAAAsB,SAAA,EAAe;UAAftB,EAAA,CAAAa,UAAA,UAAA6J,GAAA,CAAApG,KAAA,CAAe;UAACtE,EAAA,CAAAe,gBAAA,eAAA2J,GAAA,CAAA5J,UAAA,CAA2B;UAACd,EAAA,CAAAa,UAAA,+EAEpD;UAEAb,EAAA,CAAAsB,SAAA,EAA4B;UAA5BtB,EAAA,CAAAa,UAAA,SAAA6J,GAAA,CAAA5J,UAAA,aAA4B;UAsBJd,EAAA,CAAAsB,SAAA,IAAoE;UAApEtB,EAAA,CAAAa,UAAA,UAAA6J,GAAA,CAAA9I,UAAA,kBAAA8I,GAAA,CAAA9I,UAAA,CAAAY,WAAA,kBAAAkI,GAAA,CAAA9I,UAAA,kBAAA8I,GAAA,CAAA9I,UAAA,CAAAY,WAAA,EAAoE;UAK3ExC,EAAA,CAAAsB,SAAA,EAGR;UAHQtB,EAAA,CAAAa,UAAA,UAAA6J,GAAA,CAAAnJ,YAAA,MAAAmJ,GAAA,CAAA9I,UAAA,kBAAA8I,GAAA,CAAA9I,UAAA,CAAAY,WAAA,kBAAAkI,GAAA,CAAA9I,UAAA,kBAAA8I,GAAA,CAAA9I,UAAA,CAAAY,WAAA,GAGR;UAMsBxC,EAAA,CAAAsB,SAAA,EAA+C;UAA/CtB,EAAA,CAAAa,UAAA,UAAA6J,GAAA,CAAA9I,UAAA,kBAAA8I,GAAA,CAAA9I,UAAA,CAAAY,WAAA,oBAA+C;UAI5DxC,EAAA,CAAAsB,SAAA,EAAkB;UAAlBtB,EAAA,CAAAa,UAAA,SAAA6J,GAAA,CAAAnJ,YAAA,CAAkB;UAQ1BvB,EAAA,CAAAsB,SAAA,EAAqB;UAArBtB,EAAA,CAAAa,UAAA,UAAA6J,GAAA,CAAApF,WAAA,CAAqB;UAACtF,EAAA,CAAAe,gBAAA,eAAA2J,GAAA,CAAA5J,UAAA,CAA2B;UAAqBd,EAAA,CAAAa,UAAA,oFAE9E;UACYb,EAAA,CAAAsB,SAAA,EAA+C;UAA/CtB,EAAA,CAAAa,UAAA,UAAA6J,GAAA,CAAArF,gBAAA,kBAAAqF,GAAA,CAAArF,gBAAA,CAAAV,IAAA,qBAA+C;UA0C/C3E,EAAA,CAAAsB,SAAA,EAA2C;UAA3CtB,EAAA,CAAAa,UAAA,UAAA6J,GAAA,CAAArF,gBAAA,kBAAAqF,GAAA,CAAArF,gBAAA,CAAAV,IAAA,iBAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}