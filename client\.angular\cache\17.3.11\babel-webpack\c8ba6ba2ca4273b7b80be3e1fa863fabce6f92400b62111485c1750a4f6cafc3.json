{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { AccountRoutingModule } from './account-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { TableModule } from 'primeng/table';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TabViewModule } from 'primeng/tabview';\nimport { ToastModule } from 'primeng/toast';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { DialogModule } from 'primeng/dialog';\nimport { EditorModule } from 'primeng/editor';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { AccountSharedModule } from './account-shared.module';\nimport { CommonFormModule } from '../common-form/common-form.module';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport let AccountModule = /*#__PURE__*/(() => {\n  class AccountModule {\n    static {\n      this.ɵfac = function AccountModule_Factory(t) {\n        return new (t || AccountModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AccountModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [MessageService, ConfirmationService],\n        imports: [CommonModule, AccountSharedModule, AccountRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, ReactiveFormsModule, CalendarModule, NgSelectModule, ButtonModule, TabViewModule, ToastModule, CheckboxModule, ConfirmDialogModule, AutoCompleteModule, InputTextModule, ProgressSpinnerModule, SidebarModule, DialogModule, EditorModule, SharedModule, CommonFormModule, MultiSelectModule]\n      });\n    }\n  }\n  return AccountModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}