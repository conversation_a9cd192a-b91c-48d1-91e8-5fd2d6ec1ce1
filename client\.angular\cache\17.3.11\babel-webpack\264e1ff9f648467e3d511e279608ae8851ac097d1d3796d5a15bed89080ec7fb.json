{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../activities.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/tabview\";\nimport * as i9 from \"primeng/breadcrumb\";\nimport * as i10 from \"primeng/confirmdialog\";\nimport * as i11 from \"primeng/toast\";\nfunction TasksDetailsComponent_p_tabPanel_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction TasksDetailsComponent_p_tabPanel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 31);\n    i0.ɵɵtemplate(1, TasksDetailsComponent_p_tabPanel_9_ng_template_1_Template, 2, 2, \"ng-template\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport let TasksDetailsComponent = /*#__PURE__*/(() => {\n  class TasksDetailsComponent {\n    constructor(router, route, activitiesservice) {\n      this.router = router;\n      this.route = route;\n      this.activitiesservice = activitiesservice;\n      this.unsubscribe$ = new Subject();\n      this.activityDetails = null;\n      this.items = [];\n      this.id = '';\n      this.breadcrumbitems = [];\n      this.activeItem = null;\n      this.isSidebarHidden = false;\n      this.Actions = [];\n      this.activeIndex = 0;\n    }\n    ngOnInit() {\n      this.id = this.route.snapshot.paramMap.get('id') || '';\n      this.home = {\n        icon: 'pi pi-home text-lg',\n        routerLink: ['/']\n      };\n      this.Actions = [{\n        name: 'Set as Complete',\n        code: 'SCO'\n      }, {\n        name: 'Set as Canceled',\n        code: 'SCA'\n      }];\n      this.makeMenuItems(this.id);\n      if (this.items.length > 0) {\n        this.activeItem = this.items[0];\n      }\n      this.setActiveTabFromURL();\n      this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n        const activityId = params.get('id');\n        if (activityId) {\n          this.loadActivityData(activityId);\n        }\n      });\n      // Listen for route changes to keep active tab in sync\n      this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n        this.setActiveTabFromURL();\n      });\n    }\n    makeMenuItems(id) {\n      this.items = [{\n        label: 'Overview',\n        routerLink: `/store/activities/tasks/${id}/overview`\n      }, {\n        label: 'Contacts',\n        routerLink: `/store/activities/tasks/${id}/contacts`\n      }, {\n        label: 'Sales Team',\n        routerLink: `/store/activities/tasks/${id}/sales-team`\n      }, {\n        label: 'AI Insights',\n        routerLink: `/store/activities/tasks/${id}/ai-insights`\n      }, {\n        label: 'Organization Data',\n        routerLink: `/store/activities/tasks/${id}/organization-data`\n      }, {\n        label: 'Attachments',\n        routerLink: `/store/activities/tasks/${id}/attachments`\n      }, {\n        label: 'Notes',\n        routerLink: `/store/activities/tasks/${id}/notes`\n      }];\n    }\n    setActiveTabFromURL() {\n      const fullPath = this.router.url;\n      const currentTab = fullPath.split('/').pop() || 'overview';\n      if (this.items.length === 0) return;\n      const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n      this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n      this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n      this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n    }\n    updateBreadcrumb(activeTab) {\n      this.breadcrumbitems = [{\n        label: 'Tasks',\n        routerLink: ['/store/activities/tasks']\n      }, {\n        label: activeTab,\n        routerLink: []\n      }];\n    }\n    onTabChange(event) {\n      if (this.items.length === 0) return;\n      this.activeIndex = event.index;\n      const selectedTab = this.items[this.activeIndex];\n      if (selectedTab?.routerLink) {\n        this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n      }\n    }\n    loadActivityData(activityId) {\n      this.activitiesservice.getActivityByID(activityId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          this.activityDetails = response?.data[0] || null;\n        },\n        error: error => {\n          console.error('Error fetching data:', error);\n        }\n      });\n    }\n    goToBack() {\n      this.router.navigate(['/store/activities/tasks']);\n    }\n    toggleSidebar() {\n      this.isSidebarHidden = !this.isSidebarHidden;\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function TasksDetailsComponent_Factory(t) {\n        return new (t || TasksDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ActivitiesService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TasksDetailsComponent,\n        selectors: [[\"app-tasks-details\"]],\n        decls: 76,\n        vars: 17,\n        consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"activeIndexChange\", \"onChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\", \"sidebar-c-details\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n        template: function TasksDetailsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"p-toast\", 0);\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"p-breadcrumb\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p-dropdown\", 5);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function TasksDetailsComponent_Template_p_dropdown_ngModelChange_5_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"p-tabView\", 8);\n            i0.ɵɵtwoWayListener(\"activeIndexChange\", function TasksDetailsComponent_Template_p_tabView_activeIndexChange_8_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"onChange\", function TasksDetailsComponent_Template_p_tabView_onChange_8_listener($event) {\n              return ctx.onTabChange($event);\n            });\n            i0.ɵɵtemplate(9, TasksDetailsComponent_p_tabPanel_9_Template, 2, 1, \"p-tabPanel\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15)(16, \"div\", 16)(17, \"h5\", 17);\n            i0.ɵɵtext(18, \"JS\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"div\", 18)(20, \"h5\", 19);\n            i0.ɵɵtext(21, \" SNJYA Customer Sprint 2 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"ul\", 20)(23, \"li\", 21)(24, \"span\", 22);\n            i0.ɵɵtext(25, \"CRM ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(26, \" : 05545SD585 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"li\", 21)(28, \"span\", 22);\n            i0.ɵɵtext(29, \"Account Owner \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(30, \" : Adam Smith \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"li\", 21)(32, \"span\", 22);\n            i0.ɵɵtext(33, \"Main Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(34, \" : Ben Jacobs \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(35, \"div\", 23)(36, \"ul\", 24)(37, \"li\", 25)(38, \"span\", 26)(39, \"i\", 27);\n            i0.ɵɵtext(40, \"location_on\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(41, \" Address\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"span\", 28);\n            i0.ɵɵtext(43, \"3030 Warrenville Rd, Suite #610, Lisle, IL, United States of America, USA 60532.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(44, \"li\", 25)(45, \"span\", 26)(46, \"i\", 27);\n            i0.ɵɵtext(47, \"phone_in_talk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(48, \" Phone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"span\", 28);\n            i0.ɵɵtext(50, \"******-423-5926\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(51, \"li\", 25)(52, \"span\", 26)(53, \"i\", 27);\n            i0.ɵɵtext(54, \"phone_in_talk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(55, \" Main Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"span\", 28);\n            i0.ɵɵtext(57, \"******-423-5926\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(58, \"li\", 25)(59, \"span\", 26)(60, \"i\", 27);\n            i0.ɵɵtext(61, \"mail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(62, \" Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"span\", 28);\n            i0.ɵɵtext(64, \"<EMAIL>\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(65, \"li\", 25)(66, \"span\", 26)(67, \"i\", 27);\n            i0.ɵɵtext(68, \"language\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(69, \" Website\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(70, \"span\", 28);\n            i0.ɵɵtext(71, \"www.asardigital.com\");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(72, \"div\", 29)(73, \"p-button\", 30);\n            i0.ɵɵlistener(\"click\", function TasksDetailsComponent_Template_p_button_click_73_listener() {\n              return ctx.toggleSidebar();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(74, \"router-outlet\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelement(75, \"p-confirmDialog\");\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"life\", 3000);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.Actions);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n            i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-none font-semibold\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"scrollable\", true);\n            i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.items);\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n            i0.ɵɵadvance(61);\n            i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n            i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n          }\n        },\n        dependencies: [i3.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i4.NgControlStatus, i4.NgModel, i5.PrimeTemplate, i6.Button, i7.Dropdown, i8.TabView, i8.TabPanel, i9.Breadcrumb, i10.ConfirmDialog, i11.Toast]\n      });\n    }\n  }\n  return TasksDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}