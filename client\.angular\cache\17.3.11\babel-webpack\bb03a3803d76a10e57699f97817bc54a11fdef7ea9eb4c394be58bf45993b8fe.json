{"ast": null, "code": "import { forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Observable, Subject, concat, of } from 'rxjs';\nimport { map, switchMap, tap, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../prospects.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ng-select/ng-select\";\nfunction EmployeeSelectComponent_ng_template_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\": \", item_r1.bp_full_name, \"\");\n  }\n}\nfunction EmployeeSelectComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, EmployeeSelectComponent_ng_template_2_span_2_Template, 2, 1, \"span\", 2);\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r1.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r1.bp_full_name);\n  }\n}\nexport class EmployeeSelectComponent {\n  constructor(prospectsService) {\n    this.prospectsService = prospectsService;\n    this.employees$ = new Observable();\n    this.employeeInput$ = new Subject();\n    this.employeeLoading = false;\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  ngOnInit() {\n    this.loadEmployees();\n  }\n  loadEmployees() {\n    this.employees$ = concat(this.employeeInput$.pipe(distinctUntilChanged(), tap(() => this.employeeLoading = true), switchMap(term => {\n      const params = {\n        [`filters[roles][bp_role][$eq]`]: 'BUP003',\n        [`fields[0]`]: 'bp_id',\n        [`fields[1]`]: 'bp_full_name'\n      };\n      if (term) {\n        params[`filters[$or][0][bp_id][$containsi]`] = term;\n        params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n        return this.prospectsService.getEmployee(params).pipe(map(data => data), tap(() => this.employeeLoading = false));\n      }\n      return of([]).pipe(tap(() => this.employeeLoading = false));\n    })));\n  }\n  writeValue(value) {\n    this._value = value;\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {}\n  selectEmployee(value) {\n    const bp_id = value?.bp_id || null;\n    this._value = bp_id;\n    this.onChange(bp_id);\n  }\n  static {\n    this.ɵfac = function EmployeeSelectComponent_Factory(t) {\n      return new (t || EmployeeSelectComponent)(i0.ɵɵdirectiveInject(i1.ProspectsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EmployeeSelectComponent,\n      selectors: [[\"app-employee-select\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => EmployeeSelectComponent),\n        multi: true\n      }])],\n      decls: 3,\n      vars: 10,\n      consts: [[\"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"appendTo\", \"body\", 3, \"change\", \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\"], [\"ng-option-tmp\", \"\"], [4, \"ngIf\"]],\n      template: function EmployeeSelectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ng-select\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵlistener(\"change\", function EmployeeSelectComponent_Template_ng_select_change_0_listener($event) {\n            return ctx.selectEmployee($event);\n          });\n          i0.ɵɵtemplate(2, EmployeeSelectComponent_ng_template_2_Template, 3, 2, \"ng-template\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(1, 8, ctx.employees$))(\"hideSelected\", true)(\"loading\", ctx.employeeLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.employeeInput$)(\"maxSelectedItems\", 10);\n        }\n      },\n      dependencies: [i2.NgIf, i3.NgSelectComponent, i3.NgOptionTemplateDirective, i2.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["forwardRef", "NG_VALUE_ACCESSOR", "Observable", "Subject", "concat", "of", "map", "switchMap", "tap", "distinctUntilChanged", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "item_r1", "bp_full_name", "ɵɵtemplate", "EmployeeSelectComponent_ng_template_2_span_2_Template", "ɵɵtextInterpolate", "bp_id", "ɵɵproperty", "EmployeeSelectComponent", "constructor", "prospectsService", "employees$", "employeeInput$", "employeeLoading", "onChange", "value", "onTouched", "ngOnInit", "loadEmployees", "pipe", "term", "params", "getEmployee", "data", "writeValue", "_value", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "selectEmployee", "ɵɵdirectiveInject", "i1", "ProspectsService", "selectors", "features", "ɵɵProvidersFeature", "provide", "useExisting", "multi", "decls", "vars", "consts", "template", "EmployeeSelectComponent_Template", "rf", "ctx", "ɵɵlistener", "EmployeeSelectComponent_Template_ng_select_change_0_listener", "$event", "EmployeeSelectComponent_ng_template_2_Template", "ɵɵclassMap", "ɵɵpipeBind1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\employee-select\\employee-select.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\employee-select\\employee-select.component.html"], "sourcesContent": ["import { Component, forwardRef } from '@angular/core';\r\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { Observable, Subject, concat, of } from 'rxjs';\r\nimport { map, switchMap, tap, distinctUntilChanged } from 'rxjs/operators';\r\nimport { ProspectsService } from '../prospects.service';\r\n\r\n@Component({\r\n  selector: 'app-employee-select',\r\n  templateUrl: './employee-select.component.html',\r\n  styleUrl: './employee-select.component.scss',\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => EmployeeSelectComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class EmployeeSelectComponent {\r\n  public employees$: Observable<any[]> = new Observable<any[]>();\r\n  public employeeInput$ = new Subject<string>();\r\n  public employeeLoading = false;\r\n\r\n  private _value: any;\r\n\r\n  constructor(private prospectsService: ProspectsService) {}\r\n\r\n  ngOnInit() {\r\n    this.loadEmployees();\r\n  }\r\n\r\n  loadEmployees() {\r\n    this.employees$ = concat(\r\n      this.employeeInput$.pipe(\r\n        distinctUntilChanged(),\r\n        tap(() => (this.employeeLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            [`filters[roles][bp_role][$eq]`]: 'BUP003',\r\n            [`fields[0]`]: 'bp_id',\r\n            [`fields[1]`]: 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params[`filters[$or][0][bp_id][$containsi]`] = term;\r\n            params[`filters[$or][1][bp_full_name][$containsi]`] = term;\r\n            return this.prospectsService.getEmployee(params).pipe(\r\n              map((data: any) => data),\r\n              tap(() => (this.employeeLoading = false))\r\n            );\r\n          }\r\n          return of([]).pipe(tap(() => (this.employeeLoading = false)));\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  onChange = (value: any) => {};\r\n  onTouched = () => {};\r\n\r\n  writeValue(value: any): void {\r\n    this._value = value;\r\n  }\r\n\r\n  registerOnChange(fn: any): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: any): void {\r\n    this.onTouched = fn;\r\n  }\r\n\r\n  setDisabledState(isDisabled: boolean): void {}\r\n\r\n  selectEmployee(value: any) {\r\n    const bp_id = value?.bp_id || null;\r\n    this._value = bp_id;\r\n    this.onChange(bp_id);\r\n  }\r\n}\r\n", "<ng-select [items]=\"employees$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\" [hideSelected]=\"true\"\r\n    [loading]=\"employeeLoading\" [minTermLength]=\"0\" [typeahead]=\"employeeInput$\" [maxSelectedItems]=\"10\" appendTo=\"body\"\r\n    (change)=\"selectEmployee($event)\" [class]=\"'multiselect-dropdown p-inputtext p-component p-element'\">\r\n\r\n    <ng-template ng-option-tmp let-item=\"item\">\r\n        <span>{{ item.bp_id }}</span>\r\n        <span *ngIf=\"item.bp_full_name\">: {{ item.bp_full_name }}</span>\r\n    </ng-template>\r\n</ng-select>"], "mappings": "AAAA,SAAoBA,UAAU,QAAQ,eAAe;AACrD,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,EAAE,QAAQ,MAAM;AACtD,SAASC,GAAG,EAAEC,SAAS,EAAEC,GAAG,EAAEC,oBAAoB,QAAQ,gBAAgB;;;;;;;ICGlEC,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAK,kBAAA,OAAAC,OAAA,CAAAC,YAAA,KAAyB;;;;;IADzDP,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAQ,UAAA,IAAAC,qDAAA,kBAAgC;;;;IAD1BT,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAU,iBAAA,CAAAJ,OAAA,CAAAK,KAAA,CAAgB;IACfX,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAY,UAAA,SAAAN,OAAA,CAAAC,YAAA,CAAuB;;;ADYtC,OAAM,MAAOM,uBAAuB;EAOlCC,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAN7B,KAAAC,UAAU,GAAsB,IAAIxB,UAAU,EAAS;IACvD,KAAAyB,cAAc,GAAG,IAAIxB,OAAO,EAAU;IACtC,KAAAyB,eAAe,GAAG,KAAK;IAoC9B,KAAAC,QAAQ,GAAIC,KAAU,IAAI,CAAE,CAAC;IAC7B,KAAAC,SAAS,GAAG,MAAK,CAAE,CAAC;EAjCqC;EAEzDC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAA,aAAaA,CAAA;IACX,IAAI,CAACP,UAAU,GAAGtB,MAAM,CACtB,IAAI,CAACuB,cAAc,CAACO,IAAI,CACtBzB,oBAAoB,EAAE,EACtBD,GAAG,CAAC,MAAO,IAAI,CAACoB,eAAe,GAAG,IAAK,CAAC,EACxCrB,SAAS,CAAE4B,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,CAAC,8BAA8B,GAAG,QAAQ;QAC1C,CAAC,WAAW,GAAG,OAAO;QACtB,CAAC,WAAW,GAAG;OAChB;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;QAC1D,OAAO,IAAI,CAACV,gBAAgB,CAACY,WAAW,CAACD,MAAM,CAAC,CAACF,IAAI,CACnD5B,GAAG,CAAEgC,IAAS,IAAKA,IAAI,CAAC,EACxB9B,GAAG,CAAC,MAAO,IAAI,CAACoB,eAAe,GAAG,KAAM,CAAC,CAC1C;MACH;MACA,OAAOvB,EAAE,CAAC,EAAE,CAAC,CAAC6B,IAAI,CAAC1B,GAAG,CAAC,MAAO,IAAI,CAACoB,eAAe,GAAG,KAAM,CAAC,CAAC;IAC/D,CAAC,CAAC,CACH,CACF;EACH;EAKAW,UAAUA,CAACT,KAAU;IACnB,IAAI,CAACU,MAAM,GAAGV,KAAK;EACrB;EAEAW,gBAAgBA,CAACC,EAAO;IACtB,IAAI,CAACb,QAAQ,GAAGa,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAO;IACvB,IAAI,CAACX,SAAS,GAAGW,EAAE;EACrB;EAEAE,gBAAgBA,CAACC,UAAmB,GAAS;EAE7CC,cAAcA,CAAChB,KAAU;IACvB,MAAMT,KAAK,GAAGS,KAAK,EAAET,KAAK,IAAI,IAAI;IAClC,IAAI,CAACmB,MAAM,GAAGnB,KAAK;IACnB,IAAI,CAACQ,QAAQ,CAACR,KAAK,CAAC;EACtB;;;uBA5DWE,uBAAuB,EAAAb,EAAA,CAAAqC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAvB1B,uBAAuB;MAAA2B,SAAA;MAAAC,QAAA,GAAAzC,EAAA,CAAA0C,kBAAA,CARvB,CACT;QACEC,OAAO,EAAEpD,iBAAiB;QAC1BqD,WAAW,EAAEtD,UAAU,CAAC,MAAMuB,uBAAuB,CAAC;QACtDgC,KAAK,EAAE;OACR,CACF;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBHnD,EAAA,CAAAC,cAAA,mBAEyG;;UAArGD,EAAA,CAAAqD,UAAA,oBAAAC,6DAAAC,MAAA;YAAA,OAAUH,GAAA,CAAAhB,cAAA,CAAAmB,MAAA,CAAsB;UAAA,EAAC;UAEjCvD,EAAA,CAAAQ,UAAA,IAAAgD,8CAAA,yBAA2C;UAI/CxD,EAAA,CAAAG,YAAA,EAAY;;;UAN0BH,EAAA,CAAAyD,UAAA,0DAAkE;UADvBzD,EADtE,CAAAY,UAAA,UAAAZ,EAAA,CAAA0D,WAAA,OAAAN,GAAA,CAAApC,UAAA,EAA4B,sBAAiE,YAAAoC,GAAA,CAAAlC,eAAA,CACzE,oBAAoB,cAAAkC,GAAA,CAAAnC,cAAA,CAA6B,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}