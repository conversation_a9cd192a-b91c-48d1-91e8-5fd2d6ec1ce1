{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { CompetitorsRoutingModule } from './competitors-routing.module';\nimport { TabViewModule } from 'primeng/tabview';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport let CompetitorsModule = /*#__PURE__*/(() => {\n  class CompetitorsModule {\n    static {\n      this.ɵfac = function CompetitorsModule_Factory(t) {\n        return new (t || CompetitorsModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: CompetitorsModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, CompetitorsRoutingModule, BreadcrumbModule, TableModule, FormsModule, ReactiveFormsModule, ButtonModule, CheckboxModule, TabViewModule, InputTextModule, MultiSelectModule]\n      });\n    }\n  }\n  return CompetitorsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}