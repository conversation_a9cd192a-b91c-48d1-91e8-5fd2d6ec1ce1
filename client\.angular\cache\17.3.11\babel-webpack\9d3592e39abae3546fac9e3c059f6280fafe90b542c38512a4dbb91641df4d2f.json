{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs';\nimport { AppSidebarComponent } from './app.sidebar.component';\nimport { AppTopbarComponent } from './app.topbar.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./app.menu.service\";\nimport * as i2 from \"./service/app.layout.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"./config/app.config.component\";\nimport * as i6 from \"./app.breadcrumb.component\";\nimport * as i7 from \"./app.topbar.component\";\nimport * as i8 from \"./app.profilesidebar.component\";\nexport let AppLayoutComponent = /*#__PURE__*/(() => {\n  class AppLayoutComponent {\n    constructor(menuService, layoutService, renderer, router) {\n      this.menuService = menuService;\n      this.layoutService = layoutService;\n      this.renderer = renderer;\n      this.router = router;\n      this.overlayMenuOpenSubscription = this.layoutService.overlayOpen$.subscribe(() => {\n        if (!this.menuOutsideClickListener) {\n          this.menuOutsideClickListener = this.renderer.listen('document', 'click', event => {\n            const isOutsideClicked = !(this.appTopbar.el.nativeElement.isSameNode(event.target) || this.appTopbar.el.nativeElement.contains(event.target) || this.appTopbar.menuButton.nativeElement.isSameNode(event.target) || this.appTopbar.menuButton.nativeElement.contains(event.target));\n            if (isOutsideClicked) {\n              this.hideMenu();\n            }\n          });\n        }\n        if ((this.layoutService.isHorizontal() || this.layoutService.isSlim() || this.layoutService.isSlimPlus()) && !this.menuScrollListener) {\n          this.menuScrollListener = this.renderer.listen(this.appTopbar.appSidebar.menuContainer.nativeElement, 'scroll', event => {\n            if (this.layoutService.isDesktop()) {\n              this.hideMenu();\n            }\n          });\n        }\n        if (this.layoutService.state.staticMenuMobileActive) {\n          this.blockBodyScroll();\n        }\n      });\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n        this.hideMenu();\n      });\n    }\n    blockBodyScroll() {\n      if (document.body.classList) {\n        document.body.classList.add('blocked-scroll');\n      } else {\n        document.body.className += ' blocked-scroll';\n      }\n    }\n    unblockBodyScroll() {\n      if (document.body.classList) {\n        document.body.classList.remove('blocked-scroll');\n      } else {\n        document.body.className = document.body.className.replace(new RegExp('(^|\\\\b)' + 'blocked-scroll'.split(' ').join('|') + '(\\\\b|$)', 'gi'), ' ');\n      }\n    }\n    hideMenu() {\n      this.layoutService.state.overlayMenuActive = false;\n      this.layoutService.state.staticMenuMobileActive = false;\n      this.layoutService.state.menuHoverActive = false;\n      this.menuService.reset();\n      if (this.menuOutsideClickListener) {\n        this.menuOutsideClickListener();\n        this.menuOutsideClickListener = null;\n      }\n      if (this.menuScrollListener) {\n        this.menuScrollListener();\n        this.menuScrollListener = null;\n      }\n      this.unblockBodyScroll();\n    }\n    get containerClass() {\n      return {\n        'layout-light': this.layoutService.config().colorScheme === 'light',\n        'layout-dark': this.layoutService.config().colorScheme === 'dark',\n        'layout-overlay': this.layoutService.config().menuMode === 'overlay',\n        'layout-static': this.layoutService.config().menuMode === 'static',\n        'layout-slim': this.layoutService.config().menuMode === 'slim',\n        'layout-slim-plus': this.layoutService.config().menuMode === 'slim-plus',\n        'layout-horizontal': this.layoutService.config().menuMode === 'horizontal',\n        'layout-reveal': this.layoutService.config().menuMode === 'reveal',\n        'layout-drawer': this.layoutService.config().menuMode === 'drawer',\n        'layout-static-inactive': this.layoutService.state.staticMenuDesktopInactive && this.layoutService.config().menuMode === 'static',\n        'layout-overlay-active': this.layoutService.state.overlayMenuActive,\n        'layout-mobile-active': this.layoutService.state.staticMenuMobileActive,\n        'p-ripple-disabled': !this.layoutService.config().ripple,\n        'layout-sidebar-active': this.layoutService.state.sidebarActive,\n        'layout-sidebar-anchored': this.layoutService.state.anchored\n      };\n    }\n    ngOnDestroy() {\n      if (this.overlayMenuOpenSubscription) {\n        this.overlayMenuOpenSubscription.unsubscribe();\n      }\n      if (this.menuOutsideClickListener) {\n        this.menuOutsideClickListener();\n      }\n    }\n    static {\n      this.ɵfac = function AppLayoutComponent_Factory(t) {\n        return new (t || AppLayoutComponent)(i0.ɵɵdirectiveInject(i1.MenuService), i0.ɵɵdirectiveInject(i2.LayoutService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppLayoutComponent,\n        selectors: [[\"app-layout\"]],\n        viewQuery: function AppLayoutComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(AppSidebarComponent, 5);\n            i0.ɵɵviewQuery(AppTopbarComponent, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.appSidebar = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.appTopbar = _t.first);\n          }\n        },\n        decls: 9,\n        vars: 1,\n        consts: [[1, \"layout-container\", 3, \"ngClass\"], [1, \"layout-content-wrapper\"], [1, \"content-breadcrumb\"], [1, \"layout-content\"], [1, \"layout-mask\"]],\n        template: function AppLayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"app-topbar\")(3, \"app-breadcrumb\", 2);\n            i0.ɵɵelementStart(4, \"div\", 3);\n            i0.ɵɵelement(5, \"router-outlet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(6, \"div\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(7, \"app-profilemenu\")(8, \"app-config\");\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngClass\", ctx.containerClass);\n          }\n        },\n        dependencies: [i4.NgClass, i3.RouterOutlet, i5.AppConfigComponent, i6.AppBreadcrumbComponent, i7.AppTopbarComponent, i8.AppProfileSidebarComponent],\n        encapsulation: 2\n      });\n    }\n  }\n  return AppLayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}