{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { BehaviorSubject } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./service/app.layout.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/inputtext\";\nimport * as i5 from \"primeng/button\";\nconst _c0 = [\"searchinput\"];\nconst _c1 = a0 => ({\n  \"breadcrumb-search-active\": a0\n});\nfunction AppBreadcrumbComponent_ng_template_3_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 16);\n    i0.ɵɵtext(1, \" / \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppBreadcrumbComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, AppBreadcrumbComponent_ng_template_3_li_2_Template, 2, 0, \"li\", 15);\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const last_r3 = ctx.last;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r2.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !last_r3);\n  }\n}\nexport class AppBreadcrumbComponent {\n  constructor(router, layoutService) {\n    this.router = router;\n    this.layoutService = layoutService;\n    this._breadcrumbs$ = new BehaviorSubject([]);\n    this.breadcrumbs$ = this._breadcrumbs$.asObservable();\n    this.searchActive = false;\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      const root = this.router.routerState.snapshot.root;\n      const breadcrumbs = [];\n      this.addBreadcrumb(root, [], breadcrumbs);\n      this._breadcrumbs$.next(breadcrumbs);\n    });\n  }\n  activateSearch() {\n    this.searchActive = true;\n    setTimeout(() => {\n      this.searchInput.nativeElement.focus();\n    }, 100);\n  }\n  deactivateSearch() {\n    this.searchActive = false;\n  }\n  onConfigButtonClick() {\n    this.layoutService.showConfigSidebar();\n  }\n  onSidebarButtonClick() {\n    this.layoutService.showSidebar();\n  }\n  addBreadcrumb(route, parentUrl, breadcrumbs) {\n    const routeUrl = parentUrl.concat(route.url.map(url => url.path));\n    const breadcrumb = route.data['breadcrumb'];\n    const parentBreadcrumb = route.parent && route.parent.data ? route.parent.data['breadcrumb'] : null;\n    if (breadcrumb && breadcrumb !== parentBreadcrumb) {\n      breadcrumbs.push({\n        label: route.data['breadcrumb'],\n        url: '/' + routeUrl.join('/')\n      });\n    }\n    if (route.firstChild) {\n      this.addBreadcrumb(route.firstChild, routeUrl, breadcrumbs);\n    }\n  }\n  static {\n    this.ɵfac = function AppBreadcrumbComponent_Factory(t) {\n      return new (t || AppBreadcrumbComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.LayoutService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppBreadcrumbComponent,\n      selectors: [[\"app-breadcrumb\"]],\n      viewQuery: function AppBreadcrumbComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n        }\n      },\n      decls: 17,\n      vars: 6,\n      consts: [[\"searchinput\", \"\"], [1, \"layout-breadcrumb\", \"flex\", \"align-items-center\", \"relative\", \"h-3rem\"], [1, \"relative\", \"z-2\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [1, \"breadcrumb-menu\", \"flex\", \"align-items-center\", \"justify-content-end\", \"lg:hidden\", \"absolute\", \"right-0\", \"top-0\", \"z-4\", \"h-3rem\", \"w-screen\"], [1, \"w-full\", \"m-0\", \"ml-3\"], [1, \"breadcrumb-search\", \"flex\", \"justify-content-end\", 3, \"ngClass\"], [\"pButton\", \"\", \"icon\", \"pi pi-search\", \"type\", \"button\", 1, \"breadcrumb-searchbutton\", \"p-button-text\", \"p-button-secondary\", \"text-color-secondary\", \"p-button-rounded\", \"flex-shrink-0\", 3, \"click\"], [1, \"search-input-wrapper\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search\", 3, \"blur\", \"keydown.escape\"], [1, \"pi\", \"pi-search\"], [1, \"right-panel-button\", \"relative\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Today\", \"icon\", \"pi pi-bookmark\", \"styleClass\", \"font-normal\", 1, \"layout-rightmenu-button\", \"hidden\", \"md:block\", \"font-normal\", 2, \"width\", \"5.7rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bookmark\", \"styleClass\", \"font-normal\", 1, \"layout-rightmenu-button\", \"flex\", \"md:hidden\", 3, \"click\"], [\"class\", \"layout-breadcrumb-chevron\", 4, \"ngIf\"], [1, \"layout-breadcrumb-chevron\"]],\n      template: function AppBreadcrumbComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"nav\")(2, \"ol\", 2);\n          i0.ɵɵtemplate(3, AppBreadcrumbComponent_ng_template_3_Template, 3, 2, \"ng-template\", 3);\n          i0.ɵɵpipe(4, \"async\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"ul\", 4)(6, \"li\", 5)(7, \"div\", 6)(8, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function AppBreadcrumbComponent_Template_button_click_8_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.activateSearch());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"span\", 9)(11, \"input\", 10, 0);\n          i0.ɵɵlistener(\"blur\", function AppBreadcrumbComponent_Template_input_blur_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.deactivateSearch());\n          })(\"keydown.escape\", function AppBreadcrumbComponent_Template_input_keydown_escape_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.deactivateSearch());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(14, \"li\", 12)(15, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function AppBreadcrumbComponent_Template_button_click_15_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSidebarButtonClick());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function AppBreadcrumbComponent_Template_button_click_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSidebarButtonClick());\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(4, 2, ctx.breadcrumbs$));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c1, ctx.searchActive));\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i4.InputText, i5.ButtonDirective, i3.AsyncPipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["NavigationEnd", "BehaviorSubject", "filter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "AppBreadcrumbComponent_ng_template_3_li_2_Template", "ɵɵadvance", "ɵɵtextInterpolate", "item_r2", "label", "ɵɵproperty", "last_r3", "AppBreadcrumbComponent", "constructor", "router", "layoutService", "_breadcrumbs$", "breadcrumbs$", "asObservable", "searchActive", "events", "pipe", "event", "subscribe", "root", "routerState", "snapshot", "breadcrumbs", "addBreadcrumb", "next", "activateSearch", "setTimeout", "searchInput", "nativeElement", "focus", "deactivateSearch", "onConfigButtonClick", "showConfigSidebar", "onSidebarButtonClick", "showSidebar", "route", "parentUrl", "routeUrl", "concat", "url", "map", "path", "breadcrumb", "data", "parentBreadcrumb", "parent", "push", "join", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "Router", "i2", "LayoutService", "selectors", "viewQuery", "AppBreadcrumbComponent_Query", "rf", "ctx", "AppBreadcrumbComponent_ng_template_3_Template", "ɵɵlistener", "AppBreadcrumbComponent_Template_button_click_8_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "AppBreadcrumbComponent_Template_input_blur_11_listener", "AppBreadcrumbComponent_Template_input_keydown_escape_11_listener", "ɵɵelement", "AppBreadcrumbComponent_Template_button_click_15_listener", "AppBreadcrumbComponent_Template_button_click_16_listener", "ɵɵpipeBind1", "ɵɵpureFunction1", "_c1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\backoffice\\layout\\app.breadcrumb.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\backoffice\\layout\\app.breadcrumb.component.html"], "sourcesContent": ["import { Component, ElementRef, ViewChild } from '@angular/core';\r\nimport { ActivatedRouteSnapshot, NavigationEnd, Router } from '@angular/router';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { filter } from 'rxjs/operators';\r\nimport { LayoutService } from './service/app.layout.service';\r\n\r\ninterface Breadcrumb {\r\n    label: string;\r\n    url?: string;\r\n}\r\n\r\n@Component({\r\n    selector: 'app-breadcrumb',\r\n    templateUrl: './app.breadcrumb.component.html'\r\n})\r\nexport class AppBreadcrumbComponent {\r\n\r\n    private readonly _breadcrumbs$ = new BehaviorSubject<Breadcrumb[]>([]);\r\n\r\n    readonly breadcrumbs$ = this._breadcrumbs$.asObservable();\r\n    @ViewChild('searchinput') searchInput!: ElementRef;\r\n    searchActive: boolean = false;\r\n    \r\n    constructor(private router: Router,public layoutService: LayoutService) {\r\n        this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe(event => {\r\n            const root = this.router.routerState.snapshot.root;\r\n            const breadcrumbs: Breadcrumb[] = [];\r\n            this.addBreadcrumb(root, [], breadcrumbs);\r\n\r\n            this._breadcrumbs$.next(breadcrumbs);\r\n        });\r\n    }\r\n\r\n    activateSearch() {\r\n        this.searchActive = true;\r\n        setTimeout(() => {\r\n            this.searchInput.nativeElement.focus();\r\n        }, 100);\r\n    }\r\n\r\n    deactivateSearch() {\r\n        this.searchActive = false;\r\n    }\r\n\r\n    onConfigButtonClick() {\r\n        this.layoutService.showConfigSidebar();\r\n    }\r\n    \r\n    onSidebarButtonClick() {\r\n        this.layoutService.showSidebar();\r\n    }\r\n\r\n    private addBreadcrumb(route: ActivatedRouteSnapshot, parentUrl: string[], breadcrumbs: Breadcrumb[]) {\r\n        const routeUrl = parentUrl.concat(route.url.map(url => url.path));\r\n        const breadcrumb = route.data['breadcrumb'];\r\n        const parentBreadcrumb = route.parent && route.parent.data ? route.parent.data['breadcrumb'] : null;\r\n\r\n        if (breadcrumb && breadcrumb !== parentBreadcrumb) {\r\n            breadcrumbs.push({\r\n                label: route.data['breadcrumb'],\r\n                url: '/' + routeUrl.join('/')\r\n            });\r\n        }\r\n\r\n        if (route.firstChild) {\r\n            this.addBreadcrumb(route.firstChild, routeUrl, breadcrumbs);\r\n        }\r\n    }\r\n\r\n}\r\n", "<div class=\"layout-breadcrumb  flex align-items-center relative h-3rem\">\r\n    <nav >\r\n        <ol class=\"relative z-2\">\r\n            <ng-template ngFor let-item let-last=\"last\" [ngForOf]=\"breadcrumbs$ | async\">\r\n                <li>{{item.label}}</li>\r\n                <li *ngIf=\"!last\" class=\"layout-breadcrumb-chevron\"> / </li>\r\n            </ng-template>\r\n        </ol>\r\n    </nav>\r\n    <ul class=\"breadcrumb-menu flex align-items-center justify-content-end lg:hidden absolute right-0 top-0 z-4 h-3rem w-screen\">\r\n        <li class=\"w-full m-0 ml-3\">\r\n            <div class=\"breadcrumb-search  flex justify-content-end\" [ngClass]=\"{'breadcrumb-search-active': searchActive}\">\r\n                <button pButton icon=\"pi pi-search\" class=\"breadcrumb-searchbutton p-button-text p-button-secondary text-color-secondary p-button-rounded flex-shrink-0\" type=\"button\" (click)=\"activateSearch()\"></button>\r\n                <div class=\"search-input-wrapper\">\r\n                    <span class=\"p-input-icon-right\">\r\n                        <input #searchinput type=\"text\" pInputText placeholder=\"Search\" (blur)=\"deactivateSearch()\" (keydown.escape)=\"deactivateSearch()\"/>\r\n                        <i class=\"pi pi-search\"></i>\r\n                    </span>\r\n                </div>\r\n            </div>\r\n        </li>\r\n        <li class=\"right-panel-button relative\">\r\n            <button pButton  type=\"button\" label=\"Today\" style=\"width:5.7rem\" icon=\"pi pi-bookmark\" \r\n            class=\"layout-rightmenu-button hidden md:block font-normal\" styleClass=\"font-normal\" (click)=\"onSidebarButtonClick()\"></button>\r\n            <button pButton type=\"button\" icon=\"pi pi-bookmark\" styleClass=\"font-normal\" class=\"layout-rightmenu-button flex md:hidden\" (click)=\"onSidebarButtonClick()\"></button>\r\n        </li>\r\n    </ul>\r\n</div>\r\n\r\n"], "mappings": "AACA,SAAiCA,aAAa,QAAgB,iBAAiB;AAC/E,SAASC,eAAe,QAAQ,MAAM;AACtC,SAASC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;ICEvBC,EAAA,CAAAC,cAAA,aAAoD;IAACD,EAAA,CAAAE,MAAA,UAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAD5DH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAI,UAAA,IAAAC,kDAAA,iBAAoD;;;;;IADhDL,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAO,iBAAA,CAAAC,OAAA,CAAAC,KAAA,CAAc;IACbT,EAAA,CAAAM,SAAA,EAAW;IAAXN,EAAA,CAAAU,UAAA,UAAAC,OAAA,CAAW;;;ADUhC,OAAM,MAAOC,sBAAsB;EAQ/BC,YAAoBC,MAAc,EAAQC,aAA4B;IAAlD,KAAAD,MAAM,GAANA,MAAM;IAAgB,KAAAC,aAAa,GAAbA,aAAa;IANtC,KAAAC,aAAa,GAAG,IAAIlB,eAAe,CAAe,EAAE,CAAC;IAE7D,KAAAmB,YAAY,GAAG,IAAI,CAACD,aAAa,CAACE,YAAY,EAAE;IAEzD,KAAAC,YAAY,GAAY,KAAK;IAGzB,IAAI,CAACL,MAAM,CAACM,MAAM,CAACC,IAAI,CAACtB,MAAM,CAAEuB,KAAK,IAAKA,KAAK,YAAYzB,aAAa,CAAC,CAAC,CAAC0B,SAAS,CAACD,KAAK,IAAG;MACzF,MAAME,IAAI,GAAG,IAAI,CAACV,MAAM,CAACW,WAAW,CAACC,QAAQ,CAACF,IAAI;MAClD,MAAMG,WAAW,GAAiB,EAAE;MACpC,IAAI,CAACC,aAAa,CAACJ,IAAI,EAAE,EAAE,EAAEG,WAAW,CAAC;MAEzC,IAAI,CAACX,aAAa,CAACa,IAAI,CAACF,WAAW,CAAC;IACxC,CAAC,CAAC;EACN;EAEAG,cAAcA,CAAA;IACV,IAAI,CAACX,YAAY,GAAG,IAAI;IACxBY,UAAU,CAAC,MAAK;MACZ,IAAI,CAACC,WAAW,CAACC,aAAa,CAACC,KAAK,EAAE;IAC1C,CAAC,EAAE,GAAG,CAAC;EACX;EAEAC,gBAAgBA,CAAA;IACZ,IAAI,CAAChB,YAAY,GAAG,KAAK;EAC7B;EAEAiB,mBAAmBA,CAAA;IACf,IAAI,CAACrB,aAAa,CAACsB,iBAAiB,EAAE;EAC1C;EAEAC,oBAAoBA,CAAA;IAChB,IAAI,CAACvB,aAAa,CAACwB,WAAW,EAAE;EACpC;EAEQX,aAAaA,CAACY,KAA6B,EAAEC,SAAmB,EAAEd,WAAyB;IAC/F,MAAMe,QAAQ,GAAGD,SAAS,CAACE,MAAM,CAACH,KAAK,CAACI,GAAG,CAACC,GAAG,CAACD,GAAG,IAAIA,GAAG,CAACE,IAAI,CAAC,CAAC;IACjE,MAAMC,UAAU,GAAGP,KAAK,CAACQ,IAAI,CAAC,YAAY,CAAC;IAC3C,MAAMC,gBAAgB,GAAGT,KAAK,CAACU,MAAM,IAAIV,KAAK,CAACU,MAAM,CAACF,IAAI,GAAGR,KAAK,CAACU,MAAM,CAACF,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI;IAEnG,IAAID,UAAU,IAAIA,UAAU,KAAKE,gBAAgB,EAAE;MAC/CtB,WAAW,CAACwB,IAAI,CAAC;QACb1C,KAAK,EAAE+B,KAAK,CAACQ,IAAI,CAAC,YAAY,CAAC;QAC/BJ,GAAG,EAAE,GAAG,GAAGF,QAAQ,CAACU,IAAI,CAAC,GAAG;OAC/B,CAAC;IACN;IAEA,IAAIZ,KAAK,CAACa,UAAU,EAAE;MAClB,IAAI,CAACzB,aAAa,CAACY,KAAK,CAACa,UAAU,EAAEX,QAAQ,EAAEf,WAAW,CAAC;IAC/D;EACJ;;;uBApDSf,sBAAsB,EAAAZ,EAAA,CAAAsD,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAxD,EAAA,CAAAsD,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAtB9C,sBAAsB;MAAA+C,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCb3B9D,EAFR,CAAAC,cAAA,aAAwE,UAC9D,YACuB;UACrBD,EAAA,CAAAI,UAAA,IAAA4D,6CAAA,yBAA6E;;UAKrFhE,EADI,CAAAG,YAAA,EAAK,EACH;UAIMH,EAHZ,CAAAC,cAAA,YAA6H,YAC7F,aACwF,gBACsF;UAA3BD,EAAA,CAAAiE,UAAA,mBAAAC,wDAAA;YAAAlE,EAAA,CAAAmE,aAAA,CAAAC,GAAA;YAAA,OAAApE,EAAA,CAAAqE,WAAA,CAASN,GAAA,CAAAjC,cAAA,EAAgB;UAAA,EAAC;UAAC9B,EAAA,CAAAG,YAAA,EAAS;UAGnMH,EAFR,CAAAC,cAAA,aAAkC,eACG,oBACsG;UAAvCD,EAA5B,CAAAiE,UAAA,kBAAAK,uDAAA;YAAAtE,EAAA,CAAAmE,aAAA,CAAAC,GAAA;YAAA,OAAApE,EAAA,CAAAqE,WAAA,CAAQN,GAAA,CAAA5B,gBAAA,EAAkB;UAAA,EAAC,4BAAAoC,iEAAA;YAAAvE,EAAA,CAAAmE,aAAA,CAAAC,GAAA;YAAA,OAAApE,EAAA,CAAAqE,WAAA,CAAmBN,GAAA,CAAA5B,gBAAA,EAAkB;UAAA,EAAC;UAAjInC,EAAA,CAAAG,YAAA,EAAmI;UACnIH,EAAA,CAAAwE,SAAA,aAA4B;UAI5CxE,EAHY,CAAAG,YAAA,EAAO,EACL,EACJ,EACL;UAEDH,EADJ,CAAAC,cAAA,cAAwC,kBAEkF;UAAjCD,EAAA,CAAAiE,UAAA,mBAAAQ,yDAAA;YAAAzE,EAAA,CAAAmE,aAAA,CAAAC,GAAA;YAAA,OAAApE,EAAA,CAAAqE,WAAA,CAASN,GAAA,CAAAzB,oBAAA,EAAsB;UAAA,EAAC;UAACtC,EAAA,CAAAG,YAAA,EAAS;UAC/HH,EAAA,CAAAC,cAAA,kBAA6J;UAAjCD,EAAA,CAAAiE,UAAA,mBAAAS,yDAAA;YAAA1E,EAAA,CAAAmE,aAAA,CAAAC,GAAA;YAAA,OAAApE,EAAA,CAAAqE,WAAA,CAASN,GAAA,CAAAzB,oBAAA,EAAsB;UAAA,EAAC;UAGxKtC,EAHyK,CAAAG,YAAA,EAAS,EACrK,EACJ,EACH;;;UAxBkDH,EAAA,CAAAM,SAAA,GAAgC;UAAhCN,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA2E,WAAA,OAAAZ,GAAA,CAAA9C,YAAA,EAAgC;UAQnBjB,EAAA,CAAAM,SAAA,GAAsD;UAAtDN,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA4E,eAAA,IAAAC,GAAA,EAAAd,GAAA,CAAA5C,YAAA,EAAsD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}