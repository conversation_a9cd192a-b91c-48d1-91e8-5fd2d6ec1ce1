{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { StoreRoutingModule } from './store-routing.module';\nimport * as i0 from \"@angular/core\";\nexport let StoreModule = /*#__PURE__*/(() => {\n  class StoreModule {\n    static {\n      this.ɵfac = function StoreModule_Factory(t) {\n        return new (t || StoreModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: StoreModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, StoreRoutingModule]\n      });\n    }\n  }\n  return StoreModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}