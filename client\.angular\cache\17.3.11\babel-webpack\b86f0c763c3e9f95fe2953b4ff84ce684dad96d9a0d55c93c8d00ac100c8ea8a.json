{"ast": null, "code": "'use strict';\n\nvar $defineProperty = require('es-define-property');\nvar hasPropertyDescriptors = function hasPropertyDescriptors() {\n  return !!$defineProperty;\n};\nhasPropertyDescriptors.hasArrayLengthDefineBug = function hasArrayLengthDefineBug() {\n  // node v0.6 has a bug where array lengths can be Set but not Defined\n  if (!$defineProperty) {\n    return null;\n  }\n  try {\n    return $defineProperty([], 'length', {\n      value: 1\n    }).length !== 1;\n  } catch (e) {\n    // In Firefox 4-22, defining length on an array throws an exception.\n    return true;\n  }\n};\nmodule.exports = hasPropertyDescriptors;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}