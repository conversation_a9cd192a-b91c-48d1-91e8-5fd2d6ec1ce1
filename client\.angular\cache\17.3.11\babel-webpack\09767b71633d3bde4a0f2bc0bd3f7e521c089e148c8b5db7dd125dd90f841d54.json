{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ServiceTicketsComponent } from './service-tickets.component';\nimport { AccountActivitiesComponent } from '../account/account-details/account-activities/account-activities.component';\nimport { AccountAiInsightsComponent } from '../account/account-details/account-ai-insights/account-ai-insights.component';\nimport { AccountAttachmentsComponent } from '../account/account-details/account-attachments/account-attachments.component';\nimport { AccountContactsComponent } from '../account/account-details/account-contacts/account-contacts.component';\nimport { AccountDetailsComponent } from '../account/account-details/account-details.component';\nimport { AccountNotesComponent } from '../account/account-details/account-notes/account-notes.component';\nimport { AccountOpportunitiesComponent } from '../account/account-details/account-opportunities/account-opportunities.component';\nimport { AccountOrganizationDataComponent } from '../account/account-details/account-organization-data/account-organization-data.component';\nimport { AccountOverviewComponent } from '../account/account-details/account-overview/account-overview.component';\nimport { AccountRelationshipsComponent } from '../account/account-details/account-relationships/account-relationships.component';\nimport { AccountSalesOrderDetailsComponent } from '../account/account-details/account-sales-orders/account-sales-order-details/account-sales-order-details.component';\nimport { AccountSalesOrdersComponent } from '../account/account-details/account-sales-orders/account-sales-orders.component';\nimport { AccountSalesQuoteDetailsComponent } from '../account/account-details/account-sales-quotes/account-sales-quote-details/account-sales-quote-details.component';\nimport { AccountSalesQuotesComponent } from '../account/account-details/account-sales-quotes/account-sales-quotes.component';\nimport { AccountSalesTeamComponent } from '../account/account-details/account-sales-team/account-sales-team.component';\nimport { AccountTicketsComponent } from '../account/account-details/account-tickets/account-tickets.component';\nimport { AccountInvoicesComponent } from '../account/account-details/account-invoices/account-invoices.component';\nimport { AccountReturnsComponent } from '../account/account-details/account-returns/account-returns.component';\nimport { ReturnOrderDetailsComponent } from '../account/account-details/account-returns/return-order-details/return-order-details.component';\nimport { AccountCreditMemoComponent } from '../account/account-details/account-credit-memo/account-credit-memo.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: ':ticket-id',\n  component: ServiceTicketsComponent,\n  children: [{\n    path: ':id',\n    component: AccountDetailsComponent,\n    data: {\n      hideBreadCrumbs: true\n    },\n    children: [{\n      path: 'overview',\n      component: AccountOverviewComponent\n    }, {\n      path: 'contacts',\n      component: AccountContactsComponent\n    }, {\n      path: 'sales-team',\n      component: AccountSalesTeamComponent\n    }, {\n      path: 'opportunities',\n      component: AccountOpportunitiesComponent\n    }, {\n      path: 'ai-insights',\n      component: AccountAiInsightsComponent\n    }, {\n      path: 'organization-data',\n      component: AccountOrganizationDataComponent\n    }, {\n      path: 'attachments',\n      component: AccountAttachmentsComponent\n    }, {\n      path: 'notes',\n      component: AccountNotesComponent\n    }, {\n      path: 'activities',\n      component: AccountActivitiesComponent\n    }, {\n      path: 'relationships',\n      component: AccountRelationshipsComponent\n    }, {\n      path: 'tickets',\n      component: AccountTicketsComponent\n    }, {\n      path: 'sales-quotes',\n      component: AccountSalesQuotesComponent\n    }, {\n      path: 'sales-quotes/:id',\n      component: AccountSalesQuoteDetailsComponent\n    }, {\n      path: 'sales-orders',\n      component: AccountSalesOrdersComponent\n    }, {\n      path: 'sales-orders/:id',\n      component: AccountSalesOrderDetailsComponent\n    }, {\n      path: 'invoices',\n      component: AccountInvoicesComponent\n    }, {\n      path: 'returns',\n      component: AccountReturnsComponent\n    }, {\n      path: \"return-order/:returnOrderId/:refDocId\",\n      component: ReturnOrderDetailsComponent\n    }, {\n      path: \"credit-memos\",\n      component: AccountCreditMemoComponent\n    }, {\n      path: '',\n      redirectTo: 'overview',\n      pathMatch: 'full'\n    }, {\n      path: '**',\n      redirectTo: 'overview',\n      pathMatch: 'full'\n    }]\n  }]\n}];\nexport let ServiceTicketsRoutingModule = /*#__PURE__*/(() => {\n  class ServiceTicketsRoutingModule {\n    static {\n      this.ɵfac = function ServiceTicketsRoutingModule_Factory(t) {\n        return new (t || ServiceTicketsRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ServiceTicketsRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return ServiceTicketsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}