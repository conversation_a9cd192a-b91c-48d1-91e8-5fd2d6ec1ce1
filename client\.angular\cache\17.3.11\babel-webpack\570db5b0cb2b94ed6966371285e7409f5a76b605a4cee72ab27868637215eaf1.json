{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"./layout/service/app.layout.service\";\nimport * as i3 from \"@angular/router\";\nexport class StoreComponent {\n  constructor(primengConfig, renderer, layoutService) {\n    this.primengConfig = primengConfig;\n    this.renderer = renderer;\n    this.layoutService = layoutService;\n  }\n  ngOnInit() {\n    // Inject theme \n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\n    const link = this.renderer.createElement('link');\n    this.renderer.setAttribute(link, 'id', 'theme-link');\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\n    this.renderer.setAttribute(link, 'type', 'text/css');\n    this.renderer.setAttribute(link, 'href', href);\n    // Append the link tag to the head of the document\n    this.renderer.appendChild(document.head, link);\n    this.primengConfig.ripple = true; //enables core ripple functionality\n    //optional configuration with the default configuration\n    const config = {\n      ripple: false,\n      //toggles ripple on and off\n      menuMode: 'reveal',\n      //layout mode of the menu, valid values are \"static\", \"overlay\", \"slim\", \"horizontal\", \"drawer\" and \"reveal\"\n      colorScheme: 'light',\n      //color scheme of the template, valid values are \"light\" and \"dark\"\n      theme: 'snjya',\n      //default component theme for PrimeNG\n      scale: 14 //size of the body font size to scale the whole application\n    };\n    this.layoutService.config.set(config);\n  }\n  ngOnDestroy() {\n    // Find and remove the link tag when the component is destroyed\n    const link = document.getElementById('theme-link');\n    if (link) {\n      link.remove();\n    }\n  }\n  static {\n    this.ɵfac = function StoreComponent_Factory(t) {\n      return new (t || StoreComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.LayoutService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoreComponent,\n      selectors: [[\"app-store\"]],\n      decls: 1,\n      vars: 0,\n      template: function StoreComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i3.RouterOutlet],\n      styles: [\"{\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n  .bg-light-blue {\\n  background: #c3dbff !important;\\n}\\n  .object-fit-contain {\\n  object-fit: contain;\\n}\\n  .object-fit-cover {\\n  object-fit: cover;\\n}\\n  .transition-03 {\\n  transition: all 0.3s ease-in-out;\\n}\\n  .h-36rem {\\n  height: 36rem !important;\\n}\\n  .h-34rem {\\n  height: 34rem !important;\\n}\\n  .surface-b {\\n  background: var(--surface-b) !important;\\n}\\n  .h-3-3rem {\\n  height: 3.3rem;\\n}\\n  .d-grid {\\n  display: grid !important;\\n}\\n  .w-fit {\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n  .min-w-3rem {\\n  min-width: 3rem !important;\\n}\\n  .min-w-16 {\\n  min-width: 16rem !important;\\n}\\n  .min-w-14 {\\n  min-width: 14rem !important;\\n}\\n  .min-h-14 {\\n  min-height: 14rem !important;\\n}\\n  .min-h-18 {\\n  min-height: 18rem !important;\\n}\\n  .min-h-30 {\\n  min-height: 30rem !important;\\n}\\n  .font-900 {\\n  font-weight: 900 !important;\\n}\\n  .surface-b {\\n  background: var(--surface-b) !important;\\n}\\n  .object-fit-contain {\\n  object-fit: contain;\\n}\\n  .transition-03 {\\n  transition: all 0.3s ease-in-out;\\n}\\n  .p-datatable-wrapper thead p-sorticon svg {\\n  color: var(--white);\\n}\\n  .header-title:before,   .left-border:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  top: 0;\\n  bottom: 0;\\n  margin: auto;\\n  width: 5px;\\n  height: 24px;\\n  background: var(--primary-color);\\n  border-radius: 50px;\\n}\\n  .layout-sidebar {\\n  width: 20rem;\\n}\\n  .layout-content-wrapper {\\n  background: var(--surface-0);\\n}\\n  .bg-whight-light {\\n  background: #f6f7f9;\\n}\\n  .all-overview-body {\\n  min-height: calc(100vh - 90px);\\n}\\n  .all-overview-body .p-galleria .p-galleria-item-nav {\\n  color: var(--text-color) !important;\\n  background: var(--surface-0) !important;\\n  width: 3rem !important;\\n  height: 3rem !important;\\n  transform: translateY(-50%);\\n  z-index: 99;\\n}\\n  .all-overview-body .p-galleria .p-galleria-item-nav .p-icon-wrapper .p-icon {\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n  .all-overview-body .card-list .v-details-list .v-details-box .text {\\n  min-width: 120px;\\n}\\n  .all-overview-body p-table table thead th {\\n  height: 44px;\\n  background: var(--primary-color);\\n  color: var(--surface-0);\\n}\\n  .all-overview-body p-table table tbody td {\\n  height: 44px;\\n}\\n  .all-overview-body .v-details-list .v-details-box .text {\\n  min-width: 182px;\\n  min-width: 182px;\\n}\\n  .all-overview-body .order-details-list {\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n}\\n  .all-overview-body .order-details-list {\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n}\\n  .p-inputtext {\\n  appearance: auto !important;\\n}\\n  .border-left-5 {\\n  border-left: 5px solid var(--orange-200);\\n}\\n  .p-calendar {\\n  display: flex;\\n}\\n  .p-calendar .p-button-icon-only {\\n  width: 3rem;\\n}\\n  .max-w-1200 {\\n  max-width: 1200px;\\n}\\n  .text-shadow-l-blue {\\n  text-shadow: 0 2px 6px rgba(0, 63, 147, 0.8);\\n}\\n  .h-32rem {\\n  height: 32rem !important;\\n}\\n  .h-2-8rem {\\n  height: 2.8rem !important;\\n}\\n  .p-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-text {\\n  color: var(--text-color);\\n  font-weight: 600;\\n}\\n  p-paginator .p-paginator {\\n  padding: 20px 0;\\n  margin: 1.5rem 0 0 0;\\n  border-top: 1px solid var(--surface-d);\\n  border-radius: 0;\\n}\\n  p-paginator .p-paginator button {\\n  width: 3rem;\\n  height: 2rem;\\n  border-radius: 0.3rem;\\n  border: 1px solid var(--surface-c);\\n}\\n  p-paginator .p-paginator p-dropdown {\\n  display: none;\\n}\\n  .table-sec tbody:before {\\n  line-height: 20px;\\n  content: \\\"_\\\";\\n  color: transparent;\\n  display: block;\\n}\\n  .table-sec tbody tr:nth-child(odd) td {\\n  background: var(--surface-b);\\n}\\n  .table-sec thead th .p-checkbox .p-checkbox-box.p-highlight {\\n  border-color: var(--surface-0);\\n}\\n  .table-sec thead th:last-child {\\n  border-top-right-radius: 0.5rem !important;\\n  border-bottom-right-radius: 0.5rem !important;\\n}\\n  .table-sec tbody td:last-child {\\n  border-top-right-radius: 0.5rem !important;\\n  border-bottom-right-radius: 0.5rem !important;\\n}\\n  .p-datatable-scrollable > .p-datatable-wrapper {\\n  padding-bottom: 12px;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav {\\n  padding: 0 16px;\\n  border-color: var(--surface-100);\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li {\\n  position: relative;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  right: -1px;\\n  top: 0;\\n  bottom: 0;\\n  margin: auto;\\n  background: var(--surface-50);\\n  width: 1px;\\n  height: 20px;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link {\\n  border: none;\\n  padding: 0;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link {\\n  padding: 8px 14px;\\n  min-height: 40px;\\n  color: var(--gray-600);\\n  gap: 0 6px;\\n  border-radius: 10px 10px 0 0;\\n  cursor: pointer;\\n  font-weight: 500;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link:hover {\\n  color: var(--primary-color);\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link.active-tab {\\n  background: #f6f7f9;\\n  border: 2px solid var(--surface-100);\\n  border-bottom: none;\\n  font-weight: 600;\\n  color: var(--gray-800);\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav .p-tabview-ink-bar {\\n  display: none !important;\\n}\\n  .details-tabs-list .p-tabview-panels {\\n  display: none;\\n}\\n  .details-tabs-list .p-tabview-nav-btn.p-link {\\n  background: var(--surface-0);\\n}\\n  .details-tabs-result {\\n  min-height: calc(100vh - 192px);\\n}\\n  .layout-sidebar .layout-menu li:nth-child(2) .layout-menuitem-root-text {\\n  margin: 12px 0;\\n  padding: 12px 0;\\n  border-top: 1px solid var(--surface-c);\\n  font-size: 14px;\\n  font-weight: 600;\\n}\\n  .card-heading h4 {\\n  margin-left: 10px !important;\\n  min-height: 30px;\\n  align-items: center;\\n}\\n  .sidebar-hide {\\n  display: none;\\n}\\n  .arrow-btn {\\n  top: 29px;\\n  left: 0;\\n  z-index: 99;\\n}\\n  .arrow-round {\\n  transform: rotate(180deg);\\n}\\n  .layout-sidebar .sidebar-header .app-logo .arrow-icon {\\n  transform: rotateY(180deg);\\n  position: absolute;\\n  right: 11px;\\n  top: 14px;\\n  color: var(--primary-color);\\n}\\n  .layout-container.layout-reveal.layout-sidebar-active .layout-sidebar .sidebar-header .app-logo .arrow-icon {\\n  display: none !important;\\n}\\n  .p-dropdown-label {\\n  display: flex;\\n  align-items: center;\\n}\\n  .filter-sec p-dropdown .p-dropdown-label,   .filter-sec p-dropdown .p-dropdown-trigger {\\n  color: var(--primary-700);\\n}\\n  .filter-sec .table-multiselect-dropdown .p-overlay.p-component {\\n  left: -140px !important;\\n}\\n  .filter-sec .table-multiselect-dropdown .p-multiselect-items-wrapper p-multiselectitem li {\\n  margin: 0 0 2px 0 !important;\\n  min-width: 164px;\\n}\\n  .filter-sec .table-multiselect-dropdown .p-multiselect .p-multiselect-label-container {\\n  display: none;\\n}\\n  .filter-sec .table-multiselect-dropdown .p-multiselect .p-multiselect-label {\\n  color: var(--primary-700);\\n  align-items: center;\\n  display: flex;\\n  text-transform: capitalize;\\n}\\n  .filter-sec .table-multiselect-dropdown .p-multiselect-trigger {\\n  position: relative;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n  .filter-sec .table-multiselect-dropdown .p-multiselect-trigger chevrondownicon {\\n  display: none;\\n}\\n  .filter-sec .table-multiselect-dropdown .p-multiselect-trigger::before {\\n  position: absolute;\\n  content: \\\"\\\\e5d4\\\";\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  font-family: \\\"Material Symbols Rounded\\\";\\n  font-size: 1.5rem;\\n  color: var(--primary-700);\\n  line-height: 22px;\\n}\\n  .scrollable-table table thead th {\\n  min-width: 150px;\\n  white-space: nowrap;\\n}\\n  .scrollable-table table thead th.table-checkbox {\\n  min-width: 32px;\\n}\\n  .scrollable-table table tbody tr:nth-child(odd) td:first-child {\\n  background: #f2f2f5;\\n}\\n  .scrollable-table table tbody tr:nth-child(odd) td:nth-child(2) {\\n  background: #f2f2f5;\\n}\\n  .scrollable-table table tbody td {\\n  white-space: nowrap;\\n}\\n  .all-page-details {\\n  width: calc(100% - 28rem);\\n}\\n  .layout-dark .bg-whight-light {\\n  background: var(--surface-0);\\n}\\n  .layout-dark .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link.active-tab {\\n  background: var(--surface-0);\\n  color: var(--text-color);\\n}\\n  .layout-dark .scrollable-table table tbody tr:nth-child(odd) td:first-child {\\n  background: var(--surface-b);\\n}\\n  .layout-dark .scrollable-table table tbody tr:nth-child(odd) td:nth-child(2) {\\n  background: var(--surface-b);\\n}\\n  .layout-dark .table-sec tbody tr:nth-child(odd) td {\\n  background: rgba(255, 255, 255, 0.05);\\n}\\n  .p-sortable-column-badge {\\n  display: none !important;\\n}\\n  .note-text {\\n  max-width: 320px;\\n  width: 320px;\\n  white-space: normal !important;\\n}\\n  .multiselect-dropdown {\\n  padding: 0;\\n  height: 3rem;\\n}\\n  .multiselect-dropdown .ng-select-container {\\n  background: none !important;\\n  border: none;\\n  height: 3rem !important;\\n  align-items: center;\\n}\\n  .multiselect-dropdown .ng-select-container .ng-value-container {\\n  height: 2rem;\\n}\\n  .multiselect-dropdown .ng-select-container .ng-value-container .ng-input {\\n  top: 0 !important;\\n  bottom: 0;\\n  margin: auto;\\n  display: flex;\\n  align-items: center;\\n}\\n  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option input {\\n  min-width: 20px;\\n}\\n  .confirm-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .confirm-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .confirm-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .confirm-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n  .confirm-popup .p-dialog .p-dialog-footer {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n  .confirm-popup .p-dialog .p-dialog-footer .p-button {\\n  height: 2.5rem !important;\\n  width: 8rem !important;\\n  gap: 0.25rem !important;\\n  font-weight: 500 !important;\\n  border: none;\\n  border-radius: 2rem;\\n  justify-content: center;\\n}\\n  .confirm-popup .p-dialog .p-dialog-footer .p-button.p-confirm-dialog-reject {\\n  color: var(--red-500) !important;\\n  background-color: var(--red-100) !important;\\n}\\n  .confirm-popup .p-dialog .p-dialog-footer .p-button.p-confirm-dialog-accept {\\n  color: var(--primary-700) !important;\\n  background-color: #c3dbff !important;\\n}\\n  .confirm-popup .p-dialog .p-dialog-footer .p-button .p-button-label {\\n  flex: none !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["StoreComponent", "constructor", "primengConfig", "renderer", "layoutService", "ngOnInit", "href", "link", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "document", "head", "ripple", "config", "menuMode", "colorScheme", "theme", "scale", "set", "ngOnDestroy", "getElementById", "remove", "i0", "ɵɵdirectiveInject", "i1", "PrimeNGConfig", "Renderer2", "i2", "LayoutService", "selectors", "decls", "vars", "template", "StoreComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\store.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\store.component.html"], "sourcesContent": ["import { Component, Renderer2 } from '@angular/core';\r\nimport { PrimeNGConfig } from 'primeng/api';\r\nimport { AppConfig, LayoutService } from './layout/service/app.layout.service';\r\n\r\n@Component({\r\n  selector: 'app-store',\r\n  templateUrl: './store.component.html',\r\n  styleUrl: './store.component.scss',\r\n})\r\nexport class StoreComponent {\r\n  constructor(\r\n    private primengConfig: PrimeNGConfig,\r\n    private renderer: Renderer2,\r\n    private layoutService: LayoutService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Inject theme \r\n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\r\n    const link = this.renderer.createElement('link');\r\n    this.renderer.setAttribute(link, 'id', 'theme-link');\r\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\r\n    this.renderer.setAttribute(link, 'type', 'text/css');\r\n    this.renderer.setAttribute(link, 'href', href);\r\n\r\n    // Append the link tag to the head of the document\r\n    this.renderer.appendChild(document.head, link);\r\n\r\n    this.primengConfig.ripple = true; //enables core ripple functionality\r\n    //optional configuration with the default configuration\r\n    const config: AppConfig = {\r\n      ripple: false, //toggles ripple on and off\r\n      menuMode: 'reveal', //layout mode of the menu, valid values are \"static\", \"overlay\", \"slim\", \"horizontal\", \"drawer\" and \"reveal\"\r\n      colorScheme: 'light', //color scheme of the template, valid values are \"light\" and \"dark\"\r\n      theme: 'snjya', //default component theme for PrimeNG\r\n      scale: 14, //size of the body font size to scale the whole application\r\n    };\r\n    this.layoutService.config.set(config);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Find and remove the link tag when the component is destroyed\r\n    const link = document.getElementById('theme-link');\r\n    if (link) {\r\n      link.remove();\r\n    }\r\n  }\r\n}\r\n", "<router-outlet></router-outlet>"], "mappings": ";;;;AASA,OAAM,MAAOA,cAAc;EACzBC,YACUC,aAA4B,EAC5BC,QAAmB,EACnBC,aAA4B;IAF5B,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;EACpB;EAEHC,QAAQA,CAAA;IACN;IACA,MAAMC,IAAI,GAAG,wDAAwD;IACrE,MAAMC,IAAI,GAAG,IAAI,CAACJ,QAAQ,CAACK,aAAa,CAAC,MAAM,CAAC;IAChD,IAAI,CAACL,QAAQ,CAACM,YAAY,CAACF,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC;IACpD,IAAI,CAACJ,QAAQ,CAACM,YAAY,CAACF,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC;IACrD,IAAI,CAACJ,QAAQ,CAACM,YAAY,CAACF,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC;IACpD,IAAI,CAACJ,QAAQ,CAACM,YAAY,CAACF,IAAI,EAAE,MAAM,EAAED,IAAI,CAAC;IAE9C;IACA,IAAI,CAACH,QAAQ,CAACO,WAAW,CAACC,QAAQ,CAACC,IAAI,EAAEL,IAAI,CAAC;IAE9C,IAAI,CAACL,aAAa,CAACW,MAAM,GAAG,IAAI,CAAC,CAAC;IAClC;IACA,MAAMC,MAAM,GAAc;MACxBD,MAAM,EAAE,KAAK;MAAE;MACfE,QAAQ,EAAE,QAAQ;MAAE;MACpBC,WAAW,EAAE,OAAO;MAAE;MACtBC,KAAK,EAAE,OAAO;MAAE;MAChBC,KAAK,EAAE,EAAE,CAAE;KACZ;IACD,IAAI,CAACd,aAAa,CAACU,MAAM,CAACK,GAAG,CAACL,MAAM,CAAC;EACvC;EAEAM,WAAWA,CAAA;IACT;IACA,MAAMb,IAAI,GAAGI,QAAQ,CAACU,cAAc,CAAC,YAAY,CAAC;IAClD,IAAId,IAAI,EAAE;MACRA,IAAI,CAACe,MAAM,EAAE;IACf;EACF;;;uBArCWtB,cAAc,EAAAuB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,SAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAd7B,cAAc;MAAA8B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT3BZ,EAAA,CAAAc,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}