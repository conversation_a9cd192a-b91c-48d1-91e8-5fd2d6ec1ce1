{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./app.menu.service\";\nfunction AppMenuComponent_ng_container_1_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const item_r2 = ctx_r0.$implicit;\n    const i_r3 = ctx_r0.index;\n    i0.ɵɵproperty(\"item\", item_r2)(\"index\", i_r3)(\"root\", true);\n  }\n}\nfunction AppMenuComponent_ng_container_1_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 5);\n  }\n}\nfunction AppMenuComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AppMenuComponent_ng_container_1_li_1_Template, 1, 3, \"li\", 2)(2, AppMenuComponent_ng_container_1_li_2_Template, 1, 0, \"li\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r2.separator);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.separator);\n  }\n}\nexport let AppMenuComponent = /*#__PURE__*/(() => {\n  class AppMenuComponent {\n    ngOnInit() {\n      this.model = [{\n        // label: 'Apps',\n        icon: 'pi pi-th-large',\n        items: [{\n          label: 'Dashboard',\n          icon: 'space_dashboard',\n          routerLink: ['/store/dashboard'],\n          command: event => this.setActiveMenu(event.item.label)\n        }, {\n          label: 'Home',\n          icon: 'home',\n          routerLink: ['/store'],\n          command: event => this.setActiveMenu(event.item.label)\n        }, {\n          label: 'Prospects',\n          icon: 'person_search',\n          routerLink: ['/store/prospects'],\n          command: event => this.setActiveMenu(event.item.label)\n        }, {\n          label: 'Account',\n          icon: 'person',\n          routerLink: ['/store/account'],\n          command: event => this.setActiveMenu(event.item.label)\n        }, {\n          label: 'Contacts',\n          icon: 'contact_phone',\n          routerLink: ['/store/contacts'],\n          command: event => this.setActiveMenu(event.item.label)\n        }, {\n          label: 'Activities',\n          icon: 'diversity_2',\n          routerLink: ['/store/activities'],\n          command: event => this.setActiveMenu(event.item.label),\n          items: [\n          // {\n          //   label: 'Appointments',\n          //   icon: 'event',\n          //   routerLink: ['/store/activities/appointments'],\n          // },\n          // {\n          //   label: 'E-Mails',\n          //   icon: 'email',\n          //   routerLink: ['/store/activities/emails'],\n          // },\n          {\n            label: 'Sales Call',\n            icon: 'support_agent',\n            routerLink: ['/store/activities/calls']\n          }\n          // {\n          //   label: 'Tasks',\n          //   icon: 'task',\n          //   routerLink: ['/store/activities/tasks'],\n          // },\n          ]\n        }, {\n          label: 'Opportunities',\n          icon: 'wb_incandescent',\n          routerLink: ['/store/opportunities'],\n          command: event => this.setActiveMenu(event.item.label)\n        }, {\n          label: 'Org Structures',\n          icon: 'account_tree',\n          routerLink: ['/store/organization'],\n          command: event => this.setActiveMenu(event.item.label)\n        },\n        // {\n        //   label: 'Ai Insights',\n        //   icon: 'analytics',\n        //   routerLink: ['/store/ai-insights'],\n        //   command: (event: any) => this.setActiveMenu(event.item.label),\n        // },\n        {\n          label: 'Sales Quotes',\n          icon: 'request_quote',\n          routerLink: ['/store/sales-quotes'],\n          command: event => this.setActiveMenu(event.item.label)\n        }, {\n          label: 'Sales Orders',\n          icon: 'list_alt',\n          routerLink: ['/store/sales-orders'],\n          command: event => this.setActiveMenu(event.item.label)\n        }, {\n          label: 'Competitors',\n          icon: 'groups',\n          routerLink: ['/store/competitors'],\n          command: event => this.setActiveMenu(event.item.label)\n        }, {\n          label: 'Migration Cockpit',\n          icon: 'swap_vert',\n          items: [{\n            label: 'Import',\n            icon: 'upload',\n            routerLink: ['/store/import'],\n            command: event => this.setActiveMenu(event.item.label)\n          }, {\n            label: 'Export',\n            icon: 'download',\n            routerLink: ['/store/export'],\n            command: event => this.setActiveMenu(event.item.label)\n          }]\n        }]\n      }, {\n        label: 'Services Tickets Menu List',\n        items: [{\n          label: 'Identify Account',\n          icon: 'person_search',\n          routerLink: ['/store/identify-account'],\n          command: event => this.setActiveMenu(event.item.label)\n        }, {\n          label: 'Account',\n          icon: 'person',\n          routerLink: ['/store/account'],\n          command: event => this.setActiveMenu(event.item.label)\n        }, {\n          label: 'Contacts',\n          icon: 'contact_phone',\n          routerLink: ['/store/contacts'],\n          command: event => this.setActiveMenu(event.item.label)\n        }, {\n          label: 'Services Ticket',\n          icon: 'phone_in_talk',\n          routerLink: ['/store/service-tickets'],\n          command: event => this.setActiveMenu(event.item.label)\n        }, {\n          label: 'Sales Quotes',\n          icon: 'request_quote',\n          routerLink: ['/store/sales-quotes'],\n          command: event => this.setActiveMenu(event.item.label)\n        }, {\n          label: 'Sales Orders',\n          icon: 'list_alt',\n          routerLink: ['/store/sales-orders'],\n          command: event => this.setActiveMenu(event.item.label)\n        }]\n      }];\n    }\n    constructor(menuService) {\n      this.menuService = menuService;\n      this.model = [];\n    }\n    setActiveMenu(menuName) {\n      this.menuService.setActiveMenu(menuName);\n    }\n    static {\n      this.ɵfac = function AppMenuComponent_Factory(t) {\n        return new (t || AppMenuComponent)(i0.ɵɵdirectiveInject(i1.MenuService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppMenuComponent,\n        selectors: [[\"app-menu\"]],\n        decls: 2,\n        vars: 1,\n        consts: [[1, \"layout-menu\"], [4, \"ngFor\", \"ngForOf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\", 4, \"ngIf\"], [\"class\", \"menu-separator\", 4, \"ngIf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\"], [1, \"menu-separator\"]],\n        template: function AppMenuComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"ul\", 0);\n            i0.ɵɵtemplate(1, AppMenuComponent_ng_container_1_Template, 3, 2, \"ng-container\", 1);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.model);\n          }\n        },\n        encapsulation: 2\n      });\n    }\n  }\n  return AppMenuComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}