<p-dialog [(visible)]="visible" (onHide)="hideDialog()" [modal]="true" [position]="'right'" [draggable]="false"
    class="opportunity-popup">
    <ng-template pTemplate="header">
        <h4>Opportunities</h4>
    </ng-template>

    <form [formGroup]="FollowUpForm" class="relative flex flex-column gap-1">
        <div class="field grid mt-0 text-base">
            <div class="col-12 lg:col-4 md:col-6 sm:col-12">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Name">
                    <span class="material-symbols-rounded">badge</span>Name
                    <span class="text-red-500">*</span>
                </label>
                <input pInputText id="name" type="text" formControlName="name" placeholder="Name"
                    class="p-inputtext p-component p-element h-3rem w-full"
                    [ngClass]="{ 'is-invalid': submitted && f['name'].errors }" />
                <div *ngIf="submitted && f['name'].errors" class="p-error">
                    <div *ngIf="submitted && f['name'].errors['required']">
                        Name is required.
                    </div>
                </div>
            </div>

            <div class="col-12 lg:col-4 md:col-6 sm:col-12">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Account">
                    <span class="material-symbols-rounded">account_circle</span>Account
                    <span class="text-red-500">*</span>
                </label>
                <ng-select pInputText [items]="accounts$ | async" bindLabel="bp_full_name" bindValue="bp_id"
                    [hideSelected]="true" [loading]="accountLoading" [minTermLength]="0"
                    formControlName="prospect_party_id" [typeahead]="accountInput$" [maxSelectedItems]="10"
                    appendTo="body" [ngClass]="{ 'is-invalid': submitted && f['prospect_party_id'].errors }"
                    [class]="'multiselect-dropdown p-inputtext p-component p-element'"
                    placeholder="Search for an account">
                    <ng-template ng-option-tmp let-item="item">
                        <span>{{ item.bp_id }}</span>
                        <span *ngIf="item.bp_full_name"> : {{ item.bp_full_name }}</span>
                    </ng-template>
                </ng-select>
                <div *ngIf="submitted && f['prospect_party_id'].errors" class="p-error">
                    <div *ngIf="submitted && f['prospect_party_id'].errors['required']">
                        Account is required.
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Primary Contact">
                    <span class="material-symbols-rounded">person</span>Primary Contact
                    <span class="text-red-500">*</span>
                </label>
                <ng-select pInputText [items]="contacts$ | async" bindLabel="bp_full_name" bindValue="bp_id"
                    [hideSelected]="true" [loading]="contactLoading" [minTermLength]="0"
                    formControlName="primary_contact_party_id" [typeahead]="contactInput$" [maxSelectedItems]="10"
                    appendTo="body" [closeOnSelect]="false"
                    [ngClass]="{ 'is-invalid': submitted && f['primary_contact_party_id'].errors }"
                    [class]="'multiselect-dropdown p-inputtext p-component p-element'" placeholder="Select a Contact">
                    <ng-template ng-option-tmp let-item="item">
                        <div class="flex align-items-center gap-2">
                            <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>
                            <span *ngIf="item.email"> : {{ item.email }}</span>
                            <span *ngIf="item.mobile"> : {{ item.mobile }}</span>
                        </div>
                    </ng-template>
                </ng-select>
                <div *ngIf="submitted && f['primary_contact_party_id'].errors" class="p-error">
                    <div *ngIf="submitted && f['primary_contact_party_id'].errors['required']">
                        Contact is required.
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Source">
                    <span class="material-symbols-rounded">source</span>Source
                </label>
                <p-dropdown [options]="dropdowns['opportunitySource']" formControlName="origin_type_code"
                    placeholder="Select a Source" optionLabel="label" optionValue="value" styleClass="h-3rem w-full">
                </p-dropdown>
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Expected Value">
                    <span class="material-symbols-rounded">show_chart</span>Expected Value
                    <span class="text-red-500">*</span>
                </label>
                <input pInputText id="expected_revenue_amount" type="text" formControlName="expected_revenue_amount"
                    placeholder="Expected Value" class="p-inputtext p-component p-element h-3rem w-full"
                    [ngClass]="{ 'is-invalid': submitted && f['expected_revenue_amount'].errors }" />
                <div *ngIf="submitted && f['expected_revenue_amount'].errors" class="p-error">
                    <div *ngIf="
                                submitted &&
                                f['expected_revenue_amount'].errors &&
                                f['expected_revenue_amount'].errors['required']
                              ">
                        Expected Value is required.
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Create Date">
                    <span class="material-symbols-rounded">schedule</span>Create Date
                </label>
                <p-calendar formControlName="expected_revenue_start_date" inputId="calendar-12h" [showTime]="true"
                    hourFormat="12" [showIcon]="true" styleClass="h-3rem w-full" appendTo="body"
                    placeholder="Create Date" />
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Expected Decision Date">
                    <span class="material-symbols-rounded">schedule</span>Expected Decision Date
                </label>
                <p-calendar formControlName="expected_revenue_end_date" inputId="calendar-12h" [showTime]="true"
                    hourFormat="12" [showIcon]="true" styleClass="h-3rem w-full" appendTo="body"
                    placeholder="Expected Decision Date" />
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Status">
                    <span class="material-symbols-rounded">check_circle</span>Status
                    <span class="text-red-500">*</span>
                </label>
                <p-dropdown [options]="dropdowns['opportunityStatus']" formControlName="life_cycle_status_code"
                    placeholder="Select a Status" optionLabel="label" optionValue="value" styleClass="h-3rem w-full"
                    [ngClass]="{ 'is-invalid': submitted && f['life_cycle_status_code'].errors }">
                </p-dropdown>
                <div *ngIf="submitted && f['life_cycle_status_code'].errors" class="p-error">
                    <div *ngIf="submitted && f['life_cycle_status_code'].errors['required']">
                        Status is required.
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-6 sm:col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Probability">
                    <span class="material-symbols-rounded">percent</span>Probability
                </label>
                <input pInputText id="probability_percent" type="text" formControlName="probability_percent"
                    placeholder="Probability" class="p-inputtext p-component p-element h-3rem w-full" />
            </div>

            <div class="col-12 lg:col-4 md:col-6 sm:col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Category">
                    <span class="material-symbols-rounded">category</span>Category
                </label>
                <p-dropdown [options]="dropdowns['opportunityCategory']" formControlName="group_code"
                    placeholder="Select a Category" optionLabel="label" optionValue="value" styleClass="h-3rem w-full">
                </p-dropdown>
            </div>
            <div class="col-12 mt-3">
                <label class="flex align-items-center font-semibold gap-1 mb-1" for="Notes">
                    <span class="material-symbols-rounded">notes</span>Notes
                    <span class="text-red-500">*</span>
                </label>
                <p-editor formControlName="note" placeholder="Enter your note here..." [style]="{ height: '125px' }"
                    [ngClass]="{ 'is-invalid': submitted && f['note'].errors }" />
                <div *ngIf="submitted && f['note'].errors" class="p-error">
                    <div *ngIf="
                                    submitted &&
                                    f['note'].errors &&
                                    f['note'].errors['required']
                                  ">
                        Notes is required.
                    </div>
                </div>
            </div>
        </div>
        <div class="flex align-items-center gap-3 mt-1">
            <button pButton type="button" label="Cancel"
                class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem"
                (click)="visible = false"></button>
            <button pButton type="submit" label="Save" class="p-button-rounded justify-content-center w-9rem h-3rem"
                (click)="onSubmit()"></button>
        </div>
    </form>

</p-dialog>