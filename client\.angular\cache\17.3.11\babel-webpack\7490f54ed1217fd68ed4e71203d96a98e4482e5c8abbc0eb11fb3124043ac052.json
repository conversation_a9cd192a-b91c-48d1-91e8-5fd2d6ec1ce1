{"ast": null, "code": "import countryList from './assets/country.json';\nimport { compare, findEntryByCode } from './utils';\n// Get a country by isoCode.\nfunction getCountryByCode(isoCode) {\n  if (!isoCode) return undefined;\n  return findEntryByCode(countryList, isoCode);\n}\n// Get a list of all countries.\nfunction getAllCountries() {\n  return countryList;\n}\nfunction sortByIsoCode(countries) {\n  return countries.sort((a, b) => {\n    return compare(a, b, entity => {\n      return entity.isoCode;\n    });\n  });\n}\nexport default {\n  getCountryByCode,\n  getAllCountries,\n  sortByIsoCode\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}