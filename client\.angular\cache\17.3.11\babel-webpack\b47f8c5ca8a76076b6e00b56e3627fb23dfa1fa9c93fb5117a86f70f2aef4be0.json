{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { ContactsRoutingModule } from './contacts-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { TableModule } from 'primeng/table';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { TabViewModule } from 'primeng/tabview';\nimport { EditorModule } from 'primeng/editor';\nimport { DialogModule } from 'primeng/dialog';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { CommonFormModule } from '../common-form/common-form.module';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport let ContactsModule = /*#__PURE__*/(() => {\n  class ContactsModule {\n    static {\n      this.ɵfac = function ContactsModule_Factory(t) {\n        return new (t || ContactsModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ContactsModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [MessageService, ConfirmationService],\n        imports: [CommonModule, NgSelectModule, ContactsRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, ReactiveFormsModule, CalendarModule, ButtonModule, CheckboxModule, DialogModule, TabViewModule, AutoCompleteModule, InputTextModule, InputSwitchModule, EditorModule, ToastModule, ConfirmDialogModule, SharedModule, CommonFormModule, MultiSelectModule]\n      });\n    }\n  }\n  return ContactsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}