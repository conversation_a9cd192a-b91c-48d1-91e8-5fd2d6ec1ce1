{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Japanese [ja]\n//! author : LI Long : https://github.com/baryon\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var ja = moment.defineLocale('ja', {\n    eras: [{\n      since: '2019-05-01',\n      offset: 1,\n      name: '令和',\n      narrow: '㋿',\n      abbr: 'R'\n    }, {\n      since: '1989-01-08',\n      until: '2019-04-30',\n      offset: 1,\n      name: '平成',\n      narrow: '㍻',\n      abbr: 'H'\n    }, {\n      since: '1926-12-25',\n      until: '1989-01-07',\n      offset: 1,\n      name: '昭和',\n      narrow: '㍼',\n      abbr: 'S'\n    }, {\n      since: '1912-07-30',\n      until: '1926-12-24',\n      offset: 1,\n      name: '大正',\n      narrow: '㍽',\n      abbr: 'T'\n    }, {\n      since: '1873-01-01',\n      until: '1912-07-29',\n      offset: 6,\n      name: '明治',\n      narrow: '㍾',\n      abbr: 'M'\n    }, {\n      since: '0001-01-01',\n      until: '1873-12-31',\n      offset: 1,\n      name: '西暦',\n      narrow: 'AD',\n      abbr: 'AD'\n    }, {\n      since: '0000-12-31',\n      until: -Infinity,\n      offset: 1,\n      name: '紀元前',\n      narrow: 'BC',\n      abbr: 'BC'\n    }],\n    eraYearOrdinalRegex: /(元|\\d+)年/,\n    eraYearOrdinalParse: function (input, match) {\n      return match[1] === '元' ? 1 : parseInt(match[1] || input, 10);\n    },\n    months: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split('_'),\n    monthsShort: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split('_'),\n    weekdays: '日曜日_月曜日_火曜日_水曜日_木曜日_金曜日_土曜日'.split('_'),\n    weekdaysShort: '日_月_火_水_木_金_土'.split('_'),\n    weekdaysMin: '日_月_火_水_木_金_土'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY/MM/DD',\n      LL: 'YYYY年M月D日',\n      LLL: 'YYYY年M月D日 HH:mm',\n      LLLL: 'YYYY年M月D日 dddd HH:mm',\n      l: 'YYYY/MM/DD',\n      ll: 'YYYY年M月D日',\n      lll: 'YYYY年M月D日 HH:mm',\n      llll: 'YYYY年M月D日(ddd) HH:mm'\n    },\n    meridiemParse: /午前|午後/i,\n    isPM: function (input) {\n      return input === '午後';\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return '午前';\n      } else {\n        return '午後';\n      }\n    },\n    calendar: {\n      sameDay: '[今日] LT',\n      nextDay: '[明日] LT',\n      nextWeek: function (now) {\n        if (now.week() !== this.week()) {\n          return '[来週]dddd LT';\n        } else {\n          return 'dddd LT';\n        }\n      },\n      lastDay: '[昨日] LT',\n      lastWeek: function (now) {\n        if (this.week() !== now.week()) {\n          return '[先週]dddd LT';\n        } else {\n          return 'dddd LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}日/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'y':\n          return number === 1 ? '元年' : number + '年';\n        case 'd':\n        case 'D':\n        case 'DDD':\n          return number + '日';\n        default:\n          return number;\n      }\n    },\n    relativeTime: {\n      future: '%s後',\n      past: '%s前',\n      s: '数秒',\n      ss: '%d秒',\n      m: '1分',\n      mm: '%d分',\n      h: '1時間',\n      hh: '%d時間',\n      d: '1日',\n      dd: '%d日',\n      M: '1ヶ月',\n      MM: '%dヶ月',\n      y: '1年',\n      yy: '%d年'\n    }\n  });\n  return ja;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}