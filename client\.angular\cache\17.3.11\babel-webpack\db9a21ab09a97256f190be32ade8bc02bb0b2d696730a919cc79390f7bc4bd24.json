{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./competitors.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/breadcrumb\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/multiselect\";\nfunction CompetitorsComponent_ng_template_14_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 24);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction CompetitorsComponent_ng_template_14_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 25);\n  }\n}\nfunction CompetitorsComponent_ng_template_14_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 24);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction CompetitorsComponent_ng_template_14_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 25);\n  }\n}\nfunction CompetitorsComponent_ng_template_14_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 26);\n    i0.ɵɵlistener(\"click\", function CompetitorsComponent_ng_template_14_ng_container_8_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 20);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, CompetitorsComponent_ng_template_14_ng_container_8_i_4_Template, 1, 1, \"i\", 21)(5, CompetitorsComponent_ng_template_14_ng_container_8_i_5_Template, 1, 0, \"i\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== col_r5.field);\n  }\n}\nfunction CompetitorsComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 18);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 19);\n    i0.ɵɵlistener(\"click\", function CompetitorsComponent_ng_template_14_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.customSort(\"competitor_id\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 20);\n    i0.ɵɵtext(5, \" ID \");\n    i0.ɵɵtemplate(6, CompetitorsComponent_ng_template_14_i_6_Template, 1, 1, \"i\", 21)(7, CompetitorsComponent_ng_template_14_i_7_Template, 1, 0, \"i\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, CompetitorsComponent_ng_template_14_ng_container_8_Template, 6, 4, \"ng-container\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === \"competitor_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== \"competitor_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction CompetitorsComponent_ng_template_15_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const competitor_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (competitor_r6 == null ? null : competitor_r6.name) || \"-\", \" \");\n  }\n}\nfunction CompetitorsComponent_ng_template_15_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const competitor_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (competitor_r6 == null ? null : competitor_r6.status_code) || \"-\", \" \");\n  }\n}\nfunction CompetitorsComponent_ng_template_15_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 31);\n    i0.ɵɵtemplate(3, CompetitorsComponent_ng_template_15_ng_container_5_ng_container_3_Template, 2, 1, \"ng-container\", 32)(4, CompetitorsComponent_ng_template_15_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 32);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"status_code\");\n  }\n}\nfunction CompetitorsComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 27)(1, \"td\", 28);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CompetitorsComponent_ng_template_15_ng_container_5_Template, 5, 3, \"ng-container\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const competitor_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", competitor_r6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (competitor_r6 == null ? null : competitor_r6.competitor_id) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction CompetitorsComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 33);\n    i0.ɵɵtext(2, \" No competitor found. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CompetitorsComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 33);\n    i0.ɵɵtext(2, \" Loading competitor data. Please wait. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class CompetitorsComponent {\n  constructor(competitorsservice) {\n    this.competitorsservice = competitorsservice;\n    this.unsubscribe$ = new Subject();\n    this.competitors = [];\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n    this.searchInputChanged = new Subject();\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'name',\n      header: 'Name'\n    }, {\n      field: 'status_code',\n      header: 'Status'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.competitors.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.searchInputChanged.pipe(debounceTime(500),\n    // Adjust delay here (ms)\n    distinctUntilChanged()).subscribe(term => {\n      this.globalSearchTerm = term;\n      this.loadCompetitors({\n        first: 0,\n        rows: 15\n      });\n    });\n    this.breadcrumbitems = [{\n      label: 'Competitors',\n      routerLink: ['/store/competitors']\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/']\n    };\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  loadCompetitors(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    const sortField = event.sortField;\n    const sortOrder = event.sortOrder;\n    this.competitorsservice.getCompetitors(page, pageSize, sortField, sortOrder, this.globalSearchTerm).subscribe({\n      next: response => {\n        this.competitors = response?.data ?? [];\n        this.totalRecords = response?.meta?.pagination.total;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching competitors', error);\n        this.loading = false;\n      }\n    });\n  }\n  onSearchInputChange(event) {\n    const input = event.target.value;\n    this.searchInputChanged.next(input);\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function CompetitorsComponent_Factory(t) {\n      return new (t || CompetitorsComponent)(i0.ɵɵdirectiveInject(i1.CompetitorsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CompetitorsComponent,\n      selectors: [[\"app-competitors\"]],\n      decls: 18,\n      vars: 14,\n      consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Competitor\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onLazyLoad\", \"onColReorder\", \"value\", \"rows\", \"totalRecords\", \"paginator\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"6\", 1, \"border-round-left-lg\", \"pl-3\"]],\n      template: function CompetitorsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"span\", 8)(7, \"input\", 9, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CompetitorsComponent_Template_input_ngModelChange_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function CompetitorsComponent_Template_input_input_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearchInputChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"p-multiSelect\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CompetitorsComponent_Template_p_multiSelect_ngModelChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 12)(12, \"p-table\", 13, 1);\n          i0.ɵɵlistener(\"onLazyLoad\", function CompetitorsComponent_Template_p_table_onLazyLoad_12_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadCompetitors($event));\n          })(\"onColReorder\", function CompetitorsComponent_Template_p_table_onColReorder_12_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onColumnReorder($event));\n          });\n          i0.ɵɵtemplate(14, CompetitorsComponent_ng_template_14_Template, 9, 3, \"ng-template\", 14)(15, CompetitorsComponent_ng_template_15_Template, 6, 3, \"ng-template\", 15)(16, CompetitorsComponent_ng_template_16_Template, 3, 0, \"ng-template\", 16)(17, CompetitorsComponent_ng_template_17_Template, 3, 0, \"ng-template\", 17);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.competitors)(\"rows\", 15)(\"totalRecords\", ctx.totalRecords)(\"paginator\", true)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgSwitch, i2.NgSwitchCase, i3.Breadcrumb, i4.PrimeTemplate, i5.Table, i5.SortableColumn, i5.FrozenColumn, i5.ReorderableColumn, i5.TableCheckbox, i5.TableHeaderCheckbox, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, i7.MultiSelect],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "debounceTime", "distinctUntilChanged", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r2", "sortOrder", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "CompetitorsComponent_ng_template_14_ng_container_8_Template_th_click_1_listener", "col_r5", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "CompetitorsComponent_ng_template_14_ng_container_8_i_4_Template", "CompetitorsComponent_ng_template_14_ng_container_8_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "CompetitorsComponent_ng_template_14_Template_th_click_3_listener", "_r2", "CompetitorsComponent_ng_template_14_i_6_Template", "CompetitorsComponent_ng_template_14_i_7_Template", "CompetitorsComponent_ng_template_14_ng_container_8_Template", "selectedColumns", "competitor_r6", "name", "status_code", "CompetitorsComponent_ng_template_15_ng_container_5_ng_container_3_Template", "CompetitorsComponent_ng_template_15_ng_container_5_ng_container_4_Template", "col_r7", "CompetitorsComponent_ng_template_15_ng_container_5_Template", "competitor_id", "CompetitorsComponent", "constructor", "competitorsservice", "unsubscribe$", "competitors", "totalRecords", "loading", "globalSearchTerm", "searchInputChanged", "_selectedColumns", "cols", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "pipe", "subscribe", "term", "loadCompetitors", "first", "rows", "breadcrumbitems", "label", "routerLink", "home", "icon", "val", "filter", "col", "includes", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "page", "pageSize", "getCompetitors", "next", "response", "meta", "pagination", "total", "error", "console", "onSearchInputChange", "input", "target", "value", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "CompetitorsService", "selectors", "decls", "vars", "consts", "template", "CompetitorsComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "CompetitorsComponent_Template_input_ngModelChange_7_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "CompetitorsComponent_Template_input_input_7_listener", "CompetitorsComponent_Template_p_multiSelect_ngModelChange_10_listener", "CompetitorsComponent_Template_p_table_onLazyLoad_12_listener", "CompetitorsComponent_Template_p_table_onColReorder_12_listener", "CompetitorsComponent_ng_template_14_Template", "CompetitorsComponent_ng_template_15_Template", "CompetitorsComponent_ng_template_16_Template", "CompetitorsComponent_ng_template_17_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\competitors\\competitors.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\competitors\\competitors.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { CompetitorsService } from './competitors.service';\r\nimport { Subject } from 'rxjs';\r\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\r\n\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-competitors',\r\n  templateUrl: './competitors.component.html',\r\n  styleUrl: './competitors.component.scss',\r\n})\r\nexport class CompetitorsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public breadcrumbitems: MenuItem[] | any;\r\n  public home: MenuItem | any;\r\n  public competitors: any[] = [];\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n  public searchInputChanged: Subject<string> = new Subject<string>();\r\n\r\n  constructor(private competitorsservice: CompetitorsService) { }\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'name', header: 'Name' },\r\n    { field: 'status_code', header: 'Status' }\r\n  ];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.competitors.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = (value1 < value2 ? -1 : (value1 > value2 ? 1 : 0));\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.searchInputChanged\r\n      .pipe(\r\n        debounceTime(500), // Adjust delay here (ms)\r\n        distinctUntilChanged()\r\n      )\r\n      .subscribe((term: string) => {\r\n        this.globalSearchTerm = term;\r\n        this.loadCompetitors({ first: 0, rows: 15 });\r\n      });\r\n    this.breadcrumbitems = [\r\n      { label: 'Competitors', routerLink: ['/store/competitors'] },\r\n    ];\r\n\r\n    this.home = { icon: 'pi pi-home', routerLink: ['/'] };\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  loadCompetitors(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n\r\n    this.competitorsservice\r\n      .getCompetitors(\r\n        page,\r\n        pageSize,\r\n        sortField,\r\n        sortOrder,\r\n        this.globalSearchTerm\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.competitors = response?.data ?? [];\r\n          this.totalRecords = response?.meta?.pagination.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching competitors', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onSearchInputChange(event: Event) {\r\n    const input = (event.target as HTMLInputElement).value;\r\n    this.searchInputChanged.next(input);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <!-- Search Box -->\r\n            <div class=\"h-search-box\">\r\n                <span class=\"p-input-icon-right\">\r\n                    <input type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\" (input)=\"onSearchInputChange($event)\"\r\n                        placeholder=\"Search Competitor\"\r\n                        class=\"p-inputtext p-component p-element w-24rem h-3rem px-3 border-round-3xl border-1 surface-border\" />\r\n                    <i class=\"pi pi-search\" style=\"right: 16px\"></i>\r\n                </span>\r\n            </div>\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n\r\n        <p-table #dt1 [value]=\"competitors\" dataKey=\"id\" [rows]=\"15\" [totalRecords]=\"totalRecords\" [paginator]=\"true\"\r\n            (onLazyLoad)=\"loadCompetitors($event)\" [lazy]=\"true\" responsiveLayout=\"scroll\" [scrollable]=\"true\"\r\n            class=\"scrollable-table\" [reorderableColumns]=\"true\" (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center table-checkbox\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pFrozenColumn (click)=\"customSort('competitor_id')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            ID\r\n                            <i *ngIf=\"sortField === 'competitor_id'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'competitor_id'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-competitor let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"competitor\" />\r\n                    </td>\r\n                    <td pFrozenColumn class=\"text-orange-600 cursor-pointer font-medium\">\r\n                        {{ competitor?.competitor_id || \"-\" }}\r\n                    </td>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n\r\n                                <ng-container *ngSwitchCase=\"'name'\">\r\n                                    {{ competitor?.name || \"-\" }}\r\n                                </ng-container>\r\n                                \r\n                                <ng-container *ngSwitchCase=\"'status_code'\">\r\n                                    {{ competitor?.status_code || \"-\" }}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"6\" class=\"border-round-left-lg pl-3\">\r\n                        No competitor found.\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"6\" class=\"border-round-left-lg pl-3\">\r\n                        Loading competitor data. Please wait.\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAGA,SAASA,OAAO,QAAQ,MAAM;AAC9B,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,gBAAgB;;;;;;;;;;;ICiCvCC,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAAqE;;;;;IAOjED,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;;IAP3ED,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,aAAqF;IAAhCN,EAAA,CAAAO,UAAA,mBAAAC,gFAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAC,+DAAA,gBACkF,IAAAC,+DAAA,gBAEvB;IAEnEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;IAG7BhB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;;;;;;IAnB7ChB,EADJ,CAAAM,cAAA,SAAI,aACsF;IAClFN,EAAA,CAAAC,SAAA,4BAAyB;IAC7BD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAM,cAAA,aAAwD;IAAtCN,EAAA,CAAAO,UAAA,mBAAAmB,iEAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,eAAe,CAAC;IAAA,EAAC;IACnDf,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,WACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAU,gDAAA,gBACkF,IAAAC,gDAAA,gBAEjB;IAEzE7B,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAY,2DAAA,2BAAkD;IAWtD9B,EAAA,CAAAqB,YAAA,EAAK;;;;IAjBWrB,EAAA,CAAAsB,SAAA,GAAmC;IAAnCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,qBAAmC;IAGnCzB,EAAA,CAAAsB,SAAA,EAAmC;IAAnCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,qBAAmC;IAGjBzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IA2BpC/B,EAAA,CAAAK,uBAAA,GAAqC;IACjCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,aAAA,kBAAAA,aAAA,CAAAC,IAAA,cACJ;;;;;IAEAjC,EAAA,CAAAK,uBAAA,GAA4C;IACxCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,aAAA,kBAAAA,aAAA,CAAAE,WAAA,cACJ;;;;;IAVZlC,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IAMjCL,EAJA,CAAAkB,UAAA,IAAAiB,0EAAA,2BAAqC,IAAAC,0EAAA,2BAIO;;IAKpDpC,EAAA,CAAAqB,YAAA,EAAK;;;;;IAXarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAAmC,MAAA,CAAArB,KAAA,CAAsB;IAEjBhB,EAAA,CAAAsB,SAAA,EAAoB;IAApBtB,EAAA,CAAAE,UAAA,wBAAoB;IAIpBF,EAAA,CAAAsB,SAAA,EAA2B;IAA3BtB,EAAA,CAAAE,UAAA,+BAA2B;;;;;IAftDF,EADJ,CAAAM,cAAA,aAA2B,aACgD;IACnEN,EAAA,CAAAC,SAAA,0BAAwC;IAC5CD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAM,cAAA,aAAqE;IACjEN,EAAA,CAAAiB,MAAA,GACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;IAELrB,EAAA,CAAAkB,UAAA,IAAAoB,2DAAA,2BAAkD;IAetDtC,EAAA,CAAAqB,YAAA,EAAK;;;;;IArBoBrB,EAAA,CAAAsB,SAAA,GAAoB;IAApBtB,EAAA,CAAAE,UAAA,UAAA8B,aAAA,CAAoB;IAGrChC,EAAA,CAAAsB,SAAA,GACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,aAAA,kBAAAA,aAAA,CAAAO,aAAA,cACJ;IAE8BvC,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IAoBhD/B,EADJ,CAAAM,cAAA,SAAI,aACkD;IAC9CN,EAAA,CAAAiB,MAAA,6BACJ;IACJjB,EADI,CAAAqB,YAAA,EAAK,EACJ;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aACkD;IAC9CN,EAAA,CAAAiB,MAAA,8CACJ;IACJjB,EADI,CAAAqB,YAAA,EAAK,EACJ;;;ADhFrB,OAAM,MAAOmB,oBAAoB;EAU/BC,YAAoBC,kBAAsC;IAAtC,KAAAA,kBAAkB,GAAlBA,kBAAkB;IAT9B,KAAAC,YAAY,GAAG,IAAI9C,OAAO,EAAQ;IAGnC,KAAA+C,WAAW,GAAU,EAAE;IACvB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,kBAAkB,GAAoB,IAAInD,OAAO,EAAU;IAI1D,KAAAoD,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAElC,KAAK,EAAE,MAAM;MAAEQ,MAAM,EAAE;IAAM,CAAE,EACjC;MAAER,KAAK,EAAE,aAAa;MAAEQ,MAAM,EAAE;IAAQ,CAAE,CAC3C;IAED,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAArB,SAAS,GAAW,CAAC;EAVyC;EAY9DW,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACS,SAAS,KAAKT,KAAK,EAAE;MAC5B,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACqB,SAAS,GAAGT,KAAK;MACtB,IAAI,CAACZ,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAACwC,WAAW,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC7B,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAEpC,KAAK,CAAC;MAC9C,MAAMwC,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAErC,KAAK,CAAC;MAE9C,IAAIyC,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAIH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAIF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAG;MAEhE,OAAO,IAAI,CAACpD,SAAS,GAAGqD,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAE3C,KAAa;IACvC,IAAI,CAAC2C,IAAI,IAAI,CAAC3C,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAAC4C,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAAC3C,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAAC6C,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACjB,kBAAkB,CACpBkB,IAAI,CACHpE,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBC,oBAAoB,EAAE,CACvB,CACAoE,SAAS,CAAEC,IAAY,IAAI;MAC1B,IAAI,CAACrB,gBAAgB,GAAGqB,IAAI;MAC5B,IAAI,CAACC,eAAe,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAE,CAAE,CAAC;IAC9C,CAAC,CAAC;IACJ,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE,aAAa;MAAEC,UAAU,EAAE,CAAC,oBAAoB;IAAC,CAAE,CAC7D;IAED,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAErD,IAAI,CAACzB,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAGA,IAAInB,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACkB,gBAAgB;EAC9B;EAEA,IAAIlB,eAAeA,CAAC8C,GAAU;IAC5B,IAAI,CAAC5B,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAAC4B,MAAM,CAACC,GAAG,IAAIF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACpE;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAAClC,gBAAgB,CAACiC,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAACnC,gBAAgB,CAACoC,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAACnC,gBAAgB,CAACoC,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;EAEAd,eAAeA,CAACa,KAAU;IACxB,IAAI,CAACpC,OAAO,GAAG,IAAI;IACnB,MAAMyC,IAAI,GAAGL,KAAK,CAACZ,KAAK,GAAGY,KAAK,CAACX,IAAI,GAAG,CAAC;IACzC,MAAMiB,QAAQ,GAAGN,KAAK,CAACX,IAAI;IAC3B,MAAM9C,SAAS,GAAGyD,KAAK,CAACzD,SAAS;IACjC,MAAMrB,SAAS,GAAG8E,KAAK,CAAC9E,SAAS;IAEjC,IAAI,CAACsC,kBAAkB,CACpB+C,cAAc,CACbF,IAAI,EACJC,QAAQ,EACR/D,SAAS,EACTrB,SAAS,EACT,IAAI,CAAC2C,gBAAgB,CACtB,CACAoB,SAAS,CAAC;MACTuB,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAC/C,WAAW,GAAG+C,QAAQ,EAAEhC,IAAI,IAAI,EAAE;QACvC,IAAI,CAACd,YAAY,GAAG8C,QAAQ,EAAEC,IAAI,EAAEC,UAAU,CAACC,KAAK;QACpD,IAAI,CAAChD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDiD,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAACjD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEAmD,mBAAmBA,CAACf,KAAY;IAC9B,MAAMgB,KAAK,GAAIhB,KAAK,CAACiB,MAA2B,CAACC,KAAK;IACtD,IAAI,CAACpD,kBAAkB,CAAC0C,IAAI,CAACQ,KAAK,CAAC;EACrC;EAEAG,WAAWA,CAAA;IACT,IAAI,CAAC1D,YAAY,CAAC+C,IAAI,EAAE;IACxB,IAAI,CAAC/C,YAAY,CAAC2D,QAAQ,EAAE;EAC9B;;;uBA/HW9D,oBAAoB,EAAAxC,EAAA,CAAAuG,iBAAA,CAAAC,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAApBjE,oBAAoB;MAAAkE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCdzBhH,EAFR,CAAAM,cAAA,aAA8D,aACmB,aAC7C;UACxBN,EAAA,CAAAC,SAAA,sBAA+F;UACnGD,EAAA,CAAAqB,YAAA,EAAM;UAKMrB,EAJZ,CAAAM,cAAA,aAA2C,aAEb,cACW,kBAGgF;UAFlFN,EAAA,CAAAkH,gBAAA,2BAAAC,6DAAAC,MAAA;YAAApH,EAAA,CAAAU,aAAA,CAAA2G,GAAA;YAAArH,EAAA,CAAAsH,kBAAA,CAAAL,GAAA,CAAAlE,gBAAA,EAAAqE,MAAA,MAAAH,GAAA,CAAAlE,gBAAA,GAAAqE,MAAA;YAAA,OAAApH,EAAA,CAAAc,WAAA,CAAAsG,MAAA;UAAA,EAA8B;UAACpH,EAAA,CAAAO,UAAA,mBAAAgH,qDAAAH,MAAA;YAAApH,EAAA,CAAAU,aAAA,CAAA2G,GAAA;YAAA,OAAArH,EAAA,CAAAc,WAAA,CAASmG,GAAA,CAAAhB,mBAAA,CAAAmB,MAAA,CAA2B;UAAA,EAAC;UAA/FpH,EAAA,CAAAqB,YAAA,EAE6G;UAC7GrB,EAAA,CAAAC,SAAA,YAAgD;UAExDD,EADI,CAAAqB,YAAA,EAAO,EACL;UAENrB,EAAA,CAAAM,cAAA,yBAE+I;UAF/GN,EAAA,CAAAkH,gBAAA,2BAAAM,sEAAAJ,MAAA;YAAApH,EAAA,CAAAU,aAAA,CAAA2G,GAAA;YAAArH,EAAA,CAAAsH,kBAAA,CAAAL,GAAA,CAAAlF,eAAA,EAAAqF,MAAA,MAAAH,GAAA,CAAAlF,eAAA,GAAAqF,MAAA;YAAA,OAAApH,EAAA,CAAAc,WAAA,CAAAsG,MAAA;UAAA,EAA6B;UAKrEpH,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAIFrB,EAFJ,CAAAM,cAAA,eAAuB,sBAI+E;UAAzCN,EADrD,CAAAO,UAAA,wBAAAkH,6DAAAL,MAAA;YAAApH,EAAA,CAAAU,aAAA,CAAA2G,GAAA;YAAA,OAAArH,EAAA,CAAAc,WAAA,CAAcmG,GAAA,CAAA5C,eAAA,CAAA+C,MAAA,CAAuB;UAAA,EAAC,0BAAAM,+DAAAN,MAAA;YAAApH,EAAA,CAAAU,aAAA,CAAA2G,GAAA;YAAA,OAAArH,EAAA,CAAAc,WAAA,CAC+BmG,GAAA,CAAAhC,eAAA,CAAAmC,MAAA,CAAuB;UAAA,EAAC;UAgE7FpH,EA9DA,CAAAkB,UAAA,KAAAyG,4CAAA,0BAAgC,KAAAC,4CAAA,0BA4BmC,KAAAC,4CAAA,0BA2B7B,KAAAC,4CAAA,0BAOD;UASjD9H,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;;;UAjGoBrB,EAAA,CAAAsB,SAAA,GAAyB;UAAetB,EAAxC,CAAAE,UAAA,UAAA+G,GAAA,CAAAzC,eAAA,CAAyB,SAAAyC,GAAA,CAAAtC,IAAA,CAAc,uCAAuC;UAMzD3E,EAAA,CAAAsB,SAAA,GAA8B;UAA9BtB,EAAA,CAAA+H,gBAAA,YAAAd,GAAA,CAAAlE,gBAAA,CAA8B;UAOlD/C,EAAA,CAAAsB,SAAA,GAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAA+G,GAAA,CAAA/D,IAAA,CAAgB;UAAClD,EAAA,CAAA+H,gBAAA,YAAAd,GAAA,CAAAlF,eAAA,CAA6B;UAEzD/B,EAAA,CAAAE,UAAA,2IAA0I;UAOpIF,EAAA,CAAAsB,SAAA,GAAqB;UAENtB,EAFf,CAAAE,UAAA,UAAA+G,GAAA,CAAArE,WAAA,CAAqB,YAAyB,iBAAAqE,GAAA,CAAApE,YAAA,CAA8B,mBAAmB,cACrD,oBAA8C,4BAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}