{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../contacts.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/breadcrumb\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/tabview\";\nimport * as i9 from \"primeng/toast\";\nimport * as i10 from \"primeng/confirmdialog\";\nimport * as i11 from \"../../../shared/initials.pipe\";\nfunction ContactsDetailsComponent_p_tabPanel_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction ContactsDetailsComponent_p_tabPanel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 31);\n    i0.ɵɵtemplate(1, ContactsDetailsComponent_p_tabPanel_9_ng_template_1_Template, 2, 2, \"ng-template\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class ContactsDetailsComponent {\n  constructor(router, route, contactsservice, messageservice, confirmationservice) {\n    this.router = router;\n    this.route = route;\n    this.contactsservice = contactsservice;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.unsubscribe$ = new Subject();\n    this.contactDetails = null;\n    this.sidebarDetails = null;\n    this.items = [];\n    this.id = '';\n    this.bp_status = '';\n    this.bp_doc_id = '';\n    this.breadcrumbitems = [];\n    this.activeItem = null;\n    this.isSidebarHidden = false;\n    this.Actions = [];\n    this.activeIndex = 0;\n  }\n  ngOnInit() {\n    this.id = this.route.snapshot.paramMap.get('id') || '';\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.makeMenuItems(this.id);\n    if (this.items.length > 0) {\n      this.activeItem = this.items[0];\n    }\n    this.setActiveTabFromURL();\n    this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n      const contactId = params.get('id');\n      if (contactId) {\n        this.loadContactData(contactId);\n      }\n    });\n    // Listen for route changes to keep active tab in sync\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.setActiveTabFromURL();\n    });\n  }\n  makeMenuItems(id) {\n    this.items = [{\n      label: 'Overview',\n      routerLink: `/store/contacts/${id}/overview`\n    }, {\n      label: 'Relationships',\n      routerLink: `/store/contacts/${id}/relationships`\n    }, {\n      label: 'Attachments',\n      routerLink: `/store/contacts/${id}/attachments`\n    }, {\n      label: 'Notes',\n      routerLink: `/store/contacts/${id}/notes`\n    }, {\n      label: 'Activities',\n      routerLink: `/store/contacts/${id}/activities`\n    }, {\n      label: 'Opportunities',\n      routerLink: `/store/contacts/${id}/opportunities`\n    }, {\n      label: 'Tickets',\n      routerLink: `/store/contacts/${id}/tickets`\n    }];\n  }\n  setActiveTabFromURL() {\n    const fullPath = this.router.url;\n    const currentTab = fullPath.split('/').pop() || 'overview';\n    if (this.items.length === 0) return;\n    const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n  }\n  updateBreadcrumb(activeTab) {\n    this.breadcrumbitems = [{\n      label: 'Contacts',\n      routerLink: ['/store/contacts']\n    }, {\n      label: activeTab,\n      routerLink: []\n    }];\n  }\n  onTabChange(event) {\n    if (this.items.length === 0) return;\n    this.activeIndex = event.index;\n    const selectedTab = this.items[this.activeIndex];\n    if (selectedTab?.routerLink) {\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n    }\n  }\n  loadContactData(contactId) {\n    this.contactsservice.getContactByID(contactId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        if (response) {\n          this.bp_doc_id = response?.data?.[0]?.business_partner_person?.documentId;\n          this.bp_status = response?.data?.[0]?.business_partner_person?.is_marked_for_archiving;\n          this.Actions = [{\n            name: this.bp_status ? 'Set As Active' : 'Set As Obsolete',\n            code: this.bp_status ? 'SAA' : 'SAO'\n          }];\n          this.contactDetails = response?.data[0] || null;\n          this.sidebarDetails = this.formatSidebarDetails(response?.data[0]?.business_partner_person?.addresses || []);\n        }\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n      }\n    });\n  }\n  formatSidebarDetails(addresses) {\n    return (addresses || []).map(address => {\n      const phoneNumbers = address?.phone_numbers || [];\n      return {\n        ...address,\n        address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n        email_address: address?.emails?.[0]?.email_address || '-',\n        phone_number: phoneNumbers.find(item => String(item?.phone_number_type) === '1')?.phone_number || '-',\n        website_url: address?.home_page_urls?.[0]?.website_url || '-'\n      };\n    });\n  }\n  onActionChange(event) {\n    const actionCode = event.value?.code;\n    const actionsMap = {\n      SAA: () => this.UpdateStatus(this.bp_doc_id, 'false'),\n      SAO: () => this.UpdateStatus(this.bp_doc_id, 'true')\n    };\n    const action = actionsMap[actionCode];\n    if (action) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to proceed with this action?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: action\n      });\n    }\n  }\n  UpdateStatus(docid, status) {\n    const data = {\n      is_marked_for_archiving: status\n    };\n    this.contactsservice.updateBpStatus(docid, data).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Action Updated Successfully!'\n        });\n        setTimeout(() => {\n          window.location.reload();\n        }, 1000);\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  goToBack() {\n    this.router.navigate(['/store/contacts']);\n  }\n  toggleSidebar() {\n    this.isSidebarHidden = !this.isSidebarHidden;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ContactsDetailsComponent_Factory(t) {\n      return new (t || ContactsDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ContactsService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContactsDetailsComponent,\n      selectors: [[\"app-contacts-details\"]],\n      decls: 62,\n      vars: 25,\n      consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"onChange\", \"options\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"activeIndexChange\", \"onChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\", \"font-semibold\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\", \"all-page-details\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function ContactsDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"p-breadcrumb\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p-dropdown\", 5);\n          i0.ɵɵlistener(\"onChange\", function ContactsDetailsComponent_Template_p_dropdown_onChange_5_listener($event) {\n            return ctx.onActionChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"p-tabView\", 8);\n          i0.ɵɵtwoWayListener(\"activeIndexChange\", function ContactsDetailsComponent_Template_p_tabView_activeIndexChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onChange\", function ContactsDetailsComponent_Template_p_tabView_onChange_8_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵtemplate(9, ContactsDetailsComponent_p_tabPanel_9_Template, 2, 1, \"p-tabPanel\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15)(16, \"div\", 16)(17, \"h5\", 17);\n          i0.ɵɵtext(18);\n          i0.ɵɵpipe(19, \"initials\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 18)(21, \"h5\", 19);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"ul\", 20)(24, \"li\", 21)(25, \"span\", 22);\n          i0.ɵɵtext(26, \"CRM ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(28, \"div\", 23)(29, \"ul\", 24)(30, \"li\", 25)(31, \"span\", 26)(32, \"i\", 27);\n          i0.ɵɵtext(33, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(34, \" Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"span\", 28);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"li\", 25)(38, \"span\", 26)(39, \"i\", 27);\n          i0.ɵɵtext(40, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(41, \" Phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"span\", 28);\n          i0.ɵɵtext(43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"li\", 25)(45, \"span\", 26)(46, \"i\", 27);\n          i0.ɵɵtext(47, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(48, \" Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"span\", 28);\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"li\", 25)(52, \"span\", 26)(53, \"i\", 27);\n          i0.ɵɵtext(54, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(55, \" Website\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"span\", 28);\n          i0.ɵɵtext(57);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(58, \"div\", 29)(59, \"p-button\", 30);\n          i0.ɵɵlistener(\"click\", function ContactsDetailsComponent_Template_p_button_click_59_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(60, \"router-outlet\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(61, \"p-confirmDialog\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.Actions)(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 23, ctx.contactDetails == null ? null : ctx.contactDetails.business_partner_person == null ? null : ctx.contactDetails.business_partner_person.bp_full_name));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.contactDetails == null ? null : ctx.contactDetails.business_partner_person == null ? null : ctx.contactDetails.business_partner_person.bp_full_name) || \"-\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" : \", (ctx.contactDetails == null ? null : ctx.contactDetails.bp_person_id) || \"-\", \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n          i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n        }\n      },\n      dependencies: [i4.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i5.Breadcrumb, i3.PrimeTemplate, i6.Dropdown, i7.Button, i8.TabView, i8.TabPanel, i9.Toast, i10.ConfirmDialog, i11.InitialsPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "tab_r1", "routerLink", "ɵɵadvance", "ɵɵtextInterpolate", "label", "ɵɵtemplate", "ContactsDetailsComponent_p_tabPanel_9_ng_template_1_Template", "ContactsDetailsComponent", "constructor", "router", "route", "contactsservice", "messageservice", "confirmationservice", "unsubscribe$", "contactDetails", "sidebarDetails", "items", "id", "bp_status", "bp_doc_id", "breadcrumbitems", "activeItem", "isSidebarHidden", "Actions", "activeIndex", "ngOnInit", "snapshot", "paramMap", "get", "home", "icon", "makeMenuItems", "length", "setActiveTabFromURL", "pipe", "subscribe", "params", "contactId", "loadContactData", "events", "fullPath", "url", "currentTab", "split", "pop", "foundIndex", "findIndex", "tab", "endsWith", "updateBreadcrumb", "activeTab", "onTabChange", "event", "index", "selectedTab", "navigateByUrl", "getContactByID", "next", "response", "data", "business_partner_person", "documentId", "is_marked_for_archiving", "name", "code", "formatSidebarDetails", "addresses", "error", "console", "map", "address", "phoneNumbers", "phone_numbers", "house_number", "street_name", "city_name", "region", "country", "postal_code", "filter", "Boolean", "join", "email_address", "emails", "phone_number", "find", "item", "String", "phone_number_type", "website_url", "home_page_urls", "onActionChange", "actionCode", "value", "actionsMap", "SAA", "UpdateStatus", "SAO", "action", "confirm", "message", "header", "accept", "docid", "status", "updateBpStatus", "add", "severity", "detail", "setTimeout", "window", "location", "reload", "goToBack", "navigate", "toggleSidebar", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "ContactsService", "i3", "MessageService", "ConfirmationService", "selectors", "decls", "vars", "consts", "template", "ContactsDetailsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "ContactsDetailsComponent_Template_p_dropdown_onChange_5_listener", "$event", "ɵɵtwoWayListener", "ContactsDetailsComponent_Template_p_tabView_activeIndexChange_8_listener", "ɵɵtwoWayBindingSet", "ContactsDetailsComponent_Template_p_tabView_onChange_8_listener", "ContactsDetailsComponent_p_tabPanel_9_Template", "ContactsDetailsComponent_Template_p_button_click_59_listener", "ɵɵtwoWayProperty", "ɵɵclassProp", "ɵɵpipeBind1", "bp_full_name", "ɵɵtextInterpolate1", "bp_person_id"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts-details\\contacts-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts-details\\contacts-details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { ContactsService } from '../contacts.service';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-contacts-details',\r\n  templateUrl: './contacts-details.component.html',\r\n  styleUrl: './contacts-details.component.scss',\r\n})\r\nexport class ContactsDetailsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public contactDetails: any = null;\r\n  public sidebarDetails: any = null;\r\n  public items: MenuItem[] = [];\r\n  public home: MenuItem | any;\r\n  public id: string = '';\r\n  public bp_status: string = '';\r\n  public bp_doc_id: string = '';\r\n  public breadcrumbitems: MenuItem[] = [];\r\n  public activeItem: MenuItem | null = null;\r\n  public isSidebarHidden = false;\r\n  public Actions: Actions[] = [];\r\n  public activeIndex: number = 0;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private contactsservice: ContactsService,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.id = this.route.snapshot.paramMap.get('id') || '';\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n    this.makeMenuItems(this.id);\r\n    if (this.items.length > 0) {\r\n      this.activeItem = this.items[0];\r\n    }\r\n\r\n    this.setActiveTabFromURL();\r\n\r\n    this.route.paramMap\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((params) => {\r\n        const contactId = params.get('id');\r\n        if (contactId) {\r\n          this.loadContactData(contactId);\r\n        }\r\n      });\r\n    // Listen for route changes to keep active tab in sync\r\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\r\n      this.setActiveTabFromURL();\r\n    });\r\n  }\r\n\r\n  makeMenuItems(id: string) {\r\n    this.items = [\r\n      {\r\n        label: 'Overview',\r\n        routerLink: `/store/contacts/${id}/overview`,\r\n      },\r\n      {\r\n        label: 'Relationships',\r\n        routerLink: `/store/contacts/${id}/relationships`,\r\n      },\r\n      {\r\n        label: 'Attachments',\r\n        routerLink: `/store/contacts/${id}/attachments`,\r\n      },\r\n      {\r\n        label: 'Notes',\r\n        routerLink: `/store/contacts/${id}/notes`,\r\n      },\r\n      {\r\n        label: 'Activities',\r\n        routerLink: `/store/contacts/${id}/activities`,\r\n      },\r\n      {\r\n        label: 'Opportunities',\r\n        routerLink: `/store/contacts/${id}/opportunities`,\r\n      },\r\n      {\r\n        label: 'Tickets',\r\n        routerLink: `/store/contacts/${id}/tickets`,\r\n      },\r\n    ];\r\n  }\r\n\r\n  setActiveTabFromURL() {\r\n    const fullPath = this.router.url;\r\n    const currentTab = fullPath.split('/').pop() || 'overview';\r\n\r\n    if (this.items.length === 0) return;\r\n\r\n    const foundIndex = this.items.findIndex((tab: any) =>\r\n      tab.routerLink.endsWith(currentTab)\r\n    );\r\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\r\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\r\n\r\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\r\n  }\r\n\r\n  updateBreadcrumb(activeTab: string) {\r\n    this.breadcrumbitems = [\r\n      { label: 'Contacts', routerLink: ['/store/contacts'] },\r\n      { label: activeTab, routerLink: [] },\r\n    ];\r\n  }\r\n\r\n  onTabChange(event: { index: number }) {\r\n    if (this.items.length === 0) return;\r\n\r\n    this.activeIndex = event.index;\r\n    const selectedTab = this.items[this.activeIndex];\r\n\r\n    if (selectedTab?.routerLink) {\r\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\r\n    }\r\n  }\r\n\r\n  private loadContactData(contactId: string): void {\r\n    this.contactsservice\r\n      .getContactByID(contactId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (response) {\r\n            this.bp_doc_id =\r\n              response?.data?.[0]?.business_partner_person?.documentId;\r\n            this.bp_status =\r\n              response?.data?.[0]?.business_partner_person?.is_marked_for_archiving;\r\n            this.Actions = [\r\n              {\r\n                name: this.bp_status ? 'Set As Active' : 'Set As Obsolete',\r\n                code: this.bp_status ? 'SAA' : 'SAO',\r\n              },\r\n            ];\r\n\r\n            this.contactDetails = response?.data[0] || null;\r\n            this.sidebarDetails = this.formatSidebarDetails(\r\n              response?.data[0]?.business_partner_person\r\n                ?.addresses || []\r\n            );\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  private formatSidebarDetails(addresses: any[]): any[] {\r\n    return (addresses || []).map((address: any) => {\r\n      const phoneNumbers = address?.phone_numbers || [];\r\n\r\n      return {\r\n        ...address,\r\n        address: [\r\n          address?.house_number,\r\n          address?.street_name,\r\n          address?.city_name,\r\n          address?.region,\r\n          address?.country,\r\n          address?.postal_code,\r\n        ]\r\n          .filter(Boolean)\r\n          .join(', '),\r\n        email_address: address?.emails?.[0]?.email_address || '-',\r\n        phone_number:\r\n          phoneNumbers.find(\r\n            (item: any) => String(item?.phone_number_type) === '1'\r\n          )?.phone_number || '-',\r\n        website_url: address?.home_page_urls?.[0]?.website_url || '-',\r\n      };\r\n    });\r\n  }\r\n\r\n  onActionChange(event: any) {\r\n    const actionCode = event.value?.code;\r\n\r\n    const actionsMap: { [key: string]: () => void } = {\r\n      SAA: () => this.UpdateStatus(this.bp_doc_id, 'false'),\r\n      SAO: () => this.UpdateStatus(this.bp_doc_id, 'true'),\r\n    };\r\n\r\n    const action = actionsMap[actionCode];\r\n\r\n    if (action) {\r\n      this.confirmationservice.confirm({\r\n        message: 'Are you sure you want to proceed with this action?',\r\n        header: 'Confirm',\r\n        icon: 'pi pi-exclamation-triangle',\r\n        accept: action,\r\n      });\r\n    }\r\n  }\r\n\r\n  UpdateStatus(docid: any, status: any) {\r\n    const data = {\r\n      is_marked_for_archiving: status,\r\n    };\r\n    this.contactsservice\r\n      .updateBpStatus(docid, data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Action Updated Successfully!',\r\n          });\r\n\r\n          setTimeout(() => {\r\n            window.location.reload();\r\n          }, 1000);\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  goToBack() {\r\n    this.router.navigate(['/store/contacts']);\r\n  }\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarHidden = !this.isSidebarHidden;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-center\" [life]=\"3000\"></p-toast>\r\n<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\">\r\n        <div class=\"breadcrumb-sec flex align-items-center gap-3\">\r\n            <!-- <p-button icon=\"pi pi-arrow-left\" class=\"p-button-primary p-back-button\" label=\"Back\"\r\n                (onClick)=\"goToBack()\"></p-button> -->\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <p-dropdown [options]=\"Actions\" (onChange)=\"onActionChange($event)\" optionLabel=\"name\" placeholder=\"Action\"\r\n            [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" />\r\n    </div>\r\n\r\n    <div class=\"details-tabs-sec\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\" [(activeIndex)]=\"activeIndex\" (onChange)=\"onTabChange($event)\">\r\n                <p-tabPanel *ngFor=\"let tab of items; let i = index\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.routerLink\" routerLinkActive=\"active-tab\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\">{{\r\n                            tab.label }}</a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n        </div>\r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <div class=\"grid mt-0 relative\">\r\n                <div class=\"col-12 lg:w-28rem md:w-28rem sm:w-full\" [class.sidebar-hide]=\"isSidebarHidden\">\r\n                    <div class=\"w-full bg-white border-round shadow-1 overflow-hidden\">\r\n                        <div class=\"left-side-bar-top p-4 bg-primary overflow-hidden\">\r\n                            <div class=\"flex align-items-start gap-4\">\r\n                                <div\r\n                                    class=\"flex align-items-center justify-content-center w-3rem h-3rem surface-0 border-circle\">\r\n                                    <h5 class=\"m-0 p-0 text-primary font-bold\">{{ contactDetails?.business_partner_person?.bp_full_name | initials }}</h5>\r\n                                </div>\r\n                                <div class=\"flex flex-column gap-4 flex-1\">\r\n                                    <h5 class=\"mt-3 mb-1 font-semibold\">\r\n                                        {{contactDetails?.business_partner_person?.bp_full_name || \"-\"}}\r\n                                    </h5>\r\n                                    <ul class=\"flex flex-column gap-3 p-0 m-0 list-none\">\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem font-semibold\">CRM ID</span> :\r\n                                            {{contactDetails?.bp_person_id || \"-\"}}\r\n                                        </li>\r\n                                        <!-- <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">S4/HANA ID</span> : 152ASD5585\r\n                                        </li> -->\r\n                                        <!-- <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">Account Owner </span> :\r\n                                            {{contactDetails?.account_owner || \"-\"}}\r\n                                        </li>\r\n                                        <li class=\"flex align-items-center gap-2\">\r\n                                            <span class=\"flex w-9rem\">Main Contact</span> : {{\r\n                                            (contactDetails?.contact_companies?.[0]?.business_partner_person?.first_name\r\n                                            || \"-\") + \" \" +\r\n                                            (contactDetails?.contact_companies?.[0]?.business_partner_person?.last_name\r\n                                            || \"-\") }}\r\n                                        </li> -->\r\n                                    </ul>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"left-side-bar-bottom px-3 py-4\">\r\n                            <ul class=\"flex flex-column gap-5 p-0 m-0 list-none\">\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">location_on</i>\r\n                                        Address</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i>\r\n                                        Phone</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.phone_number || \"-\"}}</span>\r\n                                </li>\r\n                                <!-- <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i> Main\r\n                                        Contact</span>\r\n                                    <span\r\n                                        class=\"flex-1\">{{contactDetails?.contact_companies?.[0]?.business_partner_person?.addresses?.[0]?.phone_numbers?.[0]?.phone_number\r\n                                        || \"-\"}}</span>\r\n                                </li> -->\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">mail</i> Email</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.email_address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">language</i>\r\n                                        Website</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.website_url || \"-\"}}</span>\r\n                                </li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:flex-1 md:flex-1 relative all-page-details\">\r\n                    <p-button icon=\"pi pi-angle-left\" [rounded]=\"true\" [outlined]=\"true\"\r\n                        [styleClass]=\"'p-0 w-2rem h-2rem border-2 surface-0'\"\r\n                        class=\"arrow-btn inline-flex absolute transition-all transition-duration-300 transition-ease-in-out\"\r\n                        (click)=\"toggleSidebar()\" [class.arrow-round]=\"isSidebarHidden\" />\r\n                    <router-outlet></router-outlet>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n<p-confirmDialog></p-confirmDialog>"], "mappings": "AAGA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;ICcjBC,EAAA,CAAAC,cAAA,YACuF;IAAAD,EAAA,CAAAE,MAAA,GACvE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAFjBH,EAAA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAA6B;IACuDN,EAAA,CAAAO,SAAA,EACvE;IADuEP,EAAA,CAAAQ,iBAAA,CAAAH,MAAA,CAAAI,KAAA,CACvE;;;;;IAJxBT,EAAA,CAAAC,cAAA,qBAAoF;IAChFD,EAAA,CAAAU,UAAA,IAAAC,4DAAA,0BAAgC;IAKpCX,EAAA,CAAAG,YAAA,EAAa;;;IANwCH,EAAA,CAAAI,UAAA,+BAA8B;;;ADEnG,OAAM,MAAOQ,wBAAwB;EAenCC,YACUC,MAAc,EACdC,KAAqB,EACrBC,eAAgC,EAChCC,cAA8B,EAC9BC,mBAAwC;IAJxC,KAAAJ,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAnBrB,KAAAC,YAAY,GAAG,IAAIrB,OAAO,EAAQ;IACnC,KAAAsB,cAAc,GAAQ,IAAI;IAC1B,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,KAAK,GAAe,EAAE;IAEtB,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,eAAe,GAAe,EAAE;IAChC,KAAAC,UAAU,GAAoB,IAAI;IAClC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,OAAO,GAAc,EAAE;IACvB,KAAAC,WAAW,GAAW,CAAC;EAQ3B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACR,EAAE,GAAG,IAAI,CAACR,KAAK,CAACiB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACtD,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAE9B,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAC7D,IAAI,CAAC+B,aAAa,CAAC,IAAI,CAACd,EAAE,CAAC;IAC3B,IAAI,IAAI,CAACD,KAAK,CAACgB,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAACX,UAAU,GAAG,IAAI,CAACL,KAAK,CAAC,CAAC,CAAC;IACjC;IAEA,IAAI,CAACiB,mBAAmB,EAAE;IAE1B,IAAI,CAACxB,KAAK,CAACkB,QAAQ,CAChBO,IAAI,CAACzC,SAAS,CAAC,IAAI,CAACoB,YAAY,CAAC,CAAC,CAClCsB,SAAS,CAAEC,MAAM,IAAI;MACpB,MAAMC,SAAS,GAAGD,MAAM,CAACR,GAAG,CAAC,IAAI,CAAC;MAClC,IAAIS,SAAS,EAAE;QACb,IAAI,CAACC,eAAe,CAACD,SAAS,CAAC;MACjC;IACF,CAAC,CAAC;IACJ;IACA,IAAI,CAAC7B,MAAM,CAAC+B,MAAM,CAACL,IAAI,CAACzC,SAAS,CAAC,IAAI,CAACoB,YAAY,CAAC,CAAC,CAACsB,SAAS,CAAC,MAAK;MACnE,IAAI,CAACF,mBAAmB,EAAE;IAC5B,CAAC,CAAC;EACJ;EAEAF,aAAaA,CAACd,EAAU;IACtB,IAAI,CAACD,KAAK,GAAG,CACX;MACEb,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE,mBAAmBiB,EAAE;KAClC,EACD;MACEd,KAAK,EAAE,eAAe;MACtBH,UAAU,EAAE,mBAAmBiB,EAAE;KAClC,EACD;MACEd,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE,mBAAmBiB,EAAE;KAClC,EACD;MACEd,KAAK,EAAE,OAAO;MACdH,UAAU,EAAE,mBAAmBiB,EAAE;KAClC,EACD;MACEd,KAAK,EAAE,YAAY;MACnBH,UAAU,EAAE,mBAAmBiB,EAAE;KAClC,EACD;MACEd,KAAK,EAAE,eAAe;MACtBH,UAAU,EAAE,mBAAmBiB,EAAE;KAClC,EACD;MACEd,KAAK,EAAE,SAAS;MAChBH,UAAU,EAAE,mBAAmBiB,EAAE;KAClC,CACF;EACH;EAEAgB,mBAAmBA,CAAA;IACjB,MAAMO,QAAQ,GAAG,IAAI,CAAChC,MAAM,CAACiC,GAAG;IAChC,MAAMC,UAAU,GAAGF,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAI,UAAU;IAE1D,IAAI,IAAI,CAAC5B,KAAK,CAACgB,MAAM,KAAK,CAAC,EAAE;IAE7B,MAAMa,UAAU,GAAG,IAAI,CAAC7B,KAAK,CAAC8B,SAAS,CAAEC,GAAQ,IAC/CA,GAAG,CAAC/C,UAAU,CAACgD,QAAQ,CAACN,UAAU,CAAC,CACpC;IACD,IAAI,CAAClB,WAAW,GAAGqB,UAAU,KAAK,CAAC,CAAC,GAAGA,UAAU,GAAG,CAAC;IACrD,IAAI,CAACxB,UAAU,GAAG,IAAI,CAACL,KAAK,CAAC,IAAI,CAACQ,WAAW,CAAC,IAAI,IAAI,CAACR,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IAEvE,IAAI,CAACiC,gBAAgB,CAAC,IAAI,CAAC5B,UAAU,EAAElB,KAAK,IAAI,UAAU,CAAC;EAC7D;EAEA8C,gBAAgBA,CAACC,SAAiB;IAChC,IAAI,CAAC9B,eAAe,GAAG,CACrB;MAAEjB,KAAK,EAAE,UAAU;MAAEH,UAAU,EAAE,CAAC,iBAAiB;IAAC,CAAE,EACtD;MAAEG,KAAK,EAAE+C,SAAS;MAAElD,UAAU,EAAE;IAAE,CAAE,CACrC;EACH;EAEAmD,WAAWA,CAACC,KAAwB;IAClC,IAAI,IAAI,CAACpC,KAAK,CAACgB,MAAM,KAAK,CAAC,EAAE;IAE7B,IAAI,CAACR,WAAW,GAAG4B,KAAK,CAACC,KAAK;IAC9B,MAAMC,WAAW,GAAG,IAAI,CAACtC,KAAK,CAAC,IAAI,CAACQ,WAAW,CAAC;IAEhD,IAAI8B,WAAW,EAAEtD,UAAU,EAAE;MAC3B,IAAI,CAACQ,MAAM,CAAC+C,aAAa,CAACD,WAAW,CAACtD,UAAU,CAAC,CAAC,CAAC;IACrD;EACF;EAEQsC,eAAeA,CAACD,SAAiB;IACvC,IAAI,CAAC3B,eAAe,CACjB8C,cAAc,CAACnB,SAAS,CAAC,CACzBH,IAAI,CAACzC,SAAS,CAAC,IAAI,CAACoB,YAAY,CAAC,CAAC,CAClCsB,SAAS,CAAC;MACTsB,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAACvC,SAAS,GACZuC,QAAQ,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAEC,uBAAuB,EAAEC,UAAU;UAC1D,IAAI,CAAC3C,SAAS,GACZwC,QAAQ,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAEC,uBAAuB,EAAEE,uBAAuB;UACvE,IAAI,CAACvC,OAAO,GAAG,CACb;YACEwC,IAAI,EAAE,IAAI,CAAC7C,SAAS,GAAG,eAAe,GAAG,iBAAiB;YAC1D8C,IAAI,EAAE,IAAI,CAAC9C,SAAS,GAAG,KAAK,GAAG;WAChC,CACF;UAED,IAAI,CAACJ,cAAc,GAAG4C,QAAQ,EAAEC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;UAC/C,IAAI,CAAC5C,cAAc,GAAG,IAAI,CAACkD,oBAAoB,CAC7CP,QAAQ,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAEC,uBAAuB,EACtCM,SAAS,IAAI,EAAE,CACpB;QACH;MACF,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEQF,oBAAoBA,CAACC,SAAgB;IAC3C,OAAO,CAACA,SAAS,IAAI,EAAE,EAAEG,GAAG,CAAEC,OAAY,IAAI;MAC5C,MAAMC,YAAY,GAAGD,OAAO,EAAEE,aAAa,IAAI,EAAE;MAEjD,OAAO;QACL,GAAGF,OAAO;QACVA,OAAO,EAAE,CACPA,OAAO,EAAEG,YAAY,EACrBH,OAAO,EAAEI,WAAW,EACpBJ,OAAO,EAAEK,SAAS,EAClBL,OAAO,EAAEM,MAAM,EACfN,OAAO,EAAEO,OAAO,EAChBP,OAAO,EAAEQ,WAAW,CACrB,CACEC,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI,CAAC;QACbC,aAAa,EAAEZ,OAAO,EAAEa,MAAM,GAAG,CAAC,CAAC,EAAED,aAAa,IAAI,GAAG;QACzDE,YAAY,EACVb,YAAY,CAACc,IAAI,CACdC,IAAS,IAAKC,MAAM,CAACD,IAAI,EAAEE,iBAAiB,CAAC,KAAK,GAAG,CACvD,EAAEJ,YAAY,IAAI,GAAG;QACxBK,WAAW,EAAEnB,OAAO,EAAEoB,cAAc,GAAG,CAAC,CAAC,EAAED,WAAW,IAAI;OAC3D;IACH,CAAC,CAAC;EACJ;EAEAE,cAAcA,CAACvC,KAAU;IACvB,MAAMwC,UAAU,GAAGxC,KAAK,CAACyC,KAAK,EAAE7B,IAAI;IAEpC,MAAM8B,UAAU,GAAkC;MAChDC,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC7E,SAAS,EAAE,OAAO,CAAC;MACrD8E,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACD,YAAY,CAAC,IAAI,CAAC7E,SAAS,EAAE,MAAM;KACpD;IAED,MAAM+E,MAAM,GAAGJ,UAAU,CAACF,UAAU,CAAC;IAErC,IAAIM,MAAM,EAAE;MACV,IAAI,CAACtF,mBAAmB,CAACuF,OAAO,CAAC;QAC/BC,OAAO,EAAE,oDAAoD;QAC7DC,MAAM,EAAE,SAAS;QACjBvE,IAAI,EAAE,4BAA4B;QAClCwE,MAAM,EAAEJ;OACT,CAAC;IACJ;EACF;EAEAF,YAAYA,CAACO,KAAU,EAAEC,MAAW;IAClC,MAAM7C,IAAI,GAAG;MACXG,uBAAuB,EAAE0C;KAC1B;IACD,IAAI,CAAC9F,eAAe,CACjB+F,cAAc,CAACF,KAAK,EAAE5C,IAAI,CAAC,CAC3BzB,IAAI,CAACzC,SAAS,CAAC,IAAI,CAACoB,YAAY,CAAC,CAAC,CAClCsB,SAAS,CAAC;MACTsB,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC9C,cAAc,CAAC+F,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QAEFC,UAAU,CAAC,MAAK;UACdC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACD7C,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACxD,cAAc,CAAC+F,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAK,QAAQA,CAAA;IACN,IAAI,CAACzG,MAAM,CAAC0G,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEAC,aAAaA,CAAA;IACX,IAAI,CAAC7F,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA8F,WAAWA,CAAA;IACT,IAAI,CAACvG,YAAY,CAAC4C,IAAI,EAAE;IACxB,IAAI,CAAC5C,YAAY,CAACwG,QAAQ,EAAE;EAC9B;;;uBApOW/G,wBAAwB,EAAAZ,EAAA,CAAA4H,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA9H,EAAA,CAAA4H,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAA/H,EAAA,CAAA4H,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAAjI,EAAA,CAAA4H,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAAnI,EAAA,CAAA4H,iBAAA,CAAAM,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAAxBxH,wBAAwB;MAAAyH,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBrC3I,EAAA,CAAA6I,SAAA,iBAAuD;UAG/C7I,EAFR,CAAAC,cAAA,aAA8D,aACgC,aAC5B;UAGtDD,EAAA,CAAA6I,SAAA,sBAA+F;UACnG7I,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,oBACyG;UADzED,EAAA,CAAA8I,UAAA,sBAAAC,iEAAAC,MAAA;YAAA,OAAYJ,GAAA,CAAA3C,cAAA,CAAA+C,MAAA,CAAsB;UAAA,EAAC;UAEvEhJ,EAFI,CAAAG,YAAA,EACyG,EACvG;UAIEH,EAFR,CAAAC,cAAA,aAA8B,aACK,mBACmE;UAA/DD,EAAA,CAAAiJ,gBAAA,+BAAAC,yEAAAF,MAAA;YAAAhJ,EAAA,CAAAmJ,kBAAA,CAAAP,GAAA,CAAA9G,WAAA,EAAAkH,MAAA,MAAAJ,GAAA,CAAA9G,WAAA,GAAAkH,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAAChJ,EAAA,CAAA8I,UAAA,sBAAAM,gEAAAJ,MAAA;YAAA,OAAYJ,GAAA,CAAAnF,WAAA,CAAAuF,MAAA,CAAmB;UAAA,EAAC;UACzFhJ,EAAA,CAAAU,UAAA,IAAA2I,8CAAA,wBAAoF;UAQ5FrJ,EADI,CAAAG,YAAA,EAAY,EACV;UASsBH,EAR5B,CAAAC,cAAA,eAAqD,eACjB,eAC+D,eACpB,eACD,eAChB,eAE2D,cAClD;UAAAD,EAAA,CAAAE,MAAA,IAAsE;;UACrHF,EADqH,CAAAG,YAAA,EAAK,EACpH;UAEFH,EADJ,CAAAC,cAAA,eAA2C,cACH;UAChCD,EAAA,CAAAE,MAAA,IACJ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGGH,EAFR,CAAAC,cAAA,cAAqD,cACP,gBACE;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAE1D;UAkBhBF,EAlBgB,CAAAG,YAAA,EAAK,EAeJ,EACH,EACJ,EACJ;UAIiFH,EAHvF,CAAAC,cAAA,eAA4C,cACa,cACyB,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpDH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAAuC;UAChEF,EADgE,CAAAG,YAAA,EAAO,EAClE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtDH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA4C;UACrEF,EADqE,CAAAG,YAAA,EAAO,EACvE;UAU0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA6C;UACtEF,EADsE,CAAAG,YAAA,EAAO,EACxE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjDH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA2C;UAKpFF,EALoF,CAAAG,YAAA,EAAO,EACtE,EACJ,EACH,EACJ,EACJ;UAEFH,EADJ,CAAAC,cAAA,eAAkE,oBAIQ;UAAlED,EAAA,CAAA8I,UAAA,mBAAAQ,6DAAA;YAAA,OAASV,GAAA,CAAAnB,aAAA,EAAe;UAAA,EAAC;UAH7BzH,EAAA,CAAAG,YAAA,EAGsE;UACtEH,EAAA,CAAA6I,SAAA,qBAA+B;UAKnD7I,EAJgB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ;UACNH,EAAA,CAAA6I,SAAA,uBAAmC;;;UA7GJ7I,EAAA,CAAAI,UAAA,cAAa;UAMlBJ,EAAA,CAAAO,SAAA,GAAyB;UAAeP,EAAxC,CAAAI,UAAA,UAAAwI,GAAA,CAAAlH,eAAA,CAAyB,SAAAkH,GAAA,CAAAzG,IAAA,CAAc,uCAAuC;UAEpFnC,EAAA,CAAAO,SAAA,EAAmB;UAC3BP,EADQ,CAAAI,UAAA,YAAAwI,GAAA,CAAA/G,OAAA,CAAmB,mGACuE;UAKvF7B,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UAACJ,EAAA,CAAAuJ,gBAAA,gBAAAX,GAAA,CAAA9G,WAAA,CAA6B;UAC5B9B,EAAA,CAAAO,SAAA,EAAU;UAAVP,EAAA,CAAAI,UAAA,YAAAwI,GAAA,CAAAtH,KAAA,CAAU;UAWctB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAwJ,WAAA,iBAAAZ,GAAA,CAAAhH,eAAA,CAAsC;UAM3B5B,EAAA,CAAAO,SAAA,GAAsE;UAAtEP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAyJ,WAAA,SAAAb,GAAA,CAAAxH,cAAA,kBAAAwH,GAAA,CAAAxH,cAAA,CAAA8C,uBAAA,kBAAA0E,GAAA,CAAAxH,cAAA,CAAA8C,uBAAA,CAAAwF,YAAA,EAAsE;UAI7G1J,EAAA,CAAAO,SAAA,GACJ;UADIP,EAAA,CAAA2J,kBAAA,OAAAf,GAAA,CAAAxH,cAAA,kBAAAwH,GAAA,CAAAxH,cAAA,CAAA8C,uBAAA,kBAAA0E,GAAA,CAAAxH,cAAA,CAAA8C,uBAAA,CAAAwF,YAAA,cACJ;UAG8D1J,EAAA,CAAAO,SAAA,GAE1D;UAF0DP,EAAA,CAAA2J,kBAAA,SAAAf,GAAA,CAAAxH,cAAA,kBAAAwH,GAAA,CAAAxH,cAAA,CAAAwI,YAAA,cAE1D;UAyBiB5J,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAQ,iBAAA,EAAAoI,GAAA,CAAAvH,cAAA,kBAAAuH,GAAA,CAAAvH,cAAA,qBAAAuH,GAAA,CAAAvH,cAAA,IAAAuD,OAAA,SAAuC;UAMvC5E,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAQ,iBAAA,EAAAoI,GAAA,CAAAvH,cAAA,kBAAAuH,GAAA,CAAAvH,cAAA,qBAAAuH,GAAA,CAAAvH,cAAA,IAAAqE,YAAA,SAA4C;UAa5C1F,EAAA,CAAAO,SAAA,GAA6C;UAA7CP,EAAA,CAAAQ,iBAAA,EAAAoI,GAAA,CAAAvH,cAAA,kBAAAuH,GAAA,CAAAvH,cAAA,qBAAAuH,GAAA,CAAAvH,cAAA,IAAAmE,aAAA,SAA6C;UAM7CxF,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAQ,iBAAA,EAAAoI,GAAA,CAAAvH,cAAA,kBAAAuH,GAAA,CAAAvH,cAAA,qBAAAuH,GAAA,CAAAvH,cAAA,IAAA0E,WAAA,SAA2C;UAUlD/F,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAwJ,WAAA,gBAAAZ,GAAA,CAAAhH,eAAA,CAAqC;UAF/D5B,EAD8B,CAAAI,UAAA,iBAAgB,kBAAkB,sDACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}