{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/tabview\";\nimport * as i5 from \"primeng/breadcrumb\";\nfunction SalesQuotesDetailsComponent_p_tabPanel_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.RouterLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tab_r1.label);\n  }\n}\nfunction SalesQuotesDetailsComponent_p_tabPanel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 10);\n    i0.ɵɵtemplate(1, SalesQuotesDetailsComponent_p_tabPanel_9_ng_template_1_Template, 2, 2, \"ng-template\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class SalesQuotesDetailsComponent {\n  setActiveMenu(label) {\n    this.activeMenu = label;\n  }\n  constructor(router) {\n    this.router = router;\n    this.activeMenu = '';\n    this.activeIndex = 0;\n    this.scrollableTabs = [{\n      label: 'Overview',\n      RouterLink: '/store/sales-quotes/overview'\n    },\n    // {\n    //   label: 'Contacts',\n    //   RouterLink: '/store/sales-quotes/contacts'\n    // },\n    // {\n    //   label: 'Sales Team',\n    //   RouterLink: '/store/sales-quotes/sales-team'\n    // },\n    // {\n    //   label: 'AI Insights',\n    //   RouterLink: '/store/sales-quotes/ai-insights'\n    // },\n    // {\n    //   label: 'Organization Data',\n    //   RouterLink: '/store/sales-quotes/organization-data'\n    // },\n    {\n      label: 'Attachments',\n      RouterLink: '/store/sales-quotes/attachments'\n    }, {\n      label: 'Notes',\n      RouterLink: '/store/sales-quotes/notes'\n    }];\n  }\n  editS4Quote() {\n    console.log(\"Edit S4 Quote clicked\");\n    // Navigate to the edit page (uncomment if using Angular Router)\n    // this.router.navigate(['/edit-quote']);\n    // Add logic for editing the quote here\n  }\n  ngOnInit() {\n    this.items = [{\n      label: 'Sales Quotes',\n      routerLink: ['/store/sales-quotes']\n    }, {\n      label: `${this.activeMenu}`\n    }];\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.Actions = [{\n      name: 'Change Quote',\n      code: 'CI'\n    }];\n    this.selectedActions = {\n      name: 'Edit S4 Quote',\n      code: 'edit_s4'\n    };\n  }\n  goToBack() {\n    this.router.navigate(['/store/sales-quotes']);\n  }\n  static {\n    this.ɵfac = function SalesQuotesDetailsComponent_Factory(t) {\n      return new (t || SalesQuotesDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SalesQuotesDetailsComponent,\n      selectors: [[\"app-sales-quotes-details\"]],\n      decls: 12,\n      vars: 6,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"bg-primary\", \"text-white\", \"border-primary\", \"hover:bg-white\", \"hover:text-primary\", \"transition-all\", 3, \"click\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"scrollable\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function SalesQuotesDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function SalesQuotesDetailsComponent_Template_button_click_4_listener() {\n            return ctx.editS4Quote();\n          });\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"p-tabView\", 7);\n          i0.ɵɵtemplate(9, SalesQuotesDetailsComponent_p_tabPanel_9_Template, 2, 1, \"p-tabPanel\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 9);\n          i0.ɵɵelement(11, \"router-outlet\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.selectedActions == null ? null : ctx.selectedActions.name) || \"Edit S4 Quote\", \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.scrollableTabs);\n        }\n      },\n      dependencies: [i2.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i3.PrimeTemplate, i4.TabView, i4.TabPanel, i5.Breadcrumb],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "tab_r1", "RouterLink", "ɵɵadvance", "ɵɵtextInterpolate", "label", "ɵɵtemplate", "SalesQuotesDetailsComponent_p_tabPanel_9_ng_template_1_Template", "SalesQuotesDetailsComponent", "setActiveMenu", "activeMenu", "constructor", "router", "activeIndex", "scrollableTabs", "editS4Quote", "console", "log", "ngOnInit", "items", "routerLink", "home", "icon", "Actions", "name", "code", "selectedActions", "goToBack", "navigate", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "SalesQuotesDetailsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "SalesQuotesDetailsComponent_Template_button_click_4_listener", "SalesQuotesDetailsComponent_p_tabPanel_9_Template", "ɵɵtextInterpolate1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-quotes\\sales-quotes-details\\sales-quotes-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-quotes\\sales-quotes-details\\sales-quotes-details.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router, RouterLink } from '@angular/router';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-sales-quotes-details',\r\n  templateUrl: './sales-quotes-details.component.html',\r\n  styleUrl: './sales-quotes-details.component.scss'\r\n})\r\nexport class SalesQuotesDetailsComponent {\r\n\r\n  items: MenuItem[] | any;\r\n  home: MenuItem | any;\r\n\r\n  activeMenu: string = '';\r\n\r\n  setActiveMenu(label: string): void {\r\n    this.activeMenu = label;\r\n  }\r\n\r\n  Actions: Actions[] | undefined;\r\n  selectedActions: Actions | undefined;\r\n\r\n  constructor(\r\n    private router: Router,\r\n  ) { }\r\n\r\n  editS4Quote() {\r\n    console.log(\"Edit S4 Quote clicked\");\r\n\r\n    // Navigate to the edit page (uncomment if using Angular Router)\r\n    // this.router.navigate(['/edit-quote']);\r\n\r\n    // Add logic for editing the quote here\r\n  }\r\n  ngOnInit() {\r\n    this.items = [\r\n      { label: 'Sales Quotes', routerLink: ['/store/sales-quotes'] },\r\n      { label: `${this.activeMenu}` },\r\n    ];\r\n\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n\r\n    this.Actions = [\r\n      { name: 'Change Quote', code: 'CI' },\r\n    ];\r\n    this.selectedActions = { name: 'Edit S4 Quote', code: 'edit_s4' };\r\n  }\r\n\r\n  activeIndex: number = 0;\r\n  scrollableTabs: any[] = [\r\n    {\r\n      label: 'Overview',\r\n      RouterLink: '/store/sales-quotes/overview',\r\n    },\r\n    // {\r\n    //   label: 'Contacts',\r\n    //   RouterLink: '/store/sales-quotes/contacts'\r\n    // },\r\n    // {\r\n    //   label: 'Sales Team',\r\n    //   RouterLink: '/store/sales-quotes/sales-team'\r\n    // },\r\n    // {\r\n    //   label: 'AI Insights',\r\n    //   RouterLink: '/store/sales-quotes/ai-insights'\r\n    // },\r\n    // {\r\n    //   label: 'Organization Data',\r\n    //   RouterLink: '/store/sales-quotes/organization-data'\r\n    // },\r\n    {\r\n      label: 'Attachments',\r\n      RouterLink: '/store/sales-quotes/attachments'\r\n    },\r\n    {\r\n      label: 'Notes',\r\n      RouterLink: '/store/sales-quotes/notes'\r\n    },\r\n  ];\r\n\r\n\r\n\r\n  goToBack() {\r\n    this.router.navigate(['/store/sales-quotes']);\r\n  }\r\n}", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\">\r\n        <div class=\"breadcrumb-sec flex align-items-center gap-3\">\r\n            <!-- <p-button icon=\"pi pi-arrow-left\" class=\"p-button-primary p-back-button\" label=\"Back\"\r\n                (onClick)=\"goToBack()\"></p-button> -->\r\n            <p-breadcrumb [model]=\"items\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <!-- <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" optionLabel=\"name\" placeholder=\"Action\"\r\n            [styleClass]=\"'w-13rem h-3rem px-2 py-1 surface-0 border-1 border-primary font-semibold'\" /> -->\r\n            <button type=\"button\" (click)=\"editS4Quote()\"\r\n            class=\"h-3rem p-element p-ripple p-button p-component w-8rem justify-content-center gap-2 font-semibold bg-primary text-white border-primary hover:bg-white hover:text-primary transition-all\">\r\n            {{ selectedActions?.name || 'Edit S4 Quote' }}\r\n        </button>\r\n    </div>\r\n\r\n    <div class=\"details-tabs-sec\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\">\r\n                <p-tabPanel *ngFor=\"let tab of scrollableTabs\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.RouterLink\" routerLinkActive=\"active-tab\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\">{{\r\n                            tab.label }}</a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n        </div>\r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <router-outlet></router-outlet>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;;ICoBwBA,EAAA,CAAAC,cAAA,YACuF;IAAAD,EAAA,CAAAE,MAAA,GACvE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAFjBH,EAAA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAA6B;IACuDN,EAAA,CAAAO,SAAA,EACvE;IADuEP,EAAA,CAAAQ,iBAAA,CAAAH,MAAA,CAAAI,KAAA,CACvE;;;;;IAJxBT,EAAA,CAAAC,cAAA,qBAA8E;IAC1ED,EAAA,CAAAU,UAAA,IAAAC,+DAAA,0BAAgC;IAKpCX,EAAA,CAAAG,YAAA,EAAa;;;IANkCH,EAAA,CAAAI,UAAA,+BAA8B;;;ADJ7F,OAAM,MAAOQ,2BAA2B;EAOtCC,aAAaA,CAACJ,KAAa;IACzB,IAAI,CAACK,UAAU,GAAGL,KAAK;EACzB;EAKAM,YACUC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAVhB,KAAAF,UAAU,GAAW,EAAE;IAmCvB,KAAAG,WAAW,GAAW,CAAC;IACvB,KAAAC,cAAc,GAAU,CACtB;MACET,KAAK,EAAE,UAAU;MACjBH,UAAU,EAAE;KACb;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACEG,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE;KACb,EACD;MACEG,KAAK,EAAE,OAAO;MACdH,UAAU,EAAE;KACb,CACF;EAtDG;EAEJa,WAAWA,CAAA;IACTC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IAEpC;IACA;IAEA;EACF;EACAC,QAAQA,CAAA;IACN,IAAI,CAACC,KAAK,GAAG,CACX;MAAEd,KAAK,EAAE,cAAc;MAAEe,UAAU,EAAE,CAAC,qBAAqB;IAAC,CAAE,EAC9D;MAAEf,KAAK,EAAE,GAAG,IAAI,CAACK,UAAU;IAAE,CAAE,CAChC;IAED,IAAI,CAACW,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAE7D,IAAI,CAACG,OAAO,GAAG,CACb;MAAEC,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE;IAAI,CAAE,CACrC;IACD,IAAI,CAACC,eAAe,GAAG;MAAEF,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE;IAAS,CAAE;EACnE;EAoCAE,QAAQA,CAAA;IACN,IAAI,CAACf,MAAM,CAACgB,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC;EAC/C;;;uBA5EWpB,2BAA2B,EAAAZ,EAAA,CAAAiC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA3BvB,2BAA2B;MAAAwB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZhC1C,EAFR,CAAAC,cAAA,aAA8D,aACgC,aAC5B;UAGtDD,EAAA,CAAA4C,SAAA,sBAAqF;UACzF5C,EAAA,CAAAG,YAAA,EAAM;UAGFH,EAAA,CAAAC,cAAA,gBAC+L;UADzKD,EAAA,CAAA6C,UAAA,mBAAAC,6DAAA;YAAA,OAASH,GAAA,CAAAxB,WAAA,EAAa;UAAA,EAAC;UAE7CnB,EAAA,CAAAE,MAAA,GACJ;UACJF,EADI,CAAAG,YAAA,EAAS,EACP;UAIEH,EAFR,CAAAC,cAAA,aAA8B,aACK,mBACI;UAC3BD,EAAA,CAAAU,UAAA,IAAAqC,iDAAA,wBAA8E;UAQtF/C,EADI,CAAAG,YAAA,EAAY,EACV;UACNH,EAAA,CAAAC,cAAA,cAAqD;UACjDD,EAAA,CAAA4C,SAAA,qBAA+B;UAG3C5C,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;UA1BoBH,EAAA,CAAAO,SAAA,GAAe;UAAeP,EAA9B,CAAAI,UAAA,UAAAuC,GAAA,CAAApB,KAAA,CAAe,SAAAoB,GAAA,CAAAlB,IAAA,CAAc,uCAAuC;UAMlFzB,EAAA,CAAAO,SAAA,GACJ;UADIP,EAAA,CAAAgD,kBAAA,OAAAL,GAAA,CAAAb,eAAA,kBAAAa,GAAA,CAAAb,eAAA,CAAAF,IAAA,0BACJ;UAKe5B,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UACEJ,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAAuC,GAAA,CAAAzB,cAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}