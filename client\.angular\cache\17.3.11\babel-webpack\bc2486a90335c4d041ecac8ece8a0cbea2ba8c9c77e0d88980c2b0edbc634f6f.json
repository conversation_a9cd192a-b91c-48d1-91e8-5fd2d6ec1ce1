{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Estonian [et]\n//! author : <PERSON> : https://github.com/madhenry\n//! improvements : Il<PERSON><PERSON> : https://github.com/ragulka\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function processRelativeTime(number, withoutSuffix, key, isFuture) {\n    var format = {\n      s: ['mõne sekundi', 'mõni sekund', 'paar sekundit'],\n      ss: [number + 'sekundi', number + 'sekundit'],\n      m: ['ühe minuti', 'üks minut'],\n      mm: [number + ' minuti', number + ' minutit'],\n      h: ['ühe tunni', 'tund aega', 'üks tund'],\n      hh: [number + ' tunni', number + ' tundi'],\n      d: ['ühe päeva', 'üks päev'],\n      M: ['kuu aja', 'kuu aega', 'üks kuu'],\n      MM: [number + ' kuu', number + ' kuud'],\n      y: ['ühe aasta', 'aasta', 'üks aasta'],\n      yy: [number + ' aasta', number + ' aastat']\n    };\n    if (withoutSuffix) {\n      return format[key][2] ? format[key][2] : format[key][1];\n    }\n    return isFuture ? format[key][0] : format[key][1];\n  }\n  var et = moment.defineLocale('et', {\n    months: 'jaanuar_veebruar_märts_aprill_mai_juuni_juuli_august_september_oktoober_november_detsember'.split('_'),\n    monthsShort: 'jaan_veebr_märts_apr_mai_juuni_juuli_aug_sept_okt_nov_dets'.split('_'),\n    weekdays: 'pühapäev_esmaspäev_teisipäev_kolmapäev_neljapäev_reede_laupäev'.split('_'),\n    weekdaysShort: 'P_E_T_K_N_R_L'.split('_'),\n    weekdaysMin: 'P_E_T_K_N_R_L'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY H:mm',\n      LLLL: 'dddd, D. MMMM YYYY H:mm'\n    },\n    calendar: {\n      sameDay: '[Täna,] LT',\n      nextDay: '[Homme,] LT',\n      nextWeek: '[Järgmine] dddd LT',\n      lastDay: '[Eile,] LT',\n      lastWeek: '[Eelmine] dddd LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s pärast',\n      past: '%s tagasi',\n      s: processRelativeTime,\n      ss: processRelativeTime,\n      m: processRelativeTime,\n      mm: processRelativeTime,\n      h: processRelativeTime,\n      hh: processRelativeTime,\n      d: processRelativeTime,\n      dd: '%d päeva',\n      M: processRelativeTime,\n      MM: processRelativeTime,\n      y: processRelativeTime,\n      yy: processRelativeTime\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return et;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "processRelativeTime", "number", "withoutSuffix", "key", "isFuture", "format", "s", "ss", "m", "mm", "h", "hh", "d", "M", "MM", "y", "yy", "et", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "dd", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/moment/locale/et.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Estonian [et]\n//! author : <PERSON> : https://github.com/madhenry\n//! improvements : Il<PERSON><PERSON> : https://github.com/ragulka\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            s: ['mõne sekundi', 'mõni sekund', 'paar sekundit'],\n            ss: [number + 'sekundi', number + 'sekundit'],\n            m: ['ühe minuti', 'üks minut'],\n            mm: [number + ' minuti', number + ' minutit'],\n            h: ['ühe tunni', 'tund aega', 'üks tund'],\n            hh: [number + ' tunni', number + ' tundi'],\n            d: ['ühe päeva', 'üks päev'],\n            M: ['kuu aja', 'kuu aega', 'üks kuu'],\n            MM: [number + ' kuu', number + ' kuud'],\n            y: ['ühe aasta', 'aasta', 'üks aasta'],\n            yy: [number + ' aasta', number + ' aastat'],\n        };\n        if (withoutSuffix) {\n            return format[key][2] ? format[key][2] : format[key][1];\n        }\n        return isFuture ? format[key][0] : format[key][1];\n    }\n\n    var et = moment.defineLocale('et', {\n        months: 'jaanuar_veebruar_märts_aprill_mai_juuni_juuli_august_september_oktoober_november_detsember'.split(\n            '_'\n        ),\n        monthsShort:\n            'jaan_veebr_märts_apr_mai_juuni_juuli_aug_sept_okt_nov_dets'.split('_'),\n        weekdays:\n            'pühapäev_esmaspäev_teisipäev_kolmapäev_neljapäev_reede_laupäev'.split(\n                '_'\n            ),\n        weekdaysShort: 'P_E_T_K_N_R_L'.split('_'),\n        weekdaysMin: 'P_E_T_K_N_R_L'.split('_'),\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY H:mm',\n            LLLL: 'dddd, D. MMMM YYYY H:mm',\n        },\n        calendar: {\n            sameDay: '[Täna,] LT',\n            nextDay: '[Homme,] LT',\n            nextWeek: '[Järgmine] dddd LT',\n            lastDay: '[Eile,] LT',\n            lastWeek: '[Eelmine] dddd LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s pärast',\n            past: '%s tagasi',\n            s: processRelativeTime,\n            ss: processRelativeTime,\n            m: processRelativeTime,\n            mm: processRelativeTime,\n            h: processRelativeTime,\n            hh: processRelativeTime,\n            d: processRelativeTime,\n            dd: '%d päeva',\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return et;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,SAASC,mBAAmBA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC/D,IAAIC,MAAM,GAAG;MACTC,CAAC,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,eAAe,CAAC;MACnDC,EAAE,EAAE,CAACN,MAAM,GAAG,SAAS,EAAEA,MAAM,GAAG,UAAU,CAAC;MAC7CO,CAAC,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC;MAC9BC,EAAE,EAAE,CAACR,MAAM,GAAG,SAAS,EAAEA,MAAM,GAAG,UAAU,CAAC;MAC7CS,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC;MACzCC,EAAE,EAAE,CAACV,MAAM,GAAG,QAAQ,EAAEA,MAAM,GAAG,QAAQ,CAAC;MAC1CW,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,CAAC;MAC5BC,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC;MACrCC,EAAE,EAAE,CAACb,MAAM,GAAG,MAAM,EAAEA,MAAM,GAAG,OAAO,CAAC;MACvCc,CAAC,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,WAAW,CAAC;MACtCC,EAAE,EAAE,CAACf,MAAM,GAAG,QAAQ,EAAEA,MAAM,GAAG,SAAS;IAC9C,CAAC;IACD,IAAIC,aAAa,EAAE;MACf,OAAOG,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGE,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGE,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3D;IACA,OAAOC,QAAQ,GAAGC,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGE,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;EACrD;EAEA,IAAIc,EAAE,GAAGlB,MAAM,CAACmB,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,4FAA4F,CAACC,KAAK,CACtG,GACJ,CAAC;IACDC,WAAW,EACP,4DAA4D,CAACD,KAAK,CAAC,GAAG,CAAC;IAC3EE,QAAQ,EACJ,gEAAgE,CAACF,KAAK,CAClE,GACJ,CAAC;IACLG,aAAa,EAAE,eAAe,CAACH,KAAK,CAAC,GAAG,CAAC;IACzCI,WAAW,EAAE,eAAe,CAACJ,KAAK,CAAC,GAAG,CAAC;IACvCK,cAAc,EAAE;MACZC,EAAE,EAAE,MAAM;MACVC,GAAG,EAAE,SAAS;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,cAAc;MAClBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,YAAY;MACrBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,oBAAoB;MAC9BC,OAAO,EAAE,YAAY;MACrBC,QAAQ,EAAE,mBAAmB;MAC7BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,WAAW;MACjBnC,CAAC,EAAEN,mBAAmB;MACtBO,EAAE,EAAEP,mBAAmB;MACvBQ,CAAC,EAAER,mBAAmB;MACtBS,EAAE,EAAET,mBAAmB;MACvBU,CAAC,EAAEV,mBAAmB;MACtBW,EAAE,EAAEX,mBAAmB;MACvBY,CAAC,EAAEZ,mBAAmB;MACtB0C,EAAE,EAAE,UAAU;MACd7B,CAAC,EAAEb,mBAAmB;MACtBc,EAAE,EAAEd,mBAAmB;MACvBe,CAAC,EAAEf,mBAAmB;MACtBgB,EAAE,EAAEhB;IACR,CAAC;IACD2C,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO9B,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}