{"ast": null, "code": "'use strict';\n\n/* eslint no-invalid-this: 1 */\nvar ERROR_MESSAGE = 'Function.prototype.bind called on incompatible ';\nvar toStr = Object.prototype.toString;\nvar max = Math.max;\nvar funcType = '[object Function]';\nvar concatty = function concatty(a, b) {\n  var arr = [];\n  for (var i = 0; i < a.length; i += 1) {\n    arr[i] = a[i];\n  }\n  for (var j = 0; j < b.length; j += 1) {\n    arr[j + a.length] = b[j];\n  }\n  return arr;\n};\nvar slicy = function slicy(arrLike, offset) {\n  var arr = [];\n  for (var i = offset || 0, j = 0; i < arrLike.length; i += 1, j += 1) {\n    arr[j] = arrLike[i];\n  }\n  return arr;\n};\nvar joiny = function (arr, joiner) {\n  var str = '';\n  for (var i = 0; i < arr.length; i += 1) {\n    str += arr[i];\n    if (i + 1 < arr.length) {\n      str += joiner;\n    }\n  }\n  return str;\n};\nmodule.exports = function bind(that) {\n  var target = this;\n  if (typeof target !== 'function' || toStr.apply(target) !== funcType) {\n    throw new TypeError(ERROR_MESSAGE + target);\n  }\n  var args = slicy(arguments, 1);\n  var bound;\n  var binder = function () {\n    if (this instanceof bound) {\n      var result = target.apply(this, concatty(args, arguments));\n      if (Object(result) === result) {\n        return result;\n      }\n      return this;\n    }\n    return target.apply(that, concatty(args, arguments));\n  };\n  var boundLength = max(0, target.length - args.length);\n  var boundArgs = [];\n  for (var i = 0; i < boundLength; i++) {\n    boundArgs[i] = '$' + i;\n  }\n  bound = Function('binder', 'return function (' + joiny(boundArgs, ',') + '){ return binder.apply(this,arguments); }')(binder);\n  if (target.prototype) {\n    var Empty = function Empty() {};\n    Empty.prototype = target.prototype;\n    bound.prototype = new Empty();\n    Empty.prototype = null;\n  }\n  return bound;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}