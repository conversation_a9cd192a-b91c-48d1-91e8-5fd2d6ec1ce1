{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./prospects.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/breadcrumb\";\nimport * as i9 from \"primeng/toast\";\nimport * as i10 from \"primeng/confirmdialog\";\nimport * as i11 from \"primeng/multiselect\";\nconst _c0 = [\"dt1\"];\nfunction ProspectsComponent_ng_template_20_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ProspectsComponent_ng_template_20_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 29);\n  }\n}\nfunction ProspectsComponent_ng_template_20_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ProspectsComponent_ng_template_20_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 29);\n  }\n}\nfunction ProspectsComponent_ng_template_20_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 30);\n    i0.ɵɵlistener(\"click\", function ProspectsComponent_ng_template_20_ng_container_8_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 24);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, ProspectsComponent_ng_template_20_ng_container_8_i_4_Template, 1, 1, \"i\", 25)(5, ProspectsComponent_ng_template_20_ng_container_8_i_5_Template, 1, 0, \"i\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== col_r5.field);\n  }\n}\nfunction ProspectsComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 22);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 23);\n    i0.ɵɵlistener(\"click\", function ProspectsComponent_ng_template_20_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.customSort(\"bp_id\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 24);\n    i0.ɵɵtext(5, \" Prospect ID \");\n    i0.ɵɵtemplate(6, ProspectsComponent_ng_template_20_i_6_Template, 1, 1, \"i\", 25)(7, ProspectsComponent_ng_template_20_i_7_Template, 1, 0, \"i\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, ProspectsComponent_ng_template_20_ng_container_8_Template, 6, 4, \"ng-container\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === \"bp_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== \"bp_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/prospects/\" + prospect_r6.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.email_address) || \"-\", \" \");\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.city_name) || \"-\", \" \");\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.country) || \"-\", \" \");\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.contact_name) || \"-\", \" \");\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.phone_number) || \"-\", \" \");\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.partner_role) || \"-\", \" \");\n  }\n}\nfunction ProspectsComponent_ng_template_21_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 35);\n    i0.ɵɵtemplate(3, ProspectsComponent_ng_template_21_ng_container_5_ng_container_3_Template, 3, 2, \"ng-container\", 36)(4, ProspectsComponent_ng_template_21_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 36)(5, ProspectsComponent_ng_template_21_ng_container_5_ng_container_5_Template, 2, 1, \"ng-container\", 36)(6, ProspectsComponent_ng_template_21_ng_container_5_ng_container_6_Template, 2, 1, \"ng-container\", 36)(7, ProspectsComponent_ng_template_21_ng_container_5_ng_container_7_Template, 2, 1, \"ng-container\", 36)(8, ProspectsComponent_ng_template_21_ng_container_5_ng_container_8_Template, 2, 1, \"ng-container\", 36)(9, ProspectsComponent_ng_template_21_ng_container_5_ng_container_9_Template, 2, 1, \"ng-container\", 36);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"addresses.emails.email_address\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"addresses.city_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"addresses.country\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"contact_companies.business_partner_person.first_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"addresses.phone_numbers.phone_number\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"customer.partner_functions.business_partner.bp_full_name\");\n  }\n}\nfunction ProspectsComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 31)(1, \"td\", 32);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ProspectsComponent_ng_template_21_ng_container_5_Template, 10, 8, \"ng-container\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const prospect_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", prospect_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/prospects/\" + prospect_r6.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (prospect_r6 == null ? null : prospect_r6.bp_id) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction ProspectsComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 38);\n    i0.ɵɵtext(2, \"No prospects found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProspectsComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 38);\n    i0.ɵɵtext(2, \"Loading prospects data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ProspectsComponent {\n  constructor(prospectsservice, router, messageservice, confirmationservice) {\n    this.prospectsservice = prospectsservice;\n    this.router = router;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.unsubscribe$ = new Subject();\n    this.prospects = [];\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n    this.partner_role = '';\n    this.searchInputChanged = new Subject();\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'bp_full_name',\n      header: 'Name'\n    }, {\n      field: 'addresses.emails.email_address',\n      header: 'Email'\n    }, {\n      field: 'addresses.city_name',\n      header: 'City'\n    }, {\n      field: 'addresses.country',\n      header: 'Country'\n    }, {\n      field: 'contact_companies.business_partner_person.first_name',\n      header: 'Contact'\n    }, {\n      field: 'addresses.phone_numbers.phone_number',\n      header: 'Phone'\n    }, {\n      field: 'customer.partner_functions.business_partner.bp_full_name',\n      header: 'Owner'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.prospects.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.searchInputChanged.pipe(debounceTime(500),\n    // Adjust delay here (ms)\n    distinctUntilChanged()).subscribe(term => {\n      this.globalSearchTerm = term;\n      this.loadProspects({\n        first: 0,\n        rows: 15\n      });\n    });\n    this.breadcrumbitems = [{\n      label: 'Prospects',\n      routerLink: ['/store/prospects']\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/']\n    };\n    this.Actions = [{\n      name: 'All',\n      code: 'ALL'\n    }, {\n      name: 'My Prospects',\n      code: 'MP'\n    }, {\n      name: 'Obsolete Prospects',\n      code: 'OP'\n    }];\n    this.selectedActions = {\n      name: 'All',\n      code: 'ALL'\n    };\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  loadProspects(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    const sortField = event.sortField;\n    const sortOrder = event.sortOrder;\n    const obsolete = this.selectedActions?.code === 'OP';\n    const myprospect = this.selectedActions?.code === 'MP';\n    this.prospectsservice.getProspects(page, pageSize, sortField, sortOrder, this.globalSearchTerm, obsolete, myprospect).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        let prospects = response?.data.map(prospect => {\n          const defaultAddress = prospect.addresses?.find(address => {\n            return address.address_usages.find(usage => usage.address_usage === 'XXDEFAULT');\n          });\n          const partner_role = prospect?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n          return {\n            ...prospect,\n            contact_name: (prospect?.contact_companies?.[0]?.business_partner_person?.first_name || '') + ' ' + (prospect?.contact_companies?.[0]?.business_partner_person?.last_name || '-'),\n            city_name: defaultAddress?.city_name || '-',\n            country: defaultAddress?.country || '-',\n            email_address: defaultAddress?.emails?.[0]?.email_address || '-',\n            phone_number: (() => {\n              const phoneList = defaultAddress?.phone_numbers ?? [];\n              const mobilePhone = phoneList.find(item => item.phone_number_type === '1');\n              const countryCode = mobilePhone?.destination_location_country;\n              const phoneNumber = mobilePhone?.phone_number;\n              if (!phoneNumber) {\n                return '-';\n              }\n              return this.prospectsservice.getDialCode(countryCode, phoneNumber);\n            })(),\n            partner_role: partner_role?.business_partner?.bp_full_name || null\n          };\n        }) || [];\n        this.prospects = prospects;\n        this.totalRecords = response?.meta?.pagination.total;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching prospects', error);\n        this.loading = false;\n      }\n    });\n  }\n  onActionChange() {\n    // Re-trigger the lazy load with current dt1 state\n    const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\n      first: 0,\n      rows: 15\n    };\n    this.loadProspects(dt1State);\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.prospectsservice.delete(item.documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.refresh();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  refresh() {\n    this.loadProspects({\n      first: 0,\n      rows: 15\n    });\n  }\n  signup() {\n    this.router.navigate(['/store/prospects/create']);\n  }\n  onSearchInputChange(event) {\n    const input = event.target.value;\n    this.searchInputChanged.next(input);\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ProspectsComponent_Factory(t) {\n      return new (t || ProspectsComponent)(i0.ɵɵdirectiveInject(i1.ProspectsService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProspectsComponent,\n      selectors: [[\"app-prospects\"]],\n      viewQuery: function ProspectsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dt1 = _t.first);\n        }\n      },\n      decls: 25,\n      vars: 19,\n      consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [\"position\", \"top-right\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-start\", \"justify-content-between\", \"flex-column\", \"gap-2\", \"md:flex-row\", \"md:align-items-center\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-start\", \"gap-3\", \"md:flex-row\", \"md:align-items-center\", \"flex-wrap\", \"w-full\", \"md:w-auto\"], [1, \"h-search-box\", \"w-full\", \"sm:w-24rem\"], [1, \"p-input-icon-right\", \"w-full\", \"md:w-24rem\"], [\"type\", \"text\", \"placeholder\", \"Search Prospect\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"sm:w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"optionLabel\", \"name\", \"placeholder\", \"Filter\", 1, \"w-full\", \"sm:w-13rem\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", \"ml-auto\", \"sm:ml-0\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onLazyLoad\", \"onColReorder\", \"value\", \"rows\", \"loading\", \"paginator\", \"totalRecords\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", 3, \"routerLink\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [1, \"text-blue-600\", \"font-medium\", \"underline\", \"cursor-pointer\", 3, \"routerLink\"], [\"colspan\", \"9\", 1, \"border-round-left-lg\", \"pl-3\"]],\n      template: function ProspectsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelement(0, \"p-toast\", 2);\n          i0.ɵɵelementStart(1, \"div\", 3)(2, \"div\", 4)(3, \"div\", 5);\n          i0.ɵɵelement(4, \"p-breadcrumb\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 7)(6, \"div\", 8)(7, \"span\", 9)(8, \"input\", 10, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ProspectsComponent_Template_input_ngModelChange_8_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function ProspectsComponent_Template_input_input_8_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearchInputChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"i\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"p-dropdown\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ProspectsComponent_Template_p_dropdown_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onChange\", function ProspectsComponent_Template_p_dropdown_onChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onActionChange());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function ProspectsComponent_Template_button_click_12_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.signup());\n          });\n          i0.ɵɵelementStart(13, \"span\", 14);\n          i0.ɵɵtext(14, \"box_edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15, \" Create \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p-multiSelect\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ProspectsComponent_Template_p_multiSelect_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 16)(18, \"p-table\", 17, 1);\n          i0.ɵɵlistener(\"onLazyLoad\", function ProspectsComponent_Template_p_table_onLazyLoad_18_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadProspects($event));\n          })(\"onColReorder\", function ProspectsComponent_Template_p_table_onColReorder_18_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onColumnReorder($event));\n          });\n          i0.ɵɵtemplate(20, ProspectsComponent_ng_template_20_Template, 9, 3, \"ng-template\", 18)(21, ProspectsComponent_ng_template_21_Template, 6, 4, \"ng-template\", 19)(22, ProspectsComponent_ng_template_22_Template, 3, 0, \"ng-template\", 20)(23, ProspectsComponent_ng_template_23_Template, 3, 0, \"ng-template\", 21);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(24, \"p-confirmDialog\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-full sm:w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.prospects)(\"rows\", 14)(\"loading\", ctx.loading)(\"paginator\", true)(\"totalRecords\", ctx.totalRecords)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i2.RouterLink, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.Table, i3.PrimeTemplate, i6.SortableColumn, i6.FrozenColumn, i6.ReorderableColumn, i6.TableCheckbox, i6.TableHeaderCheckbox, i7.Dropdown, i8.Breadcrumb, i9.Toast, i10.ConfirmDialog, i11.MultiSelect],\n      styles: [\".surface-card[_ngcontent-%COMP%]   .overview-banner[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  object-fit: cover;\\n}\\n.surface-card[_ngcontent-%COMP%]   .overview-banner[_ngcontent-%COMP%]:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4392156863), transparent);\\n  mix-blend-mode: multiply;\\n}\\n\\n.home-box-list[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n}\\n.home-box-list[_ngcontent-%COMP%]   .home-box[_ngcontent-%COMP%] {\\n  background: var(--surface-b);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvcHJvc3BlY3RzL3Byb3NwZWN0cy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFFUTtFQUNJLGlCQUFBO0FBRFo7QUFJUTtFQUNJLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLE9BQUE7RUFDQSxNQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSwyRUFBQTtFQUNBLHdCQUFBO0FBRlo7O0FBT0E7RUFDSSxpQkFBQTtBQUpKO0FBTUk7RUFDSSw0QkFBQTtBQUpSIiwic291cmNlc0NvbnRlbnQiOlsiLnN1cmZhY2UtY2FyZCB7XHJcbiAgICAub3ZlcnZpZXctYmFubmVyIHtcclxuICAgICAgICBpbWcge1xyXG4gICAgICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgICY6YmVmb3JlIHtcclxuICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgICBjb250ZW50OiBcIlwiO1xyXG4gICAgICAgICAgICBsZWZ0OiAwO1xyXG4gICAgICAgICAgICB0b3A6IDA7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgwZGVnLCAjMDAwMDAwNzAsIHRyYW5zcGFyZW50KTtcclxuICAgICAgICAgICAgbWl4LWJsZW5kLW1vZGU6IG11bHRpcGx5O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxyXG5cclxuLmhvbWUtYm94LWxpc3Qge1xyXG4gICAgbWF4LXdpZHRoOiAxMjAwcHg7XHJcblxyXG4gICAgLmhvbWUtYm94IHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLWIpO1xyXG4gICAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r2", "sortOrder", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "ProspectsComponent_ng_template_20_ng_container_8_Template_th_click_1_listener", "col_r5", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "ProspectsComponent_ng_template_20_ng_container_8_i_4_Template", "ProspectsComponent_ng_template_20_ng_container_8_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "ProspectsComponent_ng_template_20_Template_th_click_3_listener", "_r2", "ProspectsComponent_ng_template_20_i_6_Template", "ProspectsComponent_ng_template_20_i_7_Template", "ProspectsComponent_ng_template_20_ng_container_8_Template", "selectedColumns", "prospect_r6", "bp_id", "bp_full_name", "email_address", "city_name", "country", "contact_name", "phone_number", "partner_role", "ProspectsComponent_ng_template_21_ng_container_5_ng_container_3_Template", "ProspectsComponent_ng_template_21_ng_container_5_ng_container_4_Template", "ProspectsComponent_ng_template_21_ng_container_5_ng_container_5_Template", "ProspectsComponent_ng_template_21_ng_container_5_ng_container_6_Template", "ProspectsComponent_ng_template_21_ng_container_5_ng_container_7_Template", "ProspectsComponent_ng_template_21_ng_container_5_ng_container_8_Template", "ProspectsComponent_ng_template_21_ng_container_5_ng_container_9_Template", "col_r7", "ProspectsComponent_ng_template_21_ng_container_5_Template", "ProspectsComponent", "constructor", "prospectsservice", "router", "messageservice", "confirmationservice", "unsubscribe$", "prospects", "totalRecords", "loading", "globalSearchTerm", "searchInputChanged", "_selectedColumns", "cols", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "pipe", "subscribe", "term", "loadProspects", "first", "rows", "breadcrumbitems", "label", "routerLink", "home", "icon", "Actions", "name", "code", "selectedActions", "val", "filter", "col", "includes", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "page", "pageSize", "obsolete", "myprospect", "getProspects", "next", "response", "map", "prospect", "defaultAddress", "addresses", "find", "address", "address_usages", "usage", "address_usage", "customer", "partner_functions", "p", "partner_function", "contact_companies", "business_partner_person", "first_name", "last_name", "emails", "phoneList", "phone_numbers", "mobilePhone", "item", "phone_number_type", "countryCode", "destination_location_country", "phoneNumber", "getDialCode", "business_partner", "meta", "pagination", "total", "error", "console", "onActionChange", "dt1State", "dt1", "createLazyLoadMetadata", "confirmRemove", "confirm", "message", "accept", "remove", "delete", "documentId", "add", "severity", "detail", "refresh", "signup", "navigate", "onSearchInputChange", "input", "target", "value", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ProspectsService", "i2", "Router", "i3", "MessageService", "ConfirmationService", "selectors", "viewQuery", "ProspectsComponent_Query", "rf", "ctx", "ɵɵtwoWayListener", "ProspectsComponent_Template_input_ngModelChange_8_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "ProspectsComponent_Template_input_input_8_listener", "ProspectsComponent_Template_p_dropdown_ngModelChange_11_listener", "ProspectsComponent_Template_p_dropdown_onChange_11_listener", "ProspectsComponent_Template_button_click_12_listener", "ProspectsComponent_Template_p_multiSelect_ngModelChange_16_listener", "ProspectsComponent_Template_p_table_onLazyLoad_18_listener", "ProspectsComponent_Template_p_table_onColReorder_18_listener", "ProspectsComponent_ng_template_20_Template", "ProspectsComponent_ng_template_21_Template", "ProspectsComponent_ng_template_22_Template", "ProspectsComponent_ng_template_23_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ProspectsService } from './prospects.service';\r\nimport { Table } from 'primeng/table';\r\nimport { Router } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-prospects',\r\n  templateUrl: './prospects.component.html',\r\n  styleUrls: ['./prospects.component.scss'],\r\n})\r\nexport class ProspectsComponent implements OnInit {\r\n  @ViewChild('dt1') dt1!: Table;\r\n  private unsubscribe$ = new Subject<void>();\r\n  public breadcrumbitems: MenuItem[] | any;\r\n  public home: MenuItem | any;\r\n  public Actions: Actions[] | undefined;\r\n  public selectedActions: Actions | undefined;\r\n  public prospects: any[] = [];\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n  public partner_role: string = '';\r\n  public searchInputChanged: Subject<string> = new Subject<string>();\r\n\r\n  constructor(\r\n    private prospectsservice: ProspectsService,\r\n    private router: Router,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) {}\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'bp_full_name', header: 'Name' },\r\n    { field: 'addresses.emails.email_address', header: 'Email' },\r\n    { field: 'addresses.city_name', header: 'City' },\r\n    { field: 'addresses.country', header: 'Country' },\r\n    { field: 'contact_companies.business_partner_person.first_name', header: 'Contact' },\r\n    { field: 'addresses.phone_numbers.phone_number', header: 'Phone' },\r\n    { field: 'customer.partner_functions.business_partner.bp_full_name', header: 'Owner' },\r\n  ];\r\n\r\n  public sortField: string = '';\r\n  public sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.prospects.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.searchInputChanged\r\n      .pipe(\r\n        debounceTime(500), // Adjust delay here (ms)\r\n        distinctUntilChanged()\r\n      )\r\n      .subscribe((term: string) => {\r\n        this.globalSearchTerm = term;\r\n        this.loadProspects({ first: 0, rows: 15 });\r\n      });\r\n    this.breadcrumbitems = [\r\n      { label: 'Prospects', routerLink: ['/store/prospects'] },\r\n    ];\r\n    this.home = { icon: 'pi pi-home', routerLink: ['/'] };\r\n    this.Actions = [\r\n      { name: 'All', code: 'ALL' },\r\n      { name: 'My Prospects', code: 'MP' },\r\n      { name: 'Obsolete Prospects', code: 'OP' },\r\n    ];\r\n    this.selectedActions = { name: 'All', code: 'ALL' };\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter((col) => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  loadProspects(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n    const obsolete = this.selectedActions?.code === 'OP';\r\n    const myprospect = this.selectedActions?.code === 'MP';\r\n\r\n    this.prospectsservice\r\n      .getProspects(\r\n        page,\r\n        pageSize,\r\n        sortField,\r\n        sortOrder,\r\n        this.globalSearchTerm,\r\n        obsolete,\r\n        myprospect\r\n      )\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          let prospects =\r\n            response?.data.map((prospect: any) => {\r\n              const defaultAddress = prospect.addresses?.find(\r\n                (address: any) => {\r\n                  return address.address_usages.find(\r\n                    (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n                  );\r\n                }\r\n              );\r\n              const partner_role = prospect?.customer?.partner_functions?.find(\r\n                (p: any) => p.partner_function === 'YI'\r\n              );\r\n\r\n              return {\r\n                ...prospect,\r\n                contact_name:\r\n                  (prospect?.contact_companies?.[0]?.business_partner_person\r\n                    ?.first_name || '') +\r\n                  ' ' +\r\n                  (prospect?.contact_companies?.[0]?.business_partner_person\r\n                    ?.last_name || '-'),\r\n                city_name: defaultAddress?.city_name || '-',\r\n                country: defaultAddress?.country || '-',\r\n                email_address:\r\n                  defaultAddress?.emails?.[0]?.email_address || '-',\r\n                phone_number: (() => {\r\n                  const phoneList = defaultAddress?.phone_numbers ?? [];\r\n                  const mobilePhone = phoneList.find(\r\n                    (item: any) => item.phone_number_type === '1'\r\n                  );\r\n                  const countryCode = mobilePhone?.destination_location_country;\r\n                  const phoneNumber = mobilePhone?.phone_number;\r\n                  if (!phoneNumber) {\r\n                    return '-';\r\n                  }\r\n                  return this.prospectsservice.getDialCode(\r\n                    countryCode,\r\n                    phoneNumber\r\n                  );\r\n                })(),\r\n                partner_role:\r\n                  partner_role?.business_partner?.bp_full_name || null,\r\n              };\r\n            }) || [];\r\n\r\n          this.prospects = prospects;\r\n          this.totalRecords = response?.meta?.pagination.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching prospects', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onActionChange() {\r\n    // Re-trigger the lazy load with current dt1 state\r\n    const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\r\n      first: 0,\r\n      rows: 15,\r\n    };\r\n    this.loadProspects(dt1State);\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.prospectsservice\r\n      .delete(item.documentId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.refresh();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  refresh() {\r\n    this.loadProspects({ first: 0, rows: 15 });\r\n  }\r\n\r\n  signup() {\r\n    this.router.navigate(['/store/prospects/create']);\r\n  }\r\n\r\n  onSearchInputChange(event: Event) {\r\n    const input = (event.target as HTMLInputElement).value;\r\n    this.searchInputChanged.next(input);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-right\" [life]=\"3000\"></p-toast>\r\n<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-start justify-content-between flex-column gap-2 md:flex-row md:align-items-center\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-start gap-3 md:flex-row md:align-items-center flex-wrap w-full md:w-auto\">\r\n            <!-- Search Box -->\r\n            <div class=\"h-search-box w-full sm:w-24rem\">\r\n                <span class=\"p-input-icon-right w-full md:w-24rem\">\r\n                    <input type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\" (input)=\"onSearchInputChange($event)\"\r\n                        placeholder=\"Search Prospect\"\r\n                        class=\"p-inputtext p-component p-element w-full sm:w-24rem h-3rem px-3 border-round-3xl border-1 surface-border\">\r\n                    <i class=\"pi pi-search\" style=\"right: 16px;\"></i>\r\n                </span>\r\n            </div>\r\n            <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" optionLabel=\"name\" placeholder=\"Filter\"\r\n                (onChange)=\"onActionChange()\" class=\"w-full sm:w-13rem\"\r\n                [styleClass]=\"'w-full sm:w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" />\r\n            <button type=\"button\" (click)=\"signup()\"\r\n                class=\"h-3rem p-element p-ripple p-button p-component border-round-3xl w-8rem justify-content-center gap-2 font-semibold border-none ml-auto sm:ml-0\">\r\n                <span class=\"material-symbols-rounded text-2xl\">box_edit</span> Create\r\n            </button>\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n\r\n        <p-table #dt1 [value]=\"prospects\" dataKey=\"id\" [rows]=\"14\" (onLazyLoad)=\"loadProspects($event)\"\r\n            [loading]=\"loading\" [paginator]=\"true\" [totalRecords]=\"totalRecords\" [lazy]=\"true\" responsiveLayout=\"scroll\"\r\n            [scrollable]=\"true\" class=\"scrollable-table\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center table-checkbox\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pFrozenColumn (click)=\"customSort('bp_id')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Prospect ID\r\n                            <i *ngIf=\"sortField === 'bp_id'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'bp_id'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-prospect let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"prospect\" />\r\n                    </td>\r\n                    <td pFrozenColumn class=\"text-orange-600 cursor-pointer font-medium underline\"\r\n                        [routerLink]=\"'/store/prospects/' + prospect.bp_id\">\r\n                        {{ prospect?.bp_id || '-' }}\r\n                    </td>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n\r\n                                <ng-container *ngSwitchCase=\"'bp_full_name'\">\r\n                                    <span class=\"text-blue-600 font-medium underline cursor-pointer\"\r\n                                        [routerLink]=\"'/store/prospects/' + prospect.bp_id \">\r\n                                        {{ prospect?.bp_full_name || '-' }}\r\n                                    </span>\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'addresses.emails.email_address'\">\r\n                                    {{ prospect?.email_address || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'addresses.city_name'\">\r\n                                    {{ prospect?.city_name || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'addresses.country'\">\r\n                                    {{ prospect?.country || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'contact_companies.business_partner_person.first_name'\">\r\n                                    {{prospect?.contact_name || \"-\"}}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'addresses.phone_numbers.phone_number'\">\r\n                                    {{prospect?.phone_number || \"-\"}}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'customer.partner_functions.business_partner.bp_full_name'\">\r\n                                    {{prospect?.partner_role || \"-\"}}\r\n                                </ng-container>\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"9\" class=\"border-round-left-lg pl-3\">No prospects found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"9\" class=\"border-round-left-lg pl-3\">Loading prospects data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-confirmDialog></p-confirmDialog>"], "mappings": "AAKA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAEzC,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,gBAAgB;;;;;;;;;;;;;;;;ICuCvCC,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA6D;;;;;IAOzDD,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;;IAP3ED,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,aAAqF;IAAhCN,EAAA,CAAAO,UAAA,mBAAAC,8EAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAC,6DAAA,gBACkF,IAAAC,6DAAA,gBAEvB;IAEnEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;IAG7BhB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;;;;;;IAnB7ChB,EADJ,CAAAM,cAAA,SAAI,aACsF;IAClFN,EAAA,CAAAC,SAAA,4BAAyB;IAC7BD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAM,cAAA,aAAgD;IAA9BN,EAAA,CAAAO,UAAA,mBAAAmB,+DAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,OAAO,CAAC;IAAA,EAAC;IAC3Cf,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,oBACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAU,8CAAA,gBACkF,IAAAC,8CAAA,gBAEzB;IAEjE7B,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAY,yDAAA,2BAAkD;IAWtD9B,EAAA,CAAAqB,YAAA,EAAK;;;;IAjBWrB,EAAA,CAAAsB,SAAA,GAA2B;IAA3BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,aAA2B;IAG3BzB,EAAA,CAAAsB,SAAA,EAA2B;IAA3BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,aAA2B;IAGTzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IA4BpC/B,EAAA,CAAAK,uBAAA,GAA6C;IACzCL,EAAA,CAAAM,cAAA,eACyD;IACrDN,EAAA,CAAAiB,MAAA,GACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;;IAFHrB,EAAA,CAAAsB,SAAA,EAAoD;IAApDtB,EAAA,CAAAE,UAAA,qCAAA8B,WAAA,CAAAC,KAAA,CAAoD;IACpDjC,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,WAAA,kBAAAA,WAAA,CAAAE,YAAA,cACJ;;;;;IAGJlC,EAAA,CAAAK,uBAAA,GAA+D;IAC3DL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,WAAA,kBAAAA,WAAA,CAAAG,aAAA,cACJ;;;;;IAEAnC,EAAA,CAAAK,uBAAA,GAAoD;IAChDL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,WAAA,kBAAAA,WAAA,CAAAI,SAAA,cACJ;;;;;IAEApC,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,WAAA,kBAAAA,WAAA,CAAAK,OAAA,cACJ;;;;;IAEArC,EAAA,CAAAK,uBAAA,GAAqF;IACjFL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,WAAA,kBAAAA,WAAA,CAAAM,YAAA,cACJ;;;;;IAEAtC,EAAA,CAAAK,uBAAA,GAAqE;IACjEL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,WAAA,kBAAAA,WAAA,CAAAO,YAAA,cACJ;;;;;IAEAvC,EAAA,CAAAK,uBAAA,GAAyF;IACrFL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,WAAA,kBAAAA,WAAA,CAAAQ,YAAA,cACJ;;;;;IAjCZxC,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IA6BjCL,EA3BA,CAAAkB,UAAA,IAAAuB,wEAAA,2BAA6C,IAAAC,wEAAA,2BAOkB,IAAAC,wEAAA,2BAIX,IAAAC,wEAAA,2BAIF,IAAAC,wEAAA,2BAImC,IAAAC,wEAAA,2BAIhB,IAAAC,wEAAA,2BAIoB;;IAIjG/C,EAAA,CAAAqB,YAAA,EAAK;;;;;IAjCarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAA8C,MAAA,CAAAhC,KAAA,CAAsB;IAEjBhB,EAAA,CAAAsB,SAAA,EAA4B;IAA5BtB,EAAA,CAAAE,UAAA,gCAA4B;IAO5BF,EAAA,CAAAsB,SAAA,EAA8C;IAA9CtB,EAAA,CAAAE,UAAA,kDAA8C;IAI9CF,EAAA,CAAAsB,SAAA,EAAmC;IAAnCtB,EAAA,CAAAE,UAAA,uCAAmC;IAInCF,EAAA,CAAAsB,SAAA,EAAiC;IAAjCtB,EAAA,CAAAE,UAAA,qCAAiC;IAIjCF,EAAA,CAAAsB,SAAA,EAAoE;IAApEtB,EAAA,CAAAE,UAAA,wEAAoE;IAIpEF,EAAA,CAAAsB,SAAA,EAAoD;IAApDtB,EAAA,CAAAE,UAAA,wDAAoD;IAIpDF,EAAA,CAAAsB,SAAA,EAAwE;IAAxEtB,EAAA,CAAAE,UAAA,4EAAwE;;;;;IAvCnGF,EADJ,CAAAM,cAAA,aAA2B,aACgD;IACnEN,EAAA,CAAAC,SAAA,0BAAsC;IAC1CD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAM,cAAA,aACwD;IACpDN,EAAA,CAAAiB,MAAA,GACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;IAELrB,EAAA,CAAAkB,UAAA,IAAA+B,yDAAA,4BAAkD;IAqCtDjD,EAAA,CAAAqB,YAAA,EAAK;;;;;IA5CoBrB,EAAA,CAAAsB,SAAA,GAAkB;IAAlBtB,EAAA,CAAAE,UAAA,UAAA8B,WAAA,CAAkB;IAGnChC,EAAA,CAAAsB,SAAA,EAAmD;IAAnDtB,EAAA,CAAAE,UAAA,qCAAA8B,WAAA,CAAAC,KAAA,CAAmD;IACnDjC,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,WAAA,kBAAAA,WAAA,CAAAC,KAAA,cACJ;IAE8BjC,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IA0ChD/B,EADJ,CAAAM,cAAA,SAAI,aACkD;IAAAN,EAAA,CAAAiB,MAAA,0BAAmB;IACzEjB,EADyE,CAAAqB,YAAA,EAAK,EACzE;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aACkD;IAAAN,EAAA,CAAAiB,MAAA,6CAAsC;IAC5FjB,EAD4F,CAAAqB,YAAA,EAAK,EAC5F;;;ADpGrB,OAAM,MAAO6B,kBAAkB;EAc7BC,YACUC,gBAAkC,EAClCC,MAAc,EACdC,cAA8B,EAC9BC,mBAAwC;IAHxC,KAAAH,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAhBrB,KAAAC,YAAY,GAAG,IAAI5D,OAAO,EAAQ;IAKnC,KAAA6D,SAAS,GAAU,EAAE;IACrB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAApB,YAAY,GAAW,EAAE;IACzB,KAAAqB,kBAAkB,GAAoB,IAAIjE,OAAO,EAAU;IAS1D,KAAAkE,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAE/C,KAAK,EAAE,cAAc;MAAEQ,MAAM,EAAE;IAAM,CAAE,EACzC;MAAER,KAAK,EAAE,gCAAgC;MAAEQ,MAAM,EAAE;IAAO,CAAE,EAC5D;MAAER,KAAK,EAAE,qBAAqB;MAAEQ,MAAM,EAAE;IAAM,CAAE,EAChD;MAAER,KAAK,EAAE,mBAAmB;MAAEQ,MAAM,EAAE;IAAS,CAAE,EACjD;MAAER,KAAK,EAAE,sDAAsD;MAAEQ,MAAM,EAAE;IAAS,CAAE,EACpF;MAAER,KAAK,EAAE,sCAAsC;MAAEQ,MAAM,EAAE;IAAO,CAAE,EAClE;MAAER,KAAK,EAAE,0DAA0D;MAAEQ,MAAM,EAAE;IAAO,CAAE,CACvF;IAEM,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAArB,SAAS,GAAW,CAAC;EAfzB;EAiBHW,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACS,SAAS,KAAKT,KAAK,EAAE;MAC5B,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACqB,SAAS,GAAGT,KAAK;MACtB,IAAI,CAACZ,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAACqD,SAAS,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAEjD,KAAK,CAAC;MAC9C,MAAMqD,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAElD,KAAK,CAAC;MAE9C,IAAIsD,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAGH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MAE5D,OAAO,IAAI,CAACjE,SAAS,GAAGkE,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAExD,KAAa;IACvC,IAAI,CAACwD,IAAI,IAAI,CAACxD,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAACyD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAACxD,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAAC0D,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACjB,kBAAkB,CACpBkB,IAAI,CACHjF,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBC,oBAAoB,EAAE,CACvB,CACAiF,SAAS,CAAEC,IAAY,IAAI;MAC1B,IAAI,CAACrB,gBAAgB,GAAGqB,IAAI;MAC5B,IAAI,CAACC,aAAa,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAE,CAAE,CAAC;IAC5C,CAAC,CAAC;IACJ,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE,WAAW;MAAEC,UAAU,EAAE,CAAC,kBAAkB;IAAC,CAAE,CACzD;IACD,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IACrD,IAAI,CAACG,OAAO,GAAG,CACb;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC5B;MAAED,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE;IAAI,CAAE,EACpC;MAAED,IAAI,EAAE,oBAAoB;MAAEC,IAAI,EAAE;IAAI,CAAE,CAC3C;IACD,IAAI,CAACC,eAAe,GAAG;MAAEF,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAE;IAEnD,IAAI,CAAC9B,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEA,IAAIhC,eAAeA,CAAA;IACjB,OAAO,IAAI,CAAC+B,gBAAgB;EAC9B;EAEA,IAAI/B,eAAeA,CAAC+D,GAAU;IAC5B,IAAI,CAAChC,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAACgC,MAAM,CAAEC,GAAG,IAAKF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACtE;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAACtC,gBAAgB,CAACqC,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAACvC,gBAAgB,CAACwC,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAACvC,gBAAgB,CAACwC,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;EAEAlB,aAAaA,CAACiB,KAAU;IACtB,IAAI,CAACxC,OAAO,GAAG,IAAI;IACnB,MAAM6C,IAAI,GAAGL,KAAK,CAAChB,KAAK,GAAGgB,KAAK,CAACf,IAAI,GAAG,CAAC;IACzC,MAAMqB,QAAQ,GAAGN,KAAK,CAACf,IAAI;IAC3B,MAAM3D,SAAS,GAAG0E,KAAK,CAAC1E,SAAS;IACjC,MAAMrB,SAAS,GAAG+F,KAAK,CAAC/F,SAAS;IACjC,MAAMsG,QAAQ,GAAG,IAAI,CAACb,eAAe,EAAED,IAAI,KAAK,IAAI;IACpD,MAAMe,UAAU,GAAG,IAAI,CAACd,eAAe,EAAED,IAAI,KAAK,IAAI;IAEtD,IAAI,CAACxC,gBAAgB,CAClBwD,YAAY,CACXJ,IAAI,EACJC,QAAQ,EACRhF,SAAS,EACTrB,SAAS,EACT,IAAI,CAACwD,gBAAgB,EACrB8C,QAAQ,EACRC,UAAU,CACX,CACA5B,IAAI,CAAClF,SAAS,CAAC,IAAI,CAAC2D,YAAY,CAAC,CAAC,CAClCwB,SAAS,CAAC;MACT6B,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIrD,SAAS,GACXqD,QAAQ,EAAEtC,IAAI,CAACuC,GAAG,CAAEC,QAAa,IAAI;UACnC,MAAMC,cAAc,GAAGD,QAAQ,CAACE,SAAS,EAAEC,IAAI,CAC5CC,OAAY,IAAI;YACf,OAAOA,OAAO,CAACC,cAAc,CAACF,IAAI,CAC/BG,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD;UACH,CAAC,CACF;UACD,MAAM/E,YAAY,GAAGwE,QAAQ,EAAEQ,QAAQ,EAAEC,iBAAiB,EAAEN,IAAI,CAC7DO,CAAM,IAAKA,CAAC,CAACC,gBAAgB,KAAK,IAAI,CACxC;UAED,OAAO;YACL,GAAGX,QAAQ;YACX1E,YAAY,EACV,CAAC0E,QAAQ,EAAEY,iBAAiB,GAAG,CAAC,CAAC,EAAEC,uBAAuB,EACtDC,UAAU,IAAI,EAAE,IACpB,GAAG,IACFd,QAAQ,EAAEY,iBAAiB,GAAG,CAAC,CAAC,EAAEC,uBAAuB,EACtDE,SAAS,IAAI,GAAG,CAAC;YACvB3F,SAAS,EAAE6E,cAAc,EAAE7E,SAAS,IAAI,GAAG;YAC3CC,OAAO,EAAE4E,cAAc,EAAE5E,OAAO,IAAI,GAAG;YACvCF,aAAa,EACX8E,cAAc,EAAEe,MAAM,GAAG,CAAC,CAAC,EAAE7F,aAAa,IAAI,GAAG;YACnDI,YAAY,EAAE,CAAC,MAAK;cAClB,MAAM0F,SAAS,GAAGhB,cAAc,EAAEiB,aAAa,IAAI,EAAE;cACrD,MAAMC,WAAW,GAAGF,SAAS,CAACd,IAAI,CAC/BiB,IAAS,IAAKA,IAAI,CAACC,iBAAiB,KAAK,GAAG,CAC9C;cACD,MAAMC,WAAW,GAAGH,WAAW,EAAEI,4BAA4B;cAC7D,MAAMC,WAAW,GAAGL,WAAW,EAAE5F,YAAY;cAC7C,IAAI,CAACiG,WAAW,EAAE;gBAChB,OAAO,GAAG;cACZ;cACA,OAAO,IAAI,CAACpF,gBAAgB,CAACqF,WAAW,CACtCH,WAAW,EACXE,WAAW,CACZ;YACH,CAAC,EAAC,CAAE;YACJhG,YAAY,EACVA,YAAY,EAAEkG,gBAAgB,EAAExG,YAAY,IAAI;WACnD;QACH,CAAC,CAAC,IAAI,EAAE;QAEV,IAAI,CAACuB,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACC,YAAY,GAAGoD,QAAQ,EAAE6B,IAAI,EAAEC,UAAU,CAACC,KAAK;QACpD,IAAI,CAAClF,OAAO,GAAG,KAAK;MACtB,CAAC;MACDmF,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACnF,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEAqF,cAAcA,CAAA;IACZ;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,GAAG,EAAEC,sBAAsB,GAAE,CAAE,IAAI;MACvDhE,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;KACP;IACD,IAAI,CAACF,aAAa,CAAC+D,QAAQ,CAAC;EAC9B;EAEAG,aAAaA,CAAChB,IAAS;IACrB,IAAI,CAAC7E,mBAAmB,CAAC8F,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChE9H,MAAM,EAAE,SAAS;MACjBiE,IAAI,EAAE,4BAA4B;MAClC8D,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAACpB,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEAoB,MAAMA,CAACpB,IAAS;IACd,IAAI,CAAChF,gBAAgB,CAClBqG,MAAM,CAACrB,IAAI,CAACsB,UAAU,CAAC,CACvB3E,IAAI,CAAClF,SAAS,CAAC,IAAI,CAAC2D,YAAY,CAAC,CAAC,CAClCwB,SAAS,CAAC;MACT6B,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACvD,cAAc,CAACqG,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACC,OAAO,EAAE;MAChB,CAAC;MACDhB,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACxF,cAAc,CAACqG,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAC,OAAOA,CAAA;IACL,IAAI,CAAC5E,aAAa,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC5C;EAEA2E,MAAMA,CAAA;IACJ,IAAI,CAAC1G,MAAM,CAAC2G,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEAC,mBAAmBA,CAAC9D,KAAY;IAC9B,MAAM+D,KAAK,GAAI/D,KAAK,CAACgE,MAA2B,CAACC,KAAK;IACtD,IAAI,CAACvG,kBAAkB,CAACgD,IAAI,CAACqD,KAAK,CAAC;EACrC;EAEAG,WAAWA,CAAA;IACT,IAAI,CAAC7G,YAAY,CAACqD,IAAI,EAAE;IACxB,IAAI,CAACrD,YAAY,CAAC8G,QAAQ,EAAE;EAC9B;;;uBApPWpH,kBAAkB,EAAAlD,EAAA,CAAAuK,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAzK,EAAA,CAAAuK,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA3K,EAAA,CAAAuK,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA7K,EAAA,CAAAuK,iBAAA,CAAAK,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAAlB5H,kBAAkB;MAAA6H,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCxB/BlL,EAAA,CAAAC,SAAA,iBAAsD;UAG9CD,EAFR,CAAAM,cAAA,aAA8D,aACsE,aAChG;UACxBN,EAAA,CAAAC,SAAA,sBAA+F;UACnGD,EAAA,CAAAqB,YAAA,EAAM;UAKMrB,EAJZ,CAAAM,cAAA,aAAuG,aAEvD,cACW,mBAGsE;UAF1FN,EAAA,CAAAoL,gBAAA,2BAAAC,2DAAAC,MAAA;YAAAtL,EAAA,CAAAU,aAAA,CAAA6K,GAAA;YAAAvL,EAAA,CAAAwL,kBAAA,CAAAL,GAAA,CAAAvH,gBAAA,EAAA0H,MAAA,MAAAH,GAAA,CAAAvH,gBAAA,GAAA0H,MAAA;YAAA,OAAAtL,EAAA,CAAAc,WAAA,CAAAwK,MAAA;UAAA,EAA8B;UAACtL,EAAA,CAAAO,UAAA,mBAAAkL,mDAAAH,MAAA;YAAAtL,EAAA,CAAAU,aAAA,CAAA6K,GAAA;YAAA,OAAAvL,EAAA,CAAAc,WAAA,CAASqK,GAAA,CAAAlB,mBAAA,CAAAqB,MAAA,CAA2B;UAAA,EAAC;UAA/FtL,EAAA,CAAAqB,YAAA,EAEqH;UACrHrB,EAAA,CAAAC,SAAA,aAAiD;UAEzDD,EADI,CAAAqB,YAAA,EAAO,EACL;UACNrB,EAAA,CAAAM,cAAA,sBAEmH;UAFnFN,EAAA,CAAAoL,gBAAA,2BAAAM,iEAAAJ,MAAA;YAAAtL,EAAA,CAAAU,aAAA,CAAA6K,GAAA;YAAAvL,EAAA,CAAAwL,kBAAA,CAAAL,GAAA,CAAAtF,eAAA,EAAAyF,MAAA,MAAAH,GAAA,CAAAtF,eAAA,GAAAyF,MAAA;YAAA,OAAAtL,EAAA,CAAAc,WAAA,CAAAwK,MAAA;UAAA,EAA6B;UACzDtL,EAAA,CAAAO,UAAA,sBAAAoL,4DAAA;YAAA3L,EAAA,CAAAU,aAAA,CAAA6K,GAAA;YAAA,OAAAvL,EAAA,CAAAc,WAAA,CAAYqK,GAAA,CAAAnC,cAAA,EAAgB;UAAA,EAAC;UADjChJ,EAAA,CAAAqB,YAAA,EAEmH;UACnHrB,EAAA,CAAAM,cAAA,kBAC0J;UADpIN,EAAA,CAAAO,UAAA,mBAAAqL,qDAAA;YAAA5L,EAAA,CAAAU,aAAA,CAAA6K,GAAA;YAAA,OAAAvL,EAAA,CAAAc,WAAA,CAASqK,GAAA,CAAApB,MAAA,EAAQ;UAAA,EAAC;UAEpC/J,EAAA,CAAAM,cAAA,gBAAgD;UAAAN,EAAA,CAAAiB,MAAA,gBAAQ;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAACrB,EAAA,CAAAiB,MAAA,gBACpE;UAAAjB,EAAA,CAAAqB,YAAA,EAAS;UAETrB,EAAA,CAAAM,cAAA,yBAE+I;UAF/GN,EAAA,CAAAoL,gBAAA,2BAAAS,oEAAAP,MAAA;YAAAtL,EAAA,CAAAU,aAAA,CAAA6K,GAAA;YAAAvL,EAAA,CAAAwL,kBAAA,CAAAL,GAAA,CAAApJ,eAAA,EAAAuJ,MAAA,MAAAH,GAAA,CAAApJ,eAAA,GAAAuJ,MAAA;YAAA,OAAAtL,EAAA,CAAAc,WAAA,CAAAwK,MAAA;UAAA,EAA6B;UAKrEtL,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAIFrB,EAFJ,CAAAM,cAAA,eAAuB,sBAK0B;UAAzCN,EAHuD,CAAAO,UAAA,wBAAAuL,2DAAAR,MAAA;YAAAtL,EAAA,CAAAU,aAAA,CAAA6K,GAAA;YAAA,OAAAvL,EAAA,CAAAc,WAAA,CAAcqK,GAAA,CAAAjG,aAAA,CAAAoG,MAAA,CAAqB;UAAA,EAAC,0BAAAS,6DAAAT,MAAA;YAAAtL,EAAA,CAAAU,aAAA,CAAA6K,GAAA;YAAA,OAAAvL,EAAA,CAAAc,WAAA,CAG3EqK,GAAA,CAAAjF,eAAA,CAAAoF,MAAA,CAAuB;UAAA,EAAC;UAqFxCtL,EAnFA,CAAAkB,UAAA,KAAA8K,0CAAA,0BAAgC,KAAAC,0CAAA,0BA4BiC,KAAAC,0CAAA,0BAkD3B,KAAAC,0CAAA,0BAKD;UAOjDnM,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;UACNrB,EAAA,CAAAC,SAAA,uBAAmC;;;UAjILD,EAAA,CAAAE,UAAA,cAAa;UAIjBF,EAAA,CAAAsB,SAAA,GAAyB;UAAetB,EAAxC,CAAAE,UAAA,UAAAiL,GAAA,CAAA9F,eAAA,CAAyB,SAAA8F,GAAA,CAAA3F,IAAA,CAAc,uCAAuC;UAMzDxF,EAAA,CAAAsB,SAAA,GAA8B;UAA9BtB,EAAA,CAAAoM,gBAAA,YAAAjB,GAAA,CAAAvH,gBAAA,CAA8B;UAMrD5D,EAAA,CAAAsB,SAAA,GAAmB;UAAnBtB,EAAA,CAAAE,UAAA,YAAAiL,GAAA,CAAAzF,OAAA,CAAmB;UAAC1F,EAAA,CAAAoM,gBAAA,YAAAjB,GAAA,CAAAtF,eAAA,CAA6B;UAEzD7F,EAAA,CAAAE,UAAA,6GAA4G;UAMjGF,EAAA,CAAAsB,SAAA,GAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAAiL,GAAA,CAAApH,IAAA,CAAgB;UAAC/D,EAAA,CAAAoM,gBAAA,YAAAjB,GAAA,CAAApJ,eAAA,CAA6B;UAEzD/B,EAAA,CAAAE,UAAA,2IAA0I;UAOpIF,EAAA,CAAAsB,SAAA,GAAmB;UAEgBtB,EAFnC,CAAAE,UAAA,UAAAiL,GAAA,CAAA1H,SAAA,CAAmB,YAAyB,YAAA0H,GAAA,CAAAxH,OAAA,CACnC,mBAAmB,iBAAAwH,GAAA,CAAAzH,YAAA,CAA8B,cAAc,oBAC/D,4BAAqD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}