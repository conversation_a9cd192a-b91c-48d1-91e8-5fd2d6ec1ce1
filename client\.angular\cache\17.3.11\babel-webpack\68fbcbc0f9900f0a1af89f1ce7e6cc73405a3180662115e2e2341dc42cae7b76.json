{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../activities.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ng-select/ng-select\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/calendar\";\nimport * as i11 from \"primeng/inputtext\";\nimport * as i12 from \"primeng/dialog\";\nimport * as i13 from \"primeng/editor\";\nimport * as i14 from \"primeng/multiselect\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = () => ({\n  height: \"200px\"\n});\nconst _c2 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction SalesCallOverviewComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"label\", 29)(4, \"span\", 30);\n    i0.ɵɵtext(5, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 27)(10, \"div\", 28)(11, \"label\", 29)(12, \"span\", 30);\n    i0.ɵɵtext(13, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Subject \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 31);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 27)(18, \"div\", 28)(19, \"label\", 29)(20, \"span\", 30);\n    i0.ɵɵtext(21, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \"Transaction Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 31);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 27)(26, \"div\", 28)(27, \"label\", 29)(28, \"span\", 30);\n    i0.ɵɵtext(29, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Account \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 32)(32, \"a\", 33);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 27)(35, \"div\", 28)(36, \"label\", 29)(37, \"span\", 30);\n    i0.ɵɵtext(38, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(39, \" Contact \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 32)(41, \"a\", 33);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(43, \"div\", 27)(44, \"div\", 28)(45, \"label\", 29)(46, \"span\", 30);\n    i0.ɵɵtext(47, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(48, \" Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 31);\n    i0.ɵɵtext(50);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(51, \"div\", 27)(52, \"div\", 28)(53, \"label\", 29)(54, \"span\", 30);\n    i0.ɵɵtext(55, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(56, \" Disposition Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\", 31);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(59, \"div\", 27)(60, \"div\", 28)(61, \"label\", 29)(62, \"span\", 30);\n    i0.ɵɵtext(63, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(64, \" Reason \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"div\", 31);\n    i0.ɵɵtext(66);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(67, \"div\", 27)(68, \"div\", 28)(69, \"label\", 29)(70, \"span\", 30);\n    i0.ɵɵtext(71, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(72, \" Call Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"div\", 31);\n    i0.ɵɵtext(74);\n    i0.ɵɵpipe(75, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(76, \"div\", 27)(77, \"div\", 28)(78, \"label\", 29)(79, \"span\", 30);\n    i0.ɵɵtext(80, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(81, \" End Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"div\", 31);\n    i0.ɵɵtext(83);\n    i0.ɵɵpipe(84, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(85, \"div\", 27)(86, \"div\", 28)(87, \"label\", 29)(88, \"span\", 30);\n    i0.ɵɵtext(89, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(90, \" Owner \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"div\", 31);\n    i0.ɵɵtext(92);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(93, \"div\", 27)(94, \"div\", 28)(95, \"label\", 29)(96, \"span\", 30);\n    i0.ɵɵtext(97, \"branding_watermark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(98, \" Brand \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(99, \"div\", 31);\n    i0.ɵɵtext(100);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(101, \"div\", 27)(102, \"div\", 28)(103, \"label\", 29)(104, \"span\", 30);\n    i0.ɵɵtext(105, \"group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(106, \" Customer Group \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(107, \"div\", 31);\n    i0.ɵɵtext(108);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(109, \"div\", 27)(110, \"div\", 28)(111, \"label\", 29)(112, \"span\", 30);\n    i0.ɵɵtext(113, \"emoji_events\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(114, \" Ranking \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(115, \"div\", 31);\n    i0.ɵɵtext(116);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(117, \"div\", 27)(118, \"div\", 28)(119, \"label\", 29)(120, \"span\", 30);\n    i0.ɵɵtext(121, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(122, \" Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(123, \"div\", 31);\n    i0.ɵɵtext(124);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(125, \"div\", 27)(126, \"div\", 28)(127, \"label\", 29)(128, \"span\", 30);\n    i0.ɵɵtext(129, \"label\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(130, \" Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(131, \"div\", 31);\n    i0.ɵɵtext(132);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(133, \"div\", 27)(134, \"div\", 28)(135, \"label\", 29)(136, \"span\", 30);\n    i0.ɵɵtext(137, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(138, \" Customer TimeZone \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(139, \"div\", 31);\n    i0.ɵɵtext(140);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.activity_id) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.subject) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"activityDocumentType\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.document_type) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"href\", \"/#/store/account/\" + (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner == null ? null : ctx_r0.overviewDetails.business_partner.documentId) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner == null ? null : ctx_r0.overviewDetails.business_partner.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"href\", \"/#/store/contacts/\" + (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_contact == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons[0] == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons[0].documentId) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_contact == null ? null : ctx_r0.overviewDetails.business_partner_contact.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"activityCategory\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.phone_call_category) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"activitydisposition\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.disposition_code) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.reason) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.start_date) ? i0.ɵɵpipeBind3(75, 19, ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.start_date, \"MM-dd-yyyy hh:mm a\", \"CST\") + \" CST\" : \"-\", \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.end_date) ? i0.ɵɵpipeBind3(84, 23, ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.end_date, \"MM-dd-yyyy hh:mm a\", \"CST\") + \" CST\" : \"-\", \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_owner == null ? null : ctx_r0.overviewDetails.business_partner_owner.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.brand) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.customer_group) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.ranking) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"activityStatus\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.activity_status) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"activityInitiatorCode\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.initiator_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.customer_timezone) || \"-\", \" \");\n  }\n}\nfunction SalesCallOverviewComponent_form_6_ng_template_12_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.bp_full_name, \"\");\n  }\n}\nfunction SalesCallOverviewComponent_form_6_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, SalesCallOverviewComponent_form_6_ng_template_12_span_2_Template, 2, 1, \"span\", 54);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.bp_full_name);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_form_6_div_13_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_account_party_id\"].errors && ctx_r0.f[\"main_account_party_id\"].errors[\"required\"]);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_ng_template_24_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r5.email, \"\");\n  }\n}\nfunction SalesCallOverviewComponent_form_6_ng_template_24_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r5.mobile, \"\");\n  }\n}\nfunction SalesCallOverviewComponent_form_6_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"input\", 57);\n    i0.ɵɵlistener(\"change\", function SalesCallOverviewComponent_form_6_ng_template_24_Template_input_change_1_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).item;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.toggleSelection(item_r5.bp_id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SalesCallOverviewComponent_form_6_ng_template_24_span_4_Template, 2, 1, \"span\", 54)(5, SalesCallOverviewComponent_form_6_ng_template_24_span_5_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.item;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r0.isSelected(item_r5.bp_id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r5.bp_id, \": \", item_r5.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.mobile);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_form_6_div_25_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_contact_party_id\"].errors && ctx_r0.f[\"main_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_35_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Subject is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_form_6_div_35_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"subject\"].errors && ctx_r0.f[\"subject\"].errors[\"required\"]);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_45_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Category is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_form_6_div_45_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"phone_call_category\"].errors && ctx_r0.f[\"phone_call_category\"].errors[\"required\"]);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_104_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_form_6_div_104_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"activity_status\"].errors && ctx_r0.f[\"activity_status\"].errors[\"required\"]);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_114_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_form_6_div_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_form_6_div_114_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"initiator_code\"].errors && ctx_r0.f[\"initiator_code\"].errors[\"required\"]);\n  }\n}\nfunction SalesCallOverviewComponent_form_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 34)(1, \"div\", 26)(2, \"div\", 27)(3, \"div\", 28)(4, \"label\", 35)(5, \"span\", 36);\n    i0.ɵɵtext(6, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \"Account \");\n    i0.ɵɵelementStart(8, \"span\", 37);\n    i0.ɵɵtext(9, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"ng-select\", 38);\n    i0.ɵɵpipe(11, \"async\");\n    i0.ɵɵtemplate(12, SalesCallOverviewComponent_form_6_ng_template_12_Template, 3, 2, \"ng-template\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, SalesCallOverviewComponent_form_6_div_13_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 27)(15, \"div\", 28)(16, \"label\", 35)(17, \"span\", 36);\n    i0.ɵɵtext(18, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19, \"Contact \");\n    i0.ɵɵelementStart(20, \"span\", 37);\n    i0.ɵɵtext(21, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"ng-select\", 41);\n    i0.ɵɵpipe(23, \"async\");\n    i0.ɵɵtemplate(24, SalesCallOverviewComponent_form_6_ng_template_24_Template, 6, 5, \"ng-template\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, SalesCallOverviewComponent_form_6_div_25_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 27)(27, \"div\", 28)(28, \"label\", 35)(29, \"span\", 36);\n    i0.ɵɵtext(30, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" Subject \");\n    i0.ɵɵelementStart(32, \"span\", 37);\n    i0.ɵɵtext(33, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(34, \"input\", 42);\n    i0.ɵɵtemplate(35, SalesCallOverviewComponent_form_6_div_35_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 27)(37, \"div\", 28)(38, \"label\", 35)(39, \"span\", 36);\n    i0.ɵɵtext(40, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41, \"Category \");\n    i0.ɵɵelementStart(42, \"span\", 37);\n    i0.ɵɵtext(43, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(44, \"p-dropdown\", 43);\n    i0.ɵɵtemplate(45, SalesCallOverviewComponent_form_6_div_45_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 27)(47, \"div\", 28)(48, \"label\", 35)(49, \"span\", 36);\n    i0.ɵɵtext(50, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(51, \"Disposition Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(52, \"p-dropdown\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 27)(54, \"div\", 28)(55, \"label\", 35)(56, \"span\", 36);\n    i0.ɵɵtext(57, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(58, \"Reason \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"input\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 27)(61, \"div\", 28)(62, \"label\", 35)(63, \"span\", 36);\n    i0.ɵɵtext(64, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(65, \"Call Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(66, \"p-calendar\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"div\", 27)(68, \"div\", 28)(69, \"label\", 35)(70, \"span\", 36);\n    i0.ɵɵtext(71, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(72, \"End Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(73, \"p-calendar\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(74, \"div\", 27)(75, \"div\", 28)(76, \"label\", 35)(77, \"span\", 36);\n    i0.ɵɵtext(78, \"branding_watermark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(79, \"Brand \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(80, \"input\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(81, \"div\", 27)(82, \"div\", 28)(83, \"label\", 35)(84, \"span\", 36);\n    i0.ɵɵtext(85, \"group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(86, \"Customer Group \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(87, \"input\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(88, \"div\", 27)(89, \"div\", 28)(90, \"label\", 35)(91, \"span\", 36);\n    i0.ɵɵtext(92, \"emoji_events\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(93, \"Ranking \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(94, \"input\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(95, \"div\", 27)(96, \"div\", 28)(97, \"label\", 35)(98, \"span\", 36);\n    i0.ɵɵtext(99, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(100, \"Status \");\n    i0.ɵɵelementStart(101, \"span\", 37);\n    i0.ɵɵtext(102, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(103, \"p-dropdown\", 51);\n    i0.ɵɵtemplate(104, SalesCallOverviewComponent_form_6_div_104_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(105, \"div\", 27)(106, \"div\", 28)(107, \"label\", 35)(108, \"span\", 36);\n    i0.ɵɵtext(109, \"label\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(110, \"Type \");\n    i0.ɵɵelementStart(111, \"span\", 37);\n    i0.ɵɵtext(112, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(113, \"p-dropdown\", 52);\n    i0.ɵɵtemplate(114, SalesCallOverviewComponent_form_6_div_114_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(115, \"div\", 53)(116, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_form_6_Template_button_click_116_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSubmit());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.SalesCallOverviewForm);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(11, 38, ctx_r0.accounts$))(\"hideSelected\", true)(\"loading\", ctx_r0.accountLoading)(\"minTermLength\", 0)(\"compareWith\", ctx_r0.compareById)(\"typeahead\", ctx_r0.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(42, _c2, ctx_r0.submitted && ctx_r0.f[\"main_account_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_account_party_id\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(23, 40, ctx_r0.contacts$))(\"hideSelected\", true)(\"loading\", ctx_r0.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx_r0.contactInput$)(\"maxSelectedItems\", 10)(\"multiple\", true)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(44, _c2, ctx_r0.submitted && ctx_r0.f[\"main_contact_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_contact_party_id\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(46, _c2, ctx_r0.submitted && ctx_r0.f[\"subject\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"subject\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activityCategory\"])(\"styleClass\", \"h-3rem w-full\")(\"ngClass\", i0.ɵɵpureFunction1(48, _c2, ctx_r0.submitted && ctx_r0.f[\"phone_call_category\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"phone_call_category\"].errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activitydisposition\"])(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(30);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activityStatus\"])(\"ngClass\", i0.ɵɵpureFunction1(50, _c2, ctx_r0.submitted && ctx_r0.f[\"activity_status\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"activity_status\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activityInitiatorCode\"])(\"ngClass\", i0.ɵɵpureFunction1(52, _c2, ctx_r0.submitted && ctx_r0.f[\"initiator_code\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"initiator_code\"].errors);\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_16_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 64);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.sortOrderNotes === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_16_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 65);\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_16_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 64);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.sortOrderNotes === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_16_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 65);\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_16_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 66);\n    i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_ng_template_16_ng_container_6_Template_th_click_1_listener() {\n      const col_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.customSort(col_r8.field, ctx_r0.notedetails, \"Notes\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 59);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, SalesCallOverviewComponent_ng_template_16_ng_container_6_i_4_Template, 1, 1, \"i\", 60)(5, SalesCallOverviewComponent_ng_template_16_ng_container_6_i_5_Template, 1, 0, \"i\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r8 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r8.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r8.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNotes === col_r8.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNotes !== col_r8.field);\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 58);\n    i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_ng_template_16_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.customSort(\"note\", ctx_r0.notedetails, \"Notes\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 59);\n    i0.ɵɵtext(3, \" Note \");\n    i0.ɵɵtemplate(4, SalesCallOverviewComponent_ng_template_16_i_4_Template, 1, 1, \"i\", 60)(5, SalesCallOverviewComponent_ng_template_16_i_5_Template, 1, 0, \"i\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, SalesCallOverviewComponent_ng_template_16_ng_container_6_Template, 6, 4, \"ng-container\", 62);\n    i0.ɵɵelementStart(7, \"th\", 63);\n    i0.ɵɵtext(8, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNotes === \"note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNotes !== \"note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedNotesColumns);\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_17_ng_container_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notes_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, notes_r10 == null ? null : notes_r10.updatedAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_17_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notes_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (notes_r10 == null ? null : notes_r10.updatedBy) || \"-\", \" \");\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_17_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 71);\n    i0.ɵɵtemplate(3, SalesCallOverviewComponent_ng_template_17_ng_container_2_ng_container_3_Template, 3, 4, \"ng-container\", 72)(4, SalesCallOverviewComponent_ng_template_17_ng_container_2_ng_container_4_Template, 2, 1, \"ng-container\", 72);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r11.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedAt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedBy\");\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 67);\n    i0.ɵɵelement(1, \"td\", 68);\n    i0.ɵɵtemplate(2, SalesCallOverviewComponent_ng_template_17_ng_container_2_Template, 5, 3, \"ng-container\", 62);\n    i0.ɵɵelementStart(3, \"td\", 63)(4, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_ng_template_17_Template_button_click_4_listener() {\n      const notes_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.editNote(notes_r10));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_ng_template_17_Template_button_click_5_listener($event) {\n      const notes_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r0.confirmRemove(notes_r10));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notes_r10 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", (notes_r10 == null ? null : notes_r10.note) || \"-\", i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedNotesColumns);\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 73);\n    i0.ɵɵtext(2, \"No notes found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 73);\n    i0.ɵɵtext(2, \"Loading notes data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallOverviewComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Note\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Note is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallOverviewComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtemplate(1, SalesCallOverviewComponent_div_26_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.fNote[\"note\"].errors[\"required\"]);\n  }\n}\nexport let SalesCallOverviewComponent = /*#__PURE__*/(() => {\n  class SalesCallOverviewComponent {\n    constructor(formBuilder, activitiesservice, messageservice, confirmationservice) {\n      this.formBuilder = formBuilder;\n      this.activitiesservice = activitiesservice;\n      this.messageservice = messageservice;\n      this.confirmationservice = confirmationservice;\n      this.ngUnsubscribe = new Subject();\n      this.overviewDetails = null;\n      this.accountLoading = false;\n      this.accountInput$ = new Subject();\n      this.contactLoading = false;\n      this.contactInput$ = new Subject();\n      this.employeeLoading = false;\n      this.employeeInput$ = new Subject();\n      this.defaultOptions = [];\n      this.submitted = false;\n      this.saving = false;\n      this.id = '';\n      this.editid = '';\n      this.isEditMode = false;\n      this.notedetails = null;\n      this.notevisible = false;\n      this.noteposition = 'right';\n      this.notesubmitted = false;\n      this.notesaving = false;\n      this.noteeditid = '';\n      this.dropdowns = {\n        activityDocumentType: [],\n        activityStatus: [],\n        activityCategory: [],\n        activitydisposition: [],\n        activityInitiatorCode: []\n      };\n      this.SalesCallOverviewForm = this.formBuilder.group({\n        main_account_party_id: ['', [Validators.required]],\n        main_contact_party_id: ['', [Validators.required]],\n        subject: ['', [Validators.required]],\n        phone_call_category: ['', [Validators.required]],\n        disposition_code: [''],\n        reason: [''],\n        start_date: [''],\n        end_date: [''],\n        brand: [''],\n        ranking: [''],\n        customer_group: [''],\n        initiator_code: ['', [Validators.required]],\n        activity_status: ['', [Validators.required]]\n      });\n      this.NoteForm = this.formBuilder.group({\n        note: ['', [Validators.required]]\n      });\n      this._selectedNotesColumns = [];\n      this.NotesCols = [{\n        field: 'updatedAt',\n        header: 'Last Updated On'\n      }, {\n        field: 'updatedBy',\n        header: 'Updated By'\n      }];\n      this.sortFieldNotes = '';\n      this.sortOrderNotes = 1;\n      this.compareById = (a, b) => a === b;\n    }\n    ngOnInit() {\n      // sales call successfully added message.\n      setTimeout(() => {\n        const successMessage = sessionStorage.getItem('salescallMessage');\n        if (successMessage) {\n          this.messageservice.add({\n            severity: 'success',\n            detail: successMessage\n          });\n          sessionStorage.removeItem('salescallMessage');\n        }\n      }, 100);\n      this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n      this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n      this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_PHONE_CALL_CATEGORY');\n      this.loadActivityDropDown('activitydisposition', 'CRM_ACTIVITY_DISPOSITION_CODE');\n      this.loadActivityDropDown('activityInitiatorCode', 'CRM_ACTIVITY_INITIATOR_CODE');\n      this.SalesCallOverviewForm.get('main_account_party_id')?.valueChanges.pipe(takeUntil(this.ngUnsubscribe), tap(selectedBpId => {\n        if (selectedBpId) {\n          this.loadAccountByContacts(selectedBpId);\n        } else {\n          this.contacts$ = of(this.defaultOptions);\n        }\n      }), catchError(err => {\n        console.error('Account selection error:', err);\n        this.contacts$ = of(this.defaultOptions);\n        return of();\n      })).subscribe();\n      this.loadAccounts();\n      this.activitiesservice.activity.pipe(takeUntil(this.ngUnsubscribe)).subscribe(response => {\n        if (!response) return;\n        this.id = response?.activity_id;\n        this.overviewDetails = response;\n        this.notedetails = response?.notes;\n        if (this.overviewDetails) {\n          this.fetchOverviewData(this.overviewDetails);\n        }\n      });\n      this._selectedNotesColumns = this.NotesCols;\n    }\n    get selectedNotesColumns() {\n      return this._selectedNotesColumns;\n    }\n    set selectedNotesColumns(val) {\n      this._selectedNotesColumns = this.NotesCols.filter(col => val.includes(col));\n    }\n    onNotesColumnReorder(event) {\n      const draggedCol = this.NotesCols[event.dragIndex];\n      this.NotesCols.splice(event.dragIndex, 1);\n      this.NotesCols.splice(event.dropIndex, 0, draggedCol);\n    }\n    customSort(field, data, type) {\n      if (type === 'Notes') {\n        this.sortFieldNotes = field;\n        this.sortOrderNotes = this.sortOrderNotes === 1 ? -1 : 1;\n      }\n      data.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrderNotes * result;\n      });\n    }\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        let fields = field.split('.');\n        let value = data;\n        for (let i = 0; i < fields.length; i++) {\n          if (value == null) return null;\n          value = value[fields[i]];\n        }\n        return value;\n      }\n    }\n    loadActivityDropDown(target, type) {\n      this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n        this.dropdowns[target] = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n      });\n    }\n    getLabelFromDropdown(dropdownKey, value) {\n      const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n      return item?.label || value;\n    }\n    editNote(note) {\n      this.notevisible = true;\n      this.noteeditid = note?.documentId;\n      this.NoteForm.patchValue(note);\n    }\n    fetchOverviewData(activity) {\n      this.existingActivity = {\n        main_account_party_id: activity?.main_account_party_id,\n        main_contact_party_id: activity?.main_contact_party_id,\n        subject: activity?.subject,\n        phone_call_category: activity?.phone_call_category,\n        disposition_code: activity?.disposition_code,\n        reason: activity?.reason,\n        start_date: activity?.start_date ? new Date(activity?.start_date) : null,\n        end_date: activity?.end_date ? new Date(activity?.end_date) : null,\n        owner_party_id: activity?.owner_party_id,\n        brand: activity?.brand,\n        customer_group: activity?.customer_group,\n        ranking: activity?.ranking,\n        initiator_code: activity?.initiator_code,\n        activity_status: activity?.activity_status\n      };\n      this.editid = activity.documentId;\n      this.SalesCallOverviewForm.patchValue(this.existingActivity);\n    }\n    loadAccounts() {\n      this.accounts$ = concat(of(this.defaultOptions),\n      // Default empty options\n      this.accountInput$.pipe(distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n        const params = {\n          [`filters[roles][bp_role][$eq][0]`]: 'FLCU01',\n          [`filters[roles][bp_role][$eq][1]`]: 'FLCU00',\n          [`fields[0]`]: 'bp_id',\n          [`fields[1]`]: 'first_name',\n          [`fields[2]`]: 'last_name',\n          [`fields[3]`]: 'bp_full_name'\n        };\n        if (term) {\n          params[`filters[$or][0][bp_id][$containsi]`] = term;\n          params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n        }\n        return this.activitiesservice.getPartners(params).pipe(map(data => {\n          return data || []; // Make sure to return correct data structure\n        }), tap(() => this.accountLoading = false), catchError(error => {\n          this.accountLoading = false;\n          return of([]);\n        }));\n      })));\n    }\n    loadAccountByContacts(bpId) {\n      this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n        const params = {\n          'filters[bp_company_id][$eq]': bpId,\n          'populate[business_partner_person][populate][addresses][populate]': '*'\n        };\n        if (term) {\n          params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n          params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n        }\n        return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n          this.contactLoading = false;\n        }), catchError(error => {\n          console.error('Contact loading failed:', error);\n          this.contactLoading = false;\n          return of([]);\n        }));\n      }));\n    }\n    toggleSelection(id) {\n      const control = this.SalesCallOverviewForm.get('main_contact_party_id');\n      let currentValue = control?.value || [];\n      if (currentValue.includes(id)) {\n        currentValue = currentValue.filter(v => v !== id);\n      } else {\n        currentValue = [...currentValue, id];\n      }\n      control?.setValue(currentValue);\n    }\n    isSelected(id) {\n      return this.SalesCallOverviewForm.get('main_contact_party_id')?.value?.includes(id);\n    }\n    onNoteSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.notesubmitted = true;\n        _this.notevisible = true;\n        if (_this.NoteForm.invalid) {\n          _this.notevisible = true;\n          return;\n        }\n        _this.notesaving = true;\n        const value = {\n          ..._this.NoteForm.value\n        };\n        const data = {\n          activity_id: _this.id,\n          note: value?.note\n        };\n        if (_this.noteeditid) {\n          _this.activitiesservice.updateNote(_this.noteeditid, data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n            complete: () => {\n              _this.notesaving = false;\n              _this.notevisible = false;\n              _this.NoteForm.reset();\n              _this.messageservice.add({\n                severity: 'success',\n                detail: 'Note Updated Successfully!.'\n              });\n              _this.activitiesservice.getActivityByID(_this.id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n            },\n            error: res => {\n              _this.notesaving = false;\n              _this.notevisible = true;\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Error while processing your request.'\n              });\n            }\n          });\n        } else {\n          _this.activitiesservice.createNote(data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n            complete: () => {\n              _this.notesaving = false;\n              _this.notevisible = false;\n              _this.NoteForm.reset();\n              _this.messageservice.add({\n                severity: 'success',\n                detail: 'Note Created Successfully!.'\n              });\n              _this.activitiesservice.getActivityByID(_this.id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n            },\n            error: res => {\n              _this.notesaving = false;\n              _this.notevisible = true;\n              _this.messageservice.add({\n                severity: 'error',\n                detail: 'Error while processing your request.'\n              });\n            }\n          });\n        }\n      })();\n    }\n    onSubmit() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        _this2.submitted = true;\n        if (_this2.SalesCallOverviewForm.invalid) {\n          return;\n        }\n        _this2.saving = true;\n        const value = {\n          ..._this2.SalesCallOverviewForm.value\n        };\n        const data = {\n          subject: value?.subject,\n          main_account_party_id: value?.main_account_party_id,\n          main_contact_party_id: value?.main_contact_party_id,\n          phone_call_category: value?.phone_call_category,\n          start_date: value?.start_date ? _this2.formatDate(value.start_date) : null,\n          end_date: value?.end_date ? _this2.formatDate(value.end_date) : null,\n          disposition_code: value?.disposition_code,\n          initiator_code: value?.initiator_code,\n          //owner_party_id: value?.owner_party_id,\n          activity_status: value?.activity_status,\n          reason: value?.reason,\n          brand: value?.brand,\n          customer_group: value?.customer_group,\n          ranking: value?.ranking\n        };\n        _this2.activitiesservice.updateActivity(_this2.editid, data).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe({\n          next: response => {\n            _this2.messageservice.add({\n              severity: 'success',\n              detail: 'Sales Call Updated successFully!'\n            });\n            _this2.activitiesservice.getActivityByID(_this2.id).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe();\n            _this2.isEditMode = false;\n          },\n          error: res => {\n            _this2.saving = false;\n            _this2.isEditMode = true;\n            _this2.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      })();\n    }\n    confirmRemove(item) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to delete the selected records?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: () => {\n          this.remove(item);\n        }\n      });\n    }\n    remove(item) {\n      this.activitiesservice.deleteNote(item.documentId).pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n        next: () => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Record Deleted Successfully!'\n          });\n          this.activitiesservice.getActivityByID(this.id).pipe(takeUntil(this.ngUnsubscribe)).subscribe();\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    stripHtml(html) {\n      const temp = document.createElement('div');\n      temp.innerHTML = html;\n      return temp.textContent || temp.innerText || '';\n    }\n    formatDate(date) {\n      if (!date) return '';\n      const yyyy = date.getFullYear();\n      const mm = String(date.getMonth() + 1).padStart(2, '0');\n      const dd = String(date.getDate()).padStart(2, '0');\n      return `${yyyy}-${mm}-${dd}`;\n    }\n    get f() {\n      return this.SalesCallOverviewForm.controls;\n    }\n    get fNote() {\n      return this.NoteForm.controls;\n    }\n    showDialog(position) {\n      this.noteposition = position;\n      this.notevisible = true;\n      this.notesubmitted = false;\n      this.NoteForm.reset();\n    }\n    toggleEdit() {\n      this.isEditMode = !this.isEditMode;\n    }\n    onReset() {\n      this.submitted = false;\n      this.SalesCallOverviewForm.reset();\n    }\n    ngOnDestroy() {\n      this.ngUnsubscribe.next();\n      this.ngUnsubscribe.complete();\n    }\n    static {\n      this.ɵfac = function SalesCallOverviewComponent_Factory(t) {\n        return new (t || SalesCallOverviewComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivitiesService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesCallOverviewComponent,\n        selectors: [[\"app-sales-call-overview\"]],\n        decls: 30,\n        vars: 31,\n        consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"label\", \"icon\", \"styleClass\", \"rounded\"], [\"class\", \"p-fluid p-formgrid grid m-0\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\", \"mt-5\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"bp_id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"note-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter text here...\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"m-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"mb-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-primary\"], [1, \"readonly-field\", \"font-medium\", \"text-700\", \"p-2\"], [1, \"readonly-field\", \"font-medium\", \"p-2\", \"text-orange-600\", \"cursor-pointer\"], [\"target\", \"_blank\", 2, \"text-decoration\", \"none\", \"color\", \"inherit\", 3, \"href\"], [3, \"formGroup\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_id\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_account_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"compareWith\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"multiple\", \"closeOnSelect\", \"ngClass\"], [\"pInputText\", \"\", \"id\", \"subject\", \"type\", \"text\", \"formControlName\", \"subject\", \"placeholder\", \"Subject\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"formControlName\", \"phone_call_category\", \"placeholder\", \"Select Category\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"options\", \"styleClass\", \"ngClass\"], [\"formControlName\", \"disposition_code\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"placeholder\", \"Select Disposition Code\", 3, \"options\", \"styleClass\"], [\"pInputText\", \"\", \"id\", \"reason\", \"type\", \"text\", \"formControlName\", \"reason\", \"placeholder\", \"Reason\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"start_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Call Date/Time\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"formControlName\", \"end_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"End Date/Time\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"pInputText\", \"\", \"id\", \"brand\", \"type\", \"text\", \"formControlName\", \"brand\", \"placeholder\", \"Brand'\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"customer_group\", \"type\", \"text\", \"formControlName\", \"customer_group\", \"placeholder\", \"Customer Group'\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"ranking\", \"type\", \"text\", \"formControlName\", \"ranking\", \"placeholder\", \"Ranking'\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"activity_status\", \"placeholder\", \"Select a Status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"formControlName\", \"initiator_code\", \"placeholder\", \"Select a Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [4, \"ngIf\"], [1, \"p-error\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"checkbox\", 2, \"width\", \"20px\", \"height\", \"20px\", \"margin-right\", \"6px\", 3, \"change\", \"checked\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"note-text\", 3, \"innerHTML\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"8\", 1, \"border-round-left-lg\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"]],\n        template: function SalesCallOverviewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Overview\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p-button\", 3);\n            i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_Template_p_button_click_4_listener() {\n              return ctx.toggleEdit();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(5, SalesCallOverviewComponent_div_5_Template, 141, 27, \"div\", 4)(6, SalesCallOverviewComponent_form_6_Template, 117, 54, \"form\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"h4\", 2);\n            i0.ɵɵtext(10, \"Notes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"div\", 8)(12, \"p-button\", 9);\n            i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_Template_p_button_click_12_listener() {\n              return ctx.showDialog(\"right\");\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"p-multiSelect\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesCallOverviewComponent_Template_p_multiSelect_ngModelChange_13_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedNotesColumns, $event) || (ctx.selectedNotesColumns = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(14, \"div\", 11)(15, \"p-table\", 12);\n            i0.ɵɵlistener(\"onColReorder\", function SalesCallOverviewComponent_Template_p_table_onColReorder_15_listener($event) {\n              return ctx.onNotesColumnReorder($event);\n            });\n            i0.ɵɵtemplate(16, SalesCallOverviewComponent_ng_template_16_Template, 9, 3, \"ng-template\", 13)(17, SalesCallOverviewComponent_ng_template_17_Template, 6, 2, \"ng-template\", 14)(18, SalesCallOverviewComponent_ng_template_18_Template, 3, 0, \"ng-template\", 15)(19, SalesCallOverviewComponent_ng_template_19_Template, 3, 0, \"ng-template\", 16);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(20, \"p-dialog\", 17);\n            i0.ɵɵtwoWayListener(\"visibleChange\", function SalesCallOverviewComponent_Template_p_dialog_visibleChange_20_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.notevisible, $event) || (ctx.notevisible = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(21, SalesCallOverviewComponent_ng_template_21_Template, 2, 0, \"ng-template\", 13);\n            i0.ɵɵelementStart(22, \"form\", 18)(23, \"div\", 19)(24, \"div\", 20);\n            i0.ɵɵelement(25, \"p-editor\", 21);\n            i0.ɵɵtemplate(26, SalesCallOverviewComponent_div_26_Template, 2, 1, \"div\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(27, \"div\", 23)(28, \"button\", 24);\n            i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_Template_button_click_28_listener() {\n              return ctx.notevisible = false;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"button\", 25);\n            i0.ɵɵlistener(\"click\", function SalesCallOverviewComponent_Template_button_click_29_listener() {\n              return ctx.onNoteSubmit();\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"label\", ctx.isEditMode ? \"Close\" : \"Edit\")(\"icon\", !ctx.isEditMode ? \"pi pi-pencil\" : \"\")(\"styleClass\", \"w-5rem font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.NotesCols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedNotesColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.notedetails)(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n            i0.ɵɵadvance(5);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(27, _c0));\n            i0.ɵɵproperty(\"modal\", true);\n            i0.ɵɵtwoWayProperty(\"visible\", ctx.notevisible);\n            i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.NoteForm);\n            i0.ɵɵadvance(3);\n            i0.ɵɵstyleMap(i0.ɵɵpureFunction0(28, _c1));\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(29, _c2, ctx.notesubmitted && ctx.fNote[\"note\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.notesubmitted && ctx.fNote[\"note\"].errors);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i5.NgSelectComponent, i5.NgOptionTemplateDirective, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.FormGroupDirective, i1.FormControlName, i6.Table, i3.PrimeTemplate, i6.SortableColumn, i6.FrozenColumn, i6.ReorderableColumn, i7.ButtonDirective, i7.Button, i8.Dropdown, i9.Tooltip, i10.Calendar, i11.InputText, i12.Dialog, i13.Editor, i14.MultiSelect, i4.AsyncPipe, i4.DatePipe],\n        styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%]{color:var(--red-500);right:10px}  .note-popup .p-dialog{margin-right:50px}  .note-popup .p-dialog .p-dialog-header{background:var(--surface-0);border-bottom:1px solid var(--surface-100)}  .note-popup .p-dialog .p-dialog-header h4{margin:0}  .note-popup .p-dialog .p-dialog-content{background:var(--surface-0);padding:1.714rem}\"]\n      });\n    }\n  }\n  return SalesCallOverviewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}