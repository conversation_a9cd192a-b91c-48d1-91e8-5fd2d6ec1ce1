{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { State } from 'country-state-city';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../activities.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/breadcrumb\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/multiselect\";\nconst _c0 = [\"dt1\"];\nfunction SalesCallComponent_ng_template_19_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallComponent_ng_template_19_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n}\nfunction SalesCallComponent_ng_template_19_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallComponent_ng_template_19_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n}\nfunction SalesCallComponent_ng_template_19_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 29);\n    i0.ɵɵlistener(\"click\", function SalesCallComponent_ng_template_19_ng_container_8_Template_th_click_1_listener() {\n      const col_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.customSort(col_r6.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, SalesCallComponent_ng_template_19_ng_container_8_i_4_Template, 1, 1, \"i\", 24)(5, SalesCallComponent_ng_template_19_ng_container_8_i_5_Template, 1, 0, \"i\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r6.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r6.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField === col_r6.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField !== col_r6.field);\n  }\n}\nfunction SalesCallComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 21);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 22);\n    i0.ɵɵlistener(\"click\", function SalesCallComponent_ng_template_19_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.customSort(\"main_account_party_id\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 23);\n    i0.ɵɵtext(5, \" Account ID \");\n    i0.ɵɵtemplate(6, SalesCallComponent_ng_template_19_i_6_Template, 1, 1, \"i\", 24)(7, SalesCallComponent_ng_template_19_i_7_Template, 1, 0, \"i\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, SalesCallComponent_ng_template_19_ng_container_8_Template, 6, 4, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField === \"main_account_party_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField !== \"main_account_party_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedColumns);\n  }\n}\nfunction SalesCallComponent_ng_template_20_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const call_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/activities/calls/\" + call_r7.activity_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (call_r7 == null ? null : call_r7.subject) || \"-\", \" \");\n  }\n}\nfunction SalesCallComponent_ng_template_20_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const call_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (call_r7 == null ? null : call_r7.business_partner == null ? null : call_r7.business_partner.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction SalesCallComponent_ng_template_20_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const call_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (call_r7 == null ? null : call_r7.ranking) || \"-\", \" \");\n  }\n}\nfunction SalesCallComponent_ng_template_20_ng_container_5_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const call_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getStateNameByCode(call_r7 == null ? null : call_r7.business_partner == null ? null : call_r7.business_partner.addresses == null ? null : call_r7.business_partner.addresses[0] == null ? null : call_r7.business_partner.addresses[0].region, call_r7 == null ? null : call_r7.business_partner == null ? null : call_r7.business_partner.addresses == null ? null : call_r7.business_partner.addresses[0] == null ? null : call_r7.business_partner.addresses[0].country) || \"-\", \" \");\n  }\n}\nfunction SalesCallComponent_ng_template_20_ng_container_5_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const call_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"pTooltip\", ctx_r3.stripHtml(call_r7 == null ? null : call_r7.globalNote == null ? null : call_r7.globalNote.note));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (call_r7 == null ? null : call_r7.globalNote == null ? null : call_r7.globalNote.note) ? i0.ɵɵpipeBind3(3, 2, ctx_r3.stripHtml(call_r7.globalNote.note), 0, 80) + (call_r7.globalNote.note.length > 80 ? \"...\" : \"\") : \"-\", \" \");\n  }\n}\nfunction SalesCallComponent_ng_template_20_ng_container_5_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const call_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (call_r7 == null ? null : call_r7.customer_group) || \"-\", \" \");\n  }\n}\nfunction SalesCallComponent_ng_template_20_ng_container_5_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const call_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (call_r7 == null ? null : call_r7.brand) || \"-\", \" \");\n  }\n}\nfunction SalesCallComponent_ng_template_20_ng_container_5_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const call_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLabelFromDropdown(\"activityCategory\", call_r7 == null ? null : call_r7.phone_call_category) || \"-\", \" \");\n  }\n}\nfunction SalesCallComponent_ng_template_20_ng_container_5_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const call_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLabelFromDropdown(\"activityStatus\", call_r7 == null ? null : call_r7.activity_status) || \"-\", \" \");\n  }\n}\nfunction SalesCallComponent_ng_template_20_ng_container_5_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const call_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (call_r7 == null ? null : call_r7.createdAt) ? i0.ɵɵpipeBind2(2, 1, call_r7 == null ? null : call_r7.createdAt, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction SalesCallComponent_ng_template_20_ng_container_5_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const call_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (call_r7 == null ? null : call_r7.business_partner_owner == null ? null : call_r7.business_partner_owner.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction SalesCallComponent_ng_template_20_ng_container_5_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const call_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (call_r7 == null ? null : call_r7.customer_timezone) || \"-\", \" \");\n  }\n}\nfunction SalesCallComponent_ng_template_20_ng_container_5_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r8 = i0.ɵɵnextContext().$implicit;\n    const call_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", call_r7[col_r8.field] || \"-\", \" \");\n  }\n}\nfunction SalesCallComponent_ng_template_20_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 34);\n    i0.ɵɵtemplate(3, SalesCallComponent_ng_template_20_ng_container_5_ng_container_3_Template, 3, 2, \"ng-container\", 35)(4, SalesCallComponent_ng_template_20_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 35)(5, SalesCallComponent_ng_template_20_ng_container_5_ng_container_5_Template, 2, 1, \"ng-container\", 35)(6, SalesCallComponent_ng_template_20_ng_container_5_ng_container_6_Template, 2, 1, \"ng-container\", 35)(7, SalesCallComponent_ng_template_20_ng_container_5_ng_container_7_Template, 4, 6, \"ng-container\", 35)(8, SalesCallComponent_ng_template_20_ng_container_5_ng_container_8_Template, 2, 1, \"ng-container\", 35)(9, SalesCallComponent_ng_template_20_ng_container_5_ng_container_9_Template, 2, 1, \"ng-container\", 35)(10, SalesCallComponent_ng_template_20_ng_container_5_ng_container_10_Template, 2, 1, \"ng-container\", 35)(11, SalesCallComponent_ng_template_20_ng_container_5_ng_container_11_Template, 2, 1, \"ng-container\", 35)(12, SalesCallComponent_ng_template_20_ng_container_5_ng_container_12_Template, 3, 4, \"ng-container\", 35)(13, SalesCallComponent_ng_template_20_ng_container_5_ng_container_13_Template, 2, 1, \"ng-container\", 35)(14, SalesCallComponent_ng_template_20_ng_container_5_ng_container_14_Template, 2, 1, \"ng-container\", 35)(15, SalesCallComponent_ng_template_20_ng_container_5_ng_container_15_Template, 2, 1, \"ng-container\", 36);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r8.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"subject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"ranking\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"state\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"globalNote.note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"customer_group\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"brand\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"phone_call_category\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"activity_status\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"createdAt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_owner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"customer_timezone\");\n  }\n}\nfunction SalesCallComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 30)(1, \"td\", 31);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SalesCallComponent_ng_template_20_ng_container_5_Template, 16, 13, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const call_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", call_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/activities/calls/\" + call_r7.activity_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (call_r7 == null ? null : call_r7.main_account_party_id) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedColumns);\n  }\n}\nfunction SalesCallComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"No calls found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \"Loading calls data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let SalesCallComponent = /*#__PURE__*/(() => {\n  class SalesCallComponent {\n    constructor(activitiesservice, router) {\n      this.activitiesservice = activitiesservice;\n      this.router = router;\n      this.unsubscribe$ = new Subject();\n      this.calls = [];\n      this.totalRecords = 0;\n      this.loading = true;\n      this.globalSearchTerm = '';\n      this.dropdowns = {\n        activityCategory: [],\n        activityStatus: []\n      };\n      this._selectedColumns = [];\n      this.cols = [{\n        field: 'subject',\n        header: 'Subject'\n      }, {\n        field: 'business_partner.bp_full_name',\n        header: 'Account'\n      }, {\n        field: 'ranking',\n        header: 'Ranking'\n      }, {\n        field: 'state',\n        header: 'State'\n      }, {\n        field: 'globalNote.note',\n        header: 'Notes'\n      }, {\n        field: 'customer_group',\n        header: 'Customer Group'\n      }, {\n        field: 'brand',\n        header: 'Brand'\n      }, {\n        field: 'phone_call_category',\n        header: 'Category'\n      }, {\n        field: 'activity_status',\n        header: 'Status'\n      }, {\n        field: 'createdAt',\n        header: 'Created On'\n      }, {\n        field: 'business_partner_owner.bp_full_name',\n        header: 'Owner'\n      }, {\n        field: 'customer_timezone',\n        header: 'Customer Timezone'\n      }];\n      this.sortField = '';\n      this.sortOrder = 1;\n    }\n    customSort(field) {\n      if (this.sortField === field) {\n        this.sortOrder = -this.sortOrder;\n      } else {\n        this.sortField = field;\n        this.sortOrder = 1;\n      }\n      this.calls.sort((a, b) => {\n        const value1 = this.resolveFieldData(a, field);\n        const value2 = this.resolveFieldData(b, field);\n        let result = 0;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n    // Utility to resolve nested fields\n    resolveFieldData(data, field) {\n      if (!data || !field) return null;\n      if (field.indexOf('.') === -1) {\n        return data[field];\n      } else {\n        return field.split('.').reduce((obj, key) => obj?.[key], data);\n      }\n    }\n    ngOnInit() {\n      this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_PHONE_CALL_CATEGORY');\n      this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n      this.breadcrumbitems = [{\n        label: 'Sales Call',\n        routerLink: ['/store/activities']\n      }];\n      this.home = {\n        icon: 'pi pi-home text-lg',\n        routerLink: ['/']\n      };\n      this.Actions = [{\n        name: 'All',\n        code: 'ALL'\n      }, {\n        name: 'My Sales Call',\n        code: 'MSC'\n      }, {\n        name: 'My Sales Call This Month',\n        code: 'MSCTM'\n      }, {\n        name: 'My Sales Call This Week',\n        code: 'MSCTW'\n      }, {\n        name: 'My Sales Call Today',\n        code: 'MSCT'\n      }, {\n        name: 'My Completed Sales Call',\n        code: 'MCSC'\n      }];\n      this._selectedColumns = this.cols;\n    }\n    get selectedColumns() {\n      return this._selectedColumns;\n    }\n    set selectedColumns(val) {\n      // optional: preserve user reorder logic instead\n      this._selectedColumns = this.cols.filter(col => val.includes(col));\n    }\n    onColumnReorder(event) {\n      const draggedCol = this._selectedColumns[event.dragIndex];\n      this._selectedColumns.splice(event.dragIndex, 1);\n      this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n    }\n    loadActivityDropDown(target, type) {\n      this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n        this.dropdowns[target] = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n      });\n    }\n    getLabelFromDropdown(dropdownKey, value) {\n      const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n      return item?.label || value;\n    }\n    loadSalesCall(event) {\n      this.loading = true;\n      const page = event.first / event.rows + 1;\n      const pageSize = event.rows;\n      const sortField = event.sortField;\n      const sortOrder = event.sortOrder;\n      const filter = this.selectedActions?.code;\n      this.activitiesservice.getSalesCall(page, pageSize, sortField, sortOrder, this.globalSearchTerm, filter).subscribe({\n        next: response => {\n          this.calls = (response?.data || []).map(call => {\n            // 🔍 Find the global note from the call.notes array\n            const globalNote = call.notes?.find(n => n.is_global_note === true);\n            return {\n              ...call,\n              globalNote: globalNote || null // 📝 Attach the global note (if found)\n            };\n          });\n          this.totalRecords = response?.meta?.pagination.total;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error fetching calls', error);\n          this.loading = false;\n        }\n      });\n    }\n    onActionChange() {\n      // Re-trigger the lazy load with current dt1 state\n      const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\n        first: 0,\n        rows: 15\n      };\n      this.loadSalesCall(dt1State);\n    }\n    stripHtml(html) {\n      const temp = document.createElement('div');\n      temp.innerHTML = html;\n      return temp.textContent || temp.innerText || '';\n    }\n    getStateNameByCode(stateCode, countryCode) {\n      const states = State.getStatesOfCountry(countryCode);\n      const match = states.find(state => state.isoCode === stateCode);\n      return match ? match.name : '-';\n    }\n    signup() {\n      this.router.navigate(['/store/activities/calls/create']);\n    }\n    onGlobalFilter(table, event) {\n      this.loadSalesCall({\n        first: 0,\n        rows: 14\n      });\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function SalesCallComponent_Factory(t) {\n        return new (t || SalesCallComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SalesCallComponent,\n        selectors: [[\"app-sales-call\"]],\n        viewQuery: function SalesCallComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dt1 = _t.first);\n          }\n        },\n        decls: 23,\n        vars: 18,\n        consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"h-search-box\"], [1, \"p-input-icon-right\"], [\"type\", \"text\", \"placeholder\", \"Search Call\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"optionLabel\", \"name\", \"placeholder\", \"Filter\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [\"type\", \"button\", \"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onLazyLoad\", \"onColReorder\", \"value\", \"rows\", \"loading\", \"paginator\", \"totalRecords\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", 3, \"routerLink\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [4, \"ngSwitchDefault\"], [1, \"text-blue-600\", \"font-medium\", \"underline\", \"cursor-pointer\", 3, \"routerLink\"], [\"tooltipPosition\", \"top\", \"tooltipStyleClass\", \"multi-line-tooltip\", 3, \"pTooltip\"], [\"colspan\", \"14\", 1, \"border-round-left-lg\", \"pl-3\"]],\n        template: function SalesCallComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n            i0.ɵɵelement(3, \"p-breadcrumb\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"span\", 8)(7, \"input\", 9, 0);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesCallComponent_Template_input_ngModelChange_7_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"input\", function SalesCallComponent_Template_input_input_7_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              const dt1_r2 = i0.ɵɵreference(18);\n              return i0.ɵɵresetView(ctx.onGlobalFilter(dt1_r2, $event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(9, \"i\", 10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"p-dropdown\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesCallComponent_Template_p_dropdown_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"onChange\", function SalesCallComponent_Template_p_dropdown_onChange_10_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onActionChange());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function SalesCallComponent_Template_button_click_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.signup());\n            });\n            i0.ɵɵelementStart(12, \"span\", 13);\n            i0.ɵɵtext(13, \"box_edit\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(14, \" Create \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"p-multiSelect\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesCallComponent_Template_p_multiSelect_ngModelChange_15_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(16, \"div\", 15)(17, \"p-table\", 16, 1);\n            i0.ɵɵlistener(\"onLazyLoad\", function SalesCallComponent_Template_p_table_onLazyLoad_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.loadSalesCall($event));\n            })(\"onColReorder\", function SalesCallComponent_Template_p_table_onColReorder_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onColumnReorder($event));\n            });\n            i0.ɵɵtemplate(19, SalesCallComponent_ng_template_19_Template, 9, 3, \"ng-template\", 17)(20, SalesCallComponent_ng_template_20_Template, 6, 4, \"ng-template\", 18)(21, SalesCallComponent_ng_template_21_Template, 3, 0, \"ng-template\", 19)(22, SalesCallComponent_ng_template_22_Template, 3, 0, \"ng-template\", 20);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"options\", ctx.Actions);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n            i0.ɵɵproperty(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"options\", ctx.cols);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n            i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.calls)(\"rows\", 14)(\"loading\", ctx.loading)(\"paginator\", true)(\"totalRecords\", ctx.totalRecords)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          }\n        },\n        dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgSwitch, i3.NgSwitchCase, i3.NgSwitchDefault, i2.RouterLink, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.Table, i6.PrimeTemplate, i5.SortableColumn, i5.FrozenColumn, i5.ReorderableColumn, i5.TableCheckbox, i5.TableHeaderCheckbox, i7.Dropdown, i8.Breadcrumb, i9.Tooltip, i10.MultiSelect, i3.SlicePipe, i3.DatePipe],\n        styles: [\".custom-sort-icon{font-size:.85rem;color:#888;transition:transform .2s ease}  th:hover .custom-sort-icon{color:#000}  .multi-line-tooltip{white-space:normal!important;max-width:300px;word-wrap:break-word}\"]\n      });\n    }\n  }\n  return SalesCallComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}