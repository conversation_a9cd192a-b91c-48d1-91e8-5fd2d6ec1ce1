{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SalesOrdersRoutingModule } from './sales-orders-routing.module';\nimport { FormsModule } from '@angular/forms';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { TableModule } from 'primeng/table';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TabViewModule } from 'primeng/tabview';\nimport { PaginatorModule } from 'primeng/paginator';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { RouterModule } from '@angular/router';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport let SalesOrdersModule = /*#__PURE__*/(() => {\n  class SalesOrdersModule {\n    static {\n      this.ɵfac = function SalesOrdersModule_Factory(t) {\n        return new (t || SalesOrdersModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: SalesOrdersModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, SalesOrdersRoutingModule, FormsModule, TableModule, ButtonModule, DropdownModule, TabViewModule, AutoCompleteModule, BreadcrumbModule, CalendarModule, InputTextModule, PaginatorModule, ReactiveFormsModule, ProgressSpinnerModule, RouterModule, MultiSelectModule]\n      });\n    }\n  }\n  return SalesOrdersModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}