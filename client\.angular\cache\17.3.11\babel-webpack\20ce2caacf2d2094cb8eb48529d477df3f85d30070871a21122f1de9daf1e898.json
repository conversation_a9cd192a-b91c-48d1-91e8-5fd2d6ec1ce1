{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ViewEncapsulation, Input, Output, ViewChild, signal, effect, PLATFORM_ID, ChangeDetectionStrategy, Inject, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport { BarsIcon } from 'primeng/icons/bars';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport { Subject, interval } from 'rxjs';\nimport { debounce, filter } from 'rxjs/operators';\nconst _c0 = [\"menubar\"];\nconst _c1 = (a0, a1) => ({\n  \"p-submenu-list\": a0,\n  \"p-menubar-root-list\": a1\n});\nconst _c2 = a0 => ({\n  \"p-menuitem-link\": true,\n  \"p-disabled\": a0\n});\nconst _c3 = () => ({\n  exact: false\n});\nconst _c4 = a0 => ({\n  $implicit: a0\n});\nfunction MenubarSub_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 8);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(ctx_r2.getItemProp(processedItem_r2, \"style\"));\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getSeparatorItemClass(processedItem_r2));\n    i0.ɵɵattribute(\"id\", ctx_r2.getItemId(processedItem_r2))(\"data-pc-section\", \"separator\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"icon\"))(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"aria-hidden\", true)(\"tabindex\", -1);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getItemLabel(processedItem_r2), \" \");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemLabel(processedItem_r2), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleDownIcon_1_Template, 1, 3, \"AngleDownIcon\", 24)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleRightIcon_2_Template, 1, 3, \"AngleRightIcon\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.root);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.root);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 26);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Template, 3, 2, \"ng-container\", 11)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template, 1, 2, null, 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(5);\n    const menubar_r5 = i0.ɵɵreference(1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !menubar_r5.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", menubar_r5.submenuIconTemplate);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 15);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template, 1, 5, \"span\", 16)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template, 2, 2, \"span\", 17)(3, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template, 1, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(5, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_5_Template, 2, 2, \"span\", 18)(6, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template, 3, 2, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r6 = i0.ɵɵreference(4);\n    const processedItem_r2 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", ctx_r2.getItemProp(processedItem_r2, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(12, _c2, ctx_r2.getItemProp(processedItem_r2, \"disabled\")));\n    i0.ɵɵattribute(\"href\", ctx_r2.getItemProp(processedItem_r2, \"url\"), i0.ɵɵsanitizeUrl)(\"aria-hidden\", true)(\"data-automationid\", ctx_r2.getItemProp(processedItem_r2, \"automationId\"))(\"data-pc-section\", \"action\")(\"tabindex\", -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"escape\"))(\"ngIfElse\", htmlLabel_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"icon\"))(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"aria-hidden\", true)(\"tabindex\", -1);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemLabel(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemLabel(processedItem_r2), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleDownIcon_1_Template, 1, 3, \"AngleDownIcon\", 24)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleRightIcon_2_Template, 1, 3, \"AngleRightIcon\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.root);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.root);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 26);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_Template, 3, 2, \"ng-container\", 11)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template, 1, 2, null, 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(5);\n    const menubar_r5 = i0.ɵɵreference(1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !menubar_r5.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", menubar_r5.submenuIconTemplate);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 27);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template, 1, 5, \"span\", 16)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template, 2, 1, \"span\", 17)(3, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template, 1, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(5, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_5_Template, 2, 2, \"span\", 18)(6, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template, 3, 2, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlRouteLabel_r7 = i0.ɵɵreference(4);\n    const processedItem_r2 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", ctx_r2.getItemProp(processedItem_r2, \"routerLink\"))(\"queryParams\", ctx_r2.getItemProp(processedItem_r2, \"queryParams\"))(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", ctx_r2.getItemProp(processedItem_r2, \"routerLinkActiveOptions\") || i0.ɵɵpureFunction0(21, _c3))(\"target\", ctx_r2.getItemProp(processedItem_r2, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(22, _c2, ctx_r2.getItemProp(processedItem_r2, \"disabled\")))(\"fragment\", ctx_r2.getItemProp(processedItem_r2, \"fragment\"))(\"queryParamsHandling\", ctx_r2.getItemProp(processedItem_r2, \"queryParamsHandling\"))(\"preserveFragment\", ctx_r2.getItemProp(processedItem_r2, \"preserveFragment\"))(\"skipLocationChange\", ctx_r2.getItemProp(processedItem_r2, \"skipLocationChange\"))(\"replaceUrl\", ctx_r2.getItemProp(processedItem_r2, \"replaceUrl\"))(\"state\", ctx_r2.getItemProp(processedItem_r2, \"state\"));\n    i0.ɵɵattribute(\"data-automationid\", ctx_r2.getItemProp(processedItem_r2, \"automationId\"))(\"tabindex\", -1)(\"aria-hidden\", true)(\"data-pc-section\", \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"escape\"))(\"ngIfElse\", htmlRouteLabel_r7);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_Template, 7, 14, \"a\", 13)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_Template, 7, 24, \"a\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.getItemProp(processedItem_r2, \"routerLink\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"routerLink\"));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template(rf, ctx) {}\nfunction MenubarSub_ng_template_2_li_1_ng_container_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_4_1_Template, 1, 0, null, 28);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, processedItem_r2.item));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-menubarSub\", 29);\n    i0.ɵɵlistener(\"itemClick\", function MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template_p_menubarSub_itemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.itemClick.emit($event));\n    })(\"itemMouseEnter\", function MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template_p_menubarSub_itemMouseEnter_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onItemMouseEnter($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"itemTemplate\", ctx_r2.itemTemplate)(\"items\", processedItem_r2.items)(\"mobileActive\", ctx_r2.mobileActive)(\"autoDisplay\", ctx_r2.autoDisplay)(\"menuId\", ctx_r2.menuId)(\"activeItemPath\", ctx_r2.activeItemPath)(\"focusedItemId\", ctx_r2.focusedItemId)(\"level\", ctx_r2.level + 1);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 9, 1)(2, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function MenubarSub_ng_template_2_li_1_Template_div_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick($event, processedItem_r2));\n    })(\"mouseenter\", function MenubarSub_ng_template_2_li_1_Template_div_mouseenter_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemMouseEnter({\n        $event: $event,\n        processedItem: processedItem_r2\n      }));\n    });\n    i0.ɵɵtemplate(3, MenubarSub_ng_template_2_li_1_ng_container_3_Template, 3, 2, \"ng-container\", 11)(4, MenubarSub_ng_template_2_li_1_ng_container_4_Template, 2, 4, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template, 1, 8, \"p-menubarSub\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    const processedItem_r2 = ctx_r8.$implicit;\n    const index_r10 = ctx_r8.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.getItemProp(processedItem_r2, \"styleClass\"));\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"style\"))(\"ngClass\", ctx_r2.getItemClass(processedItem_r2))(\"tooltipOptions\", ctx_r2.getItemProp(processedItem_r2, \"tooltipOptions\"));\n    i0.ɵɵattribute(\"id\", ctx_r2.getItemId(processedItem_r2))(\"data-pc-section\", \"menuitem\")(\"data-p-highlight\", ctx_r2.isItemActive(processedItem_r2))(\"data-p-focused\", ctx_r2.isItemFocused(processedItem_r2))(\"data-p-disabled\", ctx_r2.isItemDisabled(processedItem_r2))(\"aria-label\", ctx_r2.getItemLabel(processedItem_r2))(\"aria-disabled\", ctx_r2.isItemDisabled(processedItem_r2) || undefined)(\"aria-haspopup\", ctx_r2.isItemGroup(processedItem_r2) && !ctx_r2.getItemProp(processedItem_r2, \"to\") ? \"menu\" : undefined)(\"aria-expanded\", ctx_r2.isItemGroup(processedItem_r2) ? ctx_r2.isItemActive(processedItem_r2) : undefined)(\"aria-level\", ctx_r2.level + 1)(\"aria-setsize\", ctx_r2.getAriaSetSize())(\"aria-posinset\", ctx_r2.getAriaPosInset(index_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_0_Template, 1, 5, \"li\", 6)(1, MenubarSub_ng_template_2_li_1_Template, 6, 21, \"li\", 7);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && ctx_r2.getItemProp(processedItem_r2, \"separator\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && !ctx_r2.getItemProp(processedItem_r2, \"separator\"));\n  }\n}\nconst _c5 = [\"menubutton\"];\nconst _c6 = [\"rootmenu\"];\nconst _c7 = [\"*\"];\nconst _c8 = a0 => ({\n  \"p-menubar p-component\": true,\n  \"p-menubar-mobile-active\": a0\n});\nfunction Menubar_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menubar_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, Menubar_div_1_ng_container_1_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.startTemplate);\n  }\n}\nfunction Menubar_a_2_BarsIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"BarsIcon\");\n  }\n}\nfunction Menubar_a_2_3_ng_template_0_Template(rf, ctx) {}\nfunction Menubar_a_2_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menubar_a_2_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Menubar_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 10, 2);\n    i0.ɵɵlistener(\"click\", function Menubar_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.menuButtonClick($event));\n    })(\"keydown\", function Menubar_a_2_Template_a_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.menuButtonKeydown($event));\n    });\n    i0.ɵɵtemplate(2, Menubar_a_2_BarsIcon_2_Template, 1, 0, \"BarsIcon\", 11)(3, Menubar_a_2_3_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-haspopup\", ctx_r1.model.length && ctx_r1.model.length > 0 ? true : false)(\"aria-expanded\", ctx_r1.mobileActive)(\"aria-controls\", ctx_r1.id)(\"aria-label\", ctx_r1.config.translation.aria.navigation)(\"data-pc-section\", \"button\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.menuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.menuIconTemplate);\n  }\n}\nfunction Menubar_div_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menubar_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtemplate(1, Menubar_div_5_ng_container_1_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.endTemplate);\n  }\n}\nfunction Menubar_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\nlet MenubarService = /*#__PURE__*/(() => {\n  class MenubarService {\n    autoHide;\n    autoHideDelay;\n    mouseLeaves = new Subject();\n    mouseLeft$ = this.mouseLeaves.pipe(debounce(() => interval(this.autoHideDelay)), filter(mouseLeft => this.autoHide && mouseLeft));\n    static ɵfac = function MenubarService_Factory(t) {\n      return new (t || MenubarService)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MenubarService,\n      factory: MenubarService.ɵfac\n    });\n  }\n  return MenubarService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MenubarSub = /*#__PURE__*/(() => {\n  class MenubarSub {\n    el;\n    renderer;\n    cd;\n    menubarService;\n    items;\n    itemTemplate;\n    root = false;\n    autoZIndex = true;\n    baseZIndex = 0;\n    mobileActive;\n    autoDisplay;\n    menuId;\n    ariaLabel;\n    ariaLabelledBy;\n    level = 0;\n    focusedItemId;\n    activeItemPath;\n    itemClick = new EventEmitter();\n    itemMouseEnter = new EventEmitter();\n    menuFocus = new EventEmitter();\n    menuBlur = new EventEmitter();\n    menuKeydown = new EventEmitter();\n    menubarViewChild;\n    mouseLeaveSubscriber;\n    constructor(el, renderer, cd, menubarService) {\n      this.el = el;\n      this.renderer = renderer;\n      this.cd = cd;\n      this.menubarService = menubarService;\n    }\n    ngOnInit() {\n      this.mouseLeaveSubscriber = this.menubarService.mouseLeft$.subscribe(() => {\n        this.cd.markForCheck();\n      });\n    }\n    onItemClick(event, processedItem) {\n      this.getItemProp(processedItem, 'command', {\n        originalEvent: event,\n        item: processedItem.item\n      });\n      this.itemClick.emit({\n        originalEvent: event,\n        processedItem,\n        isFocus: true\n      });\n    }\n    getItemProp(processedItem, name, params = null) {\n      return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n    }\n    getItemId(processedItem) {\n      return processedItem.item && processedItem.item?.id ? processedItem.item.id : `${this.menuId}_${processedItem.key}`;\n    }\n    getItemKey(processedItem) {\n      return this.getItemId(processedItem);\n    }\n    getItemClass(processedItem) {\n      return {\n        ...this.getItemProp(processedItem, 'class'),\n        'p-menuitem': true,\n        'p-highlight': this.isItemActive(processedItem),\n        'p-menuitem-active': this.isItemActive(processedItem),\n        'p-focus': this.isItemFocused(processedItem),\n        'p-disabled': this.isItemDisabled(processedItem)\n      };\n    }\n    getItemLabel(processedItem) {\n      return this.getItemProp(processedItem, 'label');\n    }\n    getSeparatorItemClass(processedItem) {\n      return {\n        ...this.getItemProp(processedItem, 'class'),\n        'p-menuitem-separator': true\n      };\n    }\n    isItemVisible(processedItem) {\n      return this.getItemProp(processedItem, 'visible') !== false;\n    }\n    isItemActive(processedItem) {\n      if (this.activeItemPath) {\n        return this.activeItemPath.some(path => path.key === processedItem.key);\n      }\n    }\n    isItemDisabled(processedItem) {\n      return this.getItemProp(processedItem, 'disabled');\n    }\n    isItemFocused(processedItem) {\n      return this.focusedItemId === this.getItemId(processedItem);\n    }\n    isItemGroup(processedItem) {\n      return ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    getAriaSetSize() {\n      return this.items.filter(processedItem => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n    }\n    getAriaPosInset(index) {\n      return index - this.items.slice(0, index).filter(processedItem => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n    }\n    onItemMouseLeave() {\n      this.menubarService.mouseLeaves.next(true);\n    }\n    onItemMouseEnter(param) {\n      if (this.autoDisplay) {\n        this.menubarService.mouseLeaves.next(false);\n        const {\n          event,\n          processedItem\n        } = param;\n        this.itemMouseEnter.emit({\n          originalEvent: event,\n          processedItem\n        });\n      }\n    }\n    ngOnDestroy() {\n      this.mouseLeaveSubscriber?.unsubscribe();\n    }\n    static ɵfac = function MenubarSub_Factory(t) {\n      return new (t || MenubarSub)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MenubarService));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MenubarSub,\n      selectors: [[\"p-menubarSub\"]],\n      viewQuery: function MenubarSub_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menubarViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        items: \"items\",\n        itemTemplate: \"itemTemplate\",\n        root: \"root\",\n        autoZIndex: \"autoZIndex\",\n        baseZIndex: \"baseZIndex\",\n        mobileActive: \"mobileActive\",\n        autoDisplay: \"autoDisplay\",\n        menuId: \"menuId\",\n        ariaLabel: \"ariaLabel\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        level: \"level\",\n        focusedItemId: \"focusedItemId\",\n        activeItemPath: \"activeItemPath\"\n      },\n      outputs: {\n        itemClick: \"itemClick\",\n        itemMouseEnter: \"itemMouseEnter\",\n        menuFocus: \"menuFocus\",\n        menuBlur: \"menuBlur\",\n        menuKeydown: \"menuKeydown\"\n      },\n      decls: 3,\n      vars: 11,\n      consts: [[\"menubar\", \"\"], [\"listItem\", \"\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"], [\"role\", \"menu\", 3, \"focus\", \"blur\", \"keydown\", \"ngClass\", \"tabindex\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"role\", \"separator\", 3, \"style\", \"ngClass\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"class\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 3, \"ngClass\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [1, \"p-menuitem-content\", 3, \"click\", \"mouseenter\"], [4, \"ngIf\"], [3, \"itemTemplate\", \"items\", \"mobileActive\", \"autoDisplay\", \"menuId\", \"activeItemPath\", \"focusedItemId\", \"level\", \"itemClick\", \"itemMouseEnter\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [3, \"data-pc-section\", \"aria-hidden\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"itemClick\", \"itemMouseEnter\", \"itemTemplate\", \"items\", \"mobileActive\", \"autoDisplay\", \"menuId\", \"activeItemPath\", \"focusedItemId\", \"level\"]],\n      template: function MenubarSub_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"ul\", 4, 0);\n          i0.ɵɵlistener(\"focus\", function MenubarSub_Template_ul_focus_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.menuFocus.emit($event));\n          })(\"blur\", function MenubarSub_Template_ul_blur_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.menuBlur.emit($event));\n          })(\"keydown\", function MenubarSub_Template_ul_keydown_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.menuKeydown.emit($event));\n          });\n          i0.ɵɵtemplate(2, MenubarSub_ng_template_2_Template, 2, 2, \"ng-template\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c1, !ctx.root, ctx.root))(\"tabindex\", 0);\n          i0.ɵɵattribute(\"data-pc-section\", \"menu\")(\"aria-label\", ctx.ariaLabel)(\"aria-labelledBy\", ctx.ariaLabelledBy)(\"id\", ctx.menuId)(\"aria-activedescendant\", ctx.focusedItemId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n        }\n      },\n      dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.RouterLink, i2.RouterLinkActive, i3.Ripple, i4.Tooltip, AngleDownIcon, AngleRightIcon, MenubarSub],\n      encapsulation: 2\n    });\n  }\n  return MenubarSub;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Menubar is a horizontal menu component.\n * @group Components\n */\nlet Menubar = /*#__PURE__*/(() => {\n  class Menubar {\n    document;\n    platformId;\n    el;\n    renderer;\n    cd;\n    config;\n    menubarService;\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    set model(value) {\n      this._model = value;\n      this._processedItems = this.createProcessedItems(this._model || []);\n    }\n    get model() {\n      return this._model;\n    }\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Whether to show a root submenu on mouse over.\n     * @defaultValue true\n     * @group Props\n     */\n    autoDisplay = true;\n    /**\n     * Whether to hide a root submenu when mouse leaves.\n     * @group Props\n     */\n    autoHide;\n    /**\n     * Delay to hide the root submenu in milliseconds when mouse leaves.\n     * @group Props\n     */\n    autoHideDelay = 100;\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    id;\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Callback to execute when button is focused.\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to execute when button loses focus.\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    templates;\n    menubutton;\n    rootmenu;\n    startTemplate;\n    endTemplate;\n    menuIconTemplate;\n    submenuIconTemplate;\n    itemTemplate;\n    mobileActive;\n    outsideClickListener;\n    resizeListener;\n    mouseLeaveSubscriber;\n    dirty = false;\n    focused = false;\n    activeItemPath = signal([]);\n    number = signal(0);\n    focusedItemInfo = signal({\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    });\n    searchValue = '';\n    searchTimeout;\n    _processedItems;\n    _model;\n    get visibleItems() {\n      const processedItem = this.activeItemPath().find(p => p.key === this.focusedItemInfo().parentKey);\n      return processedItem ? processedItem.items : this.processedItems;\n    }\n    get processedItems() {\n      if (!this._processedItems || !this._processedItems.length) {\n        this._processedItems = this.createProcessedItems(this.model || []);\n      }\n      return this._processedItems;\n    }\n    get focusedItemId() {\n      const focusedItem = this.focusedItemInfo();\n      return focusedItem.item && focusedItem.item?.id ? focusedItem.item.id : focusedItem.index !== -1 ? `${this.id}${ObjectUtils.isNotEmpty(focusedItem.parentKey) ? '_' + focusedItem.parentKey : ''}_${focusedItem.index}` : null;\n    }\n    constructor(document, platformId, el, renderer, cd, config, menubarService) {\n      this.document = document;\n      this.platformId = platformId;\n      this.el = el;\n      this.renderer = renderer;\n      this.cd = cd;\n      this.config = config;\n      this.menubarService = menubarService;\n      effect(() => {\n        const path = this.activeItemPath();\n        if (ObjectUtils.isNotEmpty(path)) {\n          this.bindOutsideClickListener();\n          this.bindResizeListener();\n        } else {\n          this.unbindOutsideClickListener();\n          this.unbindResizeListener();\n        }\n      });\n    }\n    ngOnInit() {\n      this.menubarService.autoHide = this.autoHide;\n      this.menubarService.autoHideDelay = this.autoHideDelay;\n      this.mouseLeaveSubscriber = this.menubarService.mouseLeft$.subscribe(() => this.unbindOutsideClickListener());\n      this.id = this.id || UniqueComponentId();\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'start':\n            this.startTemplate = item.template;\n            break;\n          case 'end':\n            this.endTemplate = item.template;\n            break;\n          case 'menuicon':\n            this.menuIconTemplate = item.template;\n            break;\n          case 'submenuicon':\n            this.submenuIconTemplate = item.template;\n            break;\n          case 'item':\n            this.itemTemplate = item.template;\n            break;\n          default:\n            this.itemTemplate = item.template;\n            break;\n        }\n      });\n    }\n    createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n      const processedItems = [];\n      items && items.forEach((item, index) => {\n        const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n        const newItem = {\n          item,\n          index,\n          level,\n          key,\n          parent,\n          parentKey\n        };\n        newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n        processedItems.push(newItem);\n      });\n      return processedItems;\n    }\n    getItemProp(item, name) {\n      return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n    }\n    menuButtonClick(event) {\n      this.toggle(event);\n    }\n    menuButtonKeydown(event) {\n      (event.code === 'Enter' || event.code === 'Space') && this.menuButtonClick(event);\n    }\n    onItemClick(event) {\n      const {\n        originalEvent,\n        processedItem\n      } = event;\n      const grouped = this.isProcessedItemGroup(processedItem);\n      const root = ObjectUtils.isEmpty(processedItem.parent);\n      const selected = this.isSelected(processedItem);\n      if (selected) {\n        const {\n          index,\n          key,\n          level,\n          parentKey,\n          item\n        } = processedItem;\n        this.activeItemPath.set(this.activeItemPath().filter(p => key !== p.key && key.startsWith(p.key)));\n        this.focusedItemInfo.set({\n          index,\n          level,\n          parentKey,\n          item\n        });\n        this.dirty = !root;\n        DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n      } else {\n        if (grouped) {\n          this.onItemChange(event);\n        } else {\n          const rootProcessedItem = root ? processedItem : this.activeItemPath().find(p => p.parentKey === '');\n          this.hide(originalEvent);\n          this.changeFocusedItemIndex(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n          this.mobileActive = false;\n          DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n        }\n      }\n    }\n    onItemMouseEnter(event) {\n      if (!DomHandler.isTouchDevice()) {\n        if (!this.mobileActive) {\n          this.onItemChange(event);\n        }\n      }\n    }\n    changeFocusedItemIndex(event, index) {\n      const processedItem = this.findVisibleItem(index);\n      if (this.focusedItemInfo().index !== index) {\n        const focusedItemInfo = this.focusedItemInfo();\n        this.focusedItemInfo.set({\n          ...focusedItemInfo,\n          item: processedItem.item,\n          index\n        });\n        this.scrollInView();\n      }\n    }\n    scrollInView(index = -1) {\n      const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n      const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${id}\"]`);\n      if (element) {\n        element.scrollIntoView && element.scrollIntoView({\n          block: 'nearest',\n          inline: 'nearest'\n        });\n      }\n    }\n    onItemChange(event) {\n      const {\n        processedItem,\n        isFocus\n      } = event;\n      if (ObjectUtils.isEmpty(processedItem)) return;\n      const {\n        index,\n        key,\n        level,\n        parentKey,\n        items,\n        item\n      } = processedItem;\n      const grouped = ObjectUtils.isNotEmpty(items);\n      const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== parentKey && p.parentKey !== key);\n      grouped && activeItemPath.push(processedItem);\n      this.focusedItemInfo.set({\n        index,\n        level,\n        parentKey,\n        item\n      });\n      this.activeItemPath.set(activeItemPath);\n      grouped && (this.dirty = true);\n      isFocus && DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n    }\n    toggle(event) {\n      if (this.mobileActive) {\n        this.mobileActive = false;\n        ZIndexUtils.clear(this.rootmenu.el.nativeElement);\n        this.hide();\n      } else {\n        this.mobileActive = true;\n        ZIndexUtils.set('menu', this.rootmenu.el.nativeElement, this.config.zIndex.menu);\n        setTimeout(() => {\n          this.show();\n        }, 0);\n      }\n      this.cd.markForCheck();\n      this.bindOutsideClickListener();\n      event.preventDefault();\n    }\n    hide(event, isFocus) {\n      if (this.mobileActive) {\n        setTimeout(() => {\n          DomHandler.focus(this.menubutton.nativeElement);\n        }, 0);\n      }\n      this.activeItemPath.set([]);\n      this.focusedItemInfo.set({\n        index: -1,\n        level: 0,\n        parentKey: '',\n        item: null\n      });\n      isFocus && DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n      this.dirty = false;\n    }\n    show() {\n      const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n      this.focusedItemInfo.set({\n        index: this.findFirstFocusedItemIndex(),\n        level: 0,\n        parentKey: '',\n        item: processedItem.item\n      });\n      DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n    }\n    onMenuFocus(event) {\n      this.focused = true;\n      const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n      const focusedItemInfo = this.focusedItemInfo().index !== -1 ? this.focusedItemInfo() : {\n        index: this.findFirstFocusedItemIndex(),\n        level: 0,\n        parentKey: '',\n        item: processedItem.item\n      };\n      this.focusedItemInfo.set(focusedItemInfo);\n      this.onFocus.emit(event);\n    }\n    onMenuBlur(event) {\n      this.focused = false;\n      this.focusedItemInfo.set({\n        index: -1,\n        level: 0,\n        parentKey: '',\n        item: null\n      });\n      this.searchValue = '';\n      this.dirty = false;\n      this.onBlur.emit(event);\n    }\n    onKeyDown(event) {\n      const metaKey = event.metaKey || event.ctrlKey;\n      switch (event.code) {\n        case 'ArrowDown':\n          this.onArrowDownKey(event);\n          break;\n        case 'ArrowUp':\n          this.onArrowUpKey(event);\n          break;\n        case 'ArrowLeft':\n          this.onArrowLeftKey(event);\n          break;\n        case 'ArrowRight':\n          this.onArrowRightKey(event);\n          break;\n        case 'Home':\n          this.onHomeKey(event);\n          break;\n        case 'End':\n          this.onEndKey(event);\n          break;\n        case 'Space':\n          this.onSpaceKey(event);\n          break;\n        case 'Enter':\n          this.onEnterKey(event);\n          break;\n        case 'Escape':\n          this.onEscapeKey(event);\n          break;\n        case 'Tab':\n          this.onTabKey(event);\n          break;\n        case 'PageDown':\n        case 'PageUp':\n        case 'Backspace':\n        case 'ShiftLeft':\n        case 'ShiftRight':\n          //NOOP\n          break;\n        default:\n          if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n            this.searchItems(event, event.key);\n          }\n          break;\n      }\n    }\n    findVisibleItem(index) {\n      return ObjectUtils.isNotEmpty(this.visibleItems) ? this.visibleItems[index] : null;\n    }\n    findFirstFocusedItemIndex() {\n      const selectedIndex = this.findSelectedItemIndex();\n      return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n    }\n    findFirstItemIndex() {\n      return this.visibleItems.findIndex(processedItem => this.isValidItem(processedItem));\n    }\n    findSelectedItemIndex() {\n      return this.visibleItems.findIndex(processedItem => this.isValidSelectedItem(processedItem));\n    }\n    isProcessedItemGroup(processedItem) {\n      return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    isSelected(processedItem) {\n      return this.activeItemPath().some(p => p.key === processedItem.key);\n    }\n    isValidSelectedItem(processedItem) {\n      return this.isValidItem(processedItem) && this.isSelected(processedItem);\n    }\n    isValidItem(processedItem) {\n      return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n    }\n    isItemDisabled(item) {\n      return this.getItemProp(item, 'disabled');\n    }\n    isItemSeparator(item) {\n      return this.getItemProp(item, 'separator');\n    }\n    isItemMatched(processedItem) {\n      return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n    }\n    isProccessedItemGroup(processedItem) {\n      return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    searchItems(event, char) {\n      this.searchValue = (this.searchValue || '') + char;\n      let itemIndex = -1;\n      let matched = false;\n      if (this.focusedItemInfo().index !== -1) {\n        itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem));\n        itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n      } else {\n        itemIndex = this.visibleItems.findIndex(processedItem => this.isItemMatched(processedItem));\n      }\n      if (itemIndex !== -1) {\n        matched = true;\n      }\n      if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n        itemIndex = this.findFirstFocusedItemIndex();\n      }\n      if (itemIndex !== -1) {\n        this.changeFocusedItemIndex(event, itemIndex);\n      }\n      if (this.searchTimeout) {\n        clearTimeout(this.searchTimeout);\n      }\n      this.searchTimeout = setTimeout(() => {\n        this.searchValue = '';\n        this.searchTimeout = null;\n      }, 500);\n      return matched;\n    }\n    getProccessedItemLabel(processedItem) {\n      return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n    }\n    getItemLabel(item) {\n      return this.getItemProp(item, 'label');\n    }\n    onArrowDownKey(event) {\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      const root = processedItem ? ObjectUtils.isEmpty(processedItem.parent) : null;\n      if (root) {\n        const grouped = this.isProccessedItemGroup(processedItem);\n        if (grouped) {\n          this.onItemChange({\n            originalEvent: event,\n            processedItem\n          });\n          this.focusedItemInfo.set({\n            index: -1,\n            parentKey: processedItem.key,\n            item: processedItem.item\n          });\n          this.onArrowRightKey(event);\n        }\n      } else {\n        const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n        this.changeFocusedItemIndex(event, itemIndex);\n        event.preventDefault();\n      }\n    }\n    onArrowRightKey(event) {\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      const parentItem = processedItem ? this.activeItemPath().find(p => p.key === processedItem.parentKey) : null;\n      if (parentItem) {\n        const grouped = this.isProccessedItemGroup(processedItem);\n        if (grouped) {\n          this.onItemChange({\n            originalEvent: event,\n            processedItem\n          });\n          this.focusedItemInfo.set({\n            index: -1,\n            parentKey: processedItem.key,\n            item: processedItem.item\n          });\n          this.onArrowDownKey(event);\n        }\n      } else {\n        const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n        this.changeFocusedItemIndex(event, itemIndex);\n        event.preventDefault();\n      }\n    }\n    onArrowUpKey(event) {\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      const root = ObjectUtils.isEmpty(processedItem.parent);\n      if (root) {\n        const grouped = this.isProccessedItemGroup(processedItem);\n        if (grouped) {\n          this.onItemChange({\n            originalEvent: event,\n            processedItem\n          });\n          this.focusedItemInfo.set({\n            index: -1,\n            parentKey: processedItem.key,\n            item: processedItem.item\n          });\n          const itemIndex = this.findLastItemIndex();\n          this.changeFocusedItemIndex(event, itemIndex);\n        }\n      } else {\n        const parentItem = this.activeItemPath().find(p => p.key === processedItem.parentKey);\n        if (this.focusedItemInfo().index === 0) {\n          this.focusedItemInfo.set({\n            index: -1,\n            parentKey: parentItem ? parentItem.parentKey : '',\n            item: processedItem.item\n          });\n          this.searchValue = '';\n          this.onArrowLeftKey(event);\n          const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== this.focusedItemInfo().parentKey);\n          this.activeItemPath.set(activeItemPath);\n        } else {\n          const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n          this.changeFocusedItemIndex(event, itemIndex);\n        }\n      }\n      event.preventDefault();\n    }\n    onArrowLeftKey(event) {\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      const parentItem = processedItem ? this.activeItemPath().find(p => p.key === processedItem.parentKey) : null;\n      if (parentItem) {\n        this.onItemChange({\n          originalEvent: event,\n          processedItem: parentItem\n        });\n        const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== this.focusedItemInfo().parentKey);\n        this.activeItemPath.set(activeItemPath);\n        event.preventDefault();\n      } else {\n        const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n        this.changeFocusedItemIndex(event, itemIndex);\n        event.preventDefault();\n      }\n    }\n    onHomeKey(event) {\n      this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n      event.preventDefault();\n    }\n    onEndKey(event) {\n      this.changeFocusedItemIndex(event, this.findLastItemIndex());\n      event.preventDefault();\n    }\n    onSpaceKey(event) {\n      this.onEnterKey(event);\n    }\n    onEscapeKey(event) {\n      this.hide(event, true);\n      this.focusedItemInfo().index = this.findFirstFocusedItemIndex();\n      event.preventDefault();\n    }\n    onTabKey(event) {\n      if (this.focusedItemInfo().index !== -1) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const grouped = this.isProccessedItemGroup(processedItem);\n        !grouped && this.onItemChange({\n          originalEvent: event,\n          processedItem\n        });\n      }\n      this.hide();\n    }\n    onEnterKey(event) {\n      if (this.focusedItemInfo().index !== -1) {\n        const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n        const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n        anchorElement ? anchorElement.click() : element && element.click();\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const grouped = this.isProccessedItemGroup(processedItem);\n        !grouped && (this.focusedItemInfo().index = this.findFirstFocusedItemIndex());\n      }\n      event.preventDefault();\n    }\n    findLastFocusedItemIndex() {\n      const selectedIndex = this.findSelectedItemIndex();\n      return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n    }\n    findLastItemIndex() {\n      return ObjectUtils.findLastIndex(this.visibleItems, processedItem => this.isValidItem(processedItem));\n    }\n    findPrevItemIndex(index) {\n      const matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleItems.slice(0, index), processedItem => this.isValidItem(processedItem)) : -1;\n      return matchedItemIndex > -1 ? matchedItemIndex : index;\n    }\n    findNextItemIndex(index) {\n      const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex(processedItem => this.isValidItem(processedItem)) : -1;\n      return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n    }\n    bindResizeListener() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.resizeListener) {\n          this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', event => {\n            if (!DomHandler.isTouchDevice()) {\n              this.hide(event, true);\n            }\n            this.mobileActive = false;\n          });\n        }\n      }\n    }\n    bindOutsideClickListener() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.outsideClickListener) {\n          this.outsideClickListener = this.renderer.listen(this.document, 'click', event => {\n            const isOutsideContainer = this.rootmenu.el.nativeElement !== event.target && !this.rootmenu.el.nativeElement.contains(event.target);\n            const isOutsideMenuButton = this.mobileActive && this.menubutton.nativeElement !== event.target && !this.menubutton.nativeElement.contains(event.target);\n            if (isOutsideContainer) {\n              isOutsideMenuButton ? this.mobileActive = false : this.hide();\n            }\n          });\n        }\n      }\n    }\n    unbindOutsideClickListener() {\n      if (this.outsideClickListener) {\n        this.outsideClickListener();\n        this.outsideClickListener = null;\n      }\n    }\n    unbindResizeListener() {\n      if (this.resizeListener) {\n        this.resizeListener();\n        this.resizeListener = null;\n      }\n    }\n    ngOnDestroy() {\n      this.mouseLeaveSubscriber?.unsubscribe();\n      this.unbindOutsideClickListener();\n      this.unbindResizeListener();\n    }\n    static ɵfac = function Menubar_Factory(t) {\n      return new (t || Menubar)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.PrimeNGConfig), i0.ɵɵdirectiveInject(MenubarService));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Menubar,\n      selectors: [[\"p-menubar\"]],\n      contentQueries: function Menubar_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Menubar_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c5, 5);\n          i0.ɵɵviewQuery(_c6, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menubutton = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rootmenu = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        model: \"model\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        autoZIndex: \"autoZIndex\",\n        baseZIndex: \"baseZIndex\",\n        autoDisplay: \"autoDisplay\",\n        autoHide: \"autoHide\",\n        autoHideDelay: \"autoHideDelay\",\n        id: \"id\",\n        ariaLabel: \"ariaLabel\",\n        ariaLabelledBy: \"ariaLabelledBy\"\n      },\n      outputs: {\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\"\n      },\n      features: [i0.ɵɵProvidersFeature([MenubarService])],\n      ngContentSelectors: _c7,\n      decls: 8,\n      vars: 24,\n      consts: [[\"rootmenu\", \"\"], [\"legacy\", \"\"], [\"menubutton\", \"\"], [3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-menubar-start\", 4, \"ngIf\"], [\"tabindex\", \"0\", \"role\", \"button\", \"class\", \"p-menubar-button\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [3, \"itemClick\", \"menuFocus\", \"menuBlur\", \"menuKeydown\", \"itemMouseEnter\", \"items\", \"itemTemplate\", \"menuId\", \"root\", \"baseZIndex\", \"autoZIndex\", \"mobileActive\", \"autoDisplay\", \"ariaLabel\", \"ariaLabelledBy\", \"focusedItemId\", \"activeItemPath\"], [\"class\", \"p-menubar-end\", 4, \"ngIf\", \"ngIfElse\"], [1, \"p-menubar-start\"], [4, \"ngTemplateOutlet\"], [\"tabindex\", \"0\", \"role\", \"button\", 1, \"p-menubar-button\", 3, \"click\", \"keydown\"], [4, \"ngIf\"], [1, \"p-menubar-end\"]],\n      template: function Menubar_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 3);\n          i0.ɵɵtemplate(1, Menubar_div_1_Template, 2, 1, \"div\", 4)(2, Menubar_a_2_Template, 4, 7, \"a\", 5);\n          i0.ɵɵelementStart(3, \"p-menubarSub\", 6, 0);\n          i0.ɵɵlistener(\"itemClick\", function Menubar_Template_p_menubarSub_itemClick_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onItemClick($event));\n          })(\"menuFocus\", function Menubar_Template_p_menubarSub_menuFocus_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMenuFocus($event));\n          })(\"menuBlur\", function Menubar_Template_p_menubarSub_menuBlur_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMenuBlur($event));\n          })(\"menuKeydown\", function Menubar_Template_p_menubarSub_menuKeydown_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onKeyDown($event));\n          })(\"itemMouseEnter\", function Menubar_Template_p_menubarSub_itemMouseEnter_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onItemMouseEnter($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, Menubar_div_5_Template, 2, 1, \"div\", 7)(6, Menubar_ng_template_6_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const legacy_r4 = i0.ɵɵreference(7);\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(22, _c8, ctx.mobileActive))(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"data-pc-section\", \"root\")(\"data-pc-name\", \"menubar\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.startTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.model && ctx.model.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"items\", ctx.processedItems)(\"itemTemplate\", ctx.itemTemplate)(\"menuId\", ctx.id)(\"root\", true)(\"baseZIndex\", ctx.baseZIndex)(\"autoZIndex\", ctx.autoZIndex)(\"mobileActive\", ctx.mobileActive)(\"autoDisplay\", ctx.autoDisplay)(\"ariaLabel\", ctx.ariaLabel)(\"ariaLabelledBy\", ctx.ariaLabelledBy)(\"focusedItemId\", ctx.focused ? ctx.focusedItemId : undefined)(\"activeItemPath\", ctx.activeItemPath());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.endTemplate)(\"ngIfElse\", legacy_r4);\n        }\n      },\n      dependencies: () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, BarsIcon, MenubarSub],\n      styles: [\"@layer primeng{.p-menubar{display:flex;align-items:center}.p-menubar ul{margin:0;padding:0;list-style:none}.p-menubar .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menubar .p-menuitem-text{line-height:1}.p-menubar .p-menuitem{position:relative}.p-menubar-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-menubar-root-list>li ul{display:none;z-index:1}.p-menubar-root-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block}.p-menubar .p-submenu-list{display:none;position:absolute;z-index:2}.p-menubar .p-submenu-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block;left:100%;top:0}.p-menubar .p-submenu-list .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-menubar .p-menubar-root-list .p-icon-wrapper,.p-menubar .p-submenu-list .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-menubar .p-menubar-custom,.p-menubar .p-menubar-end{margin-left:auto;align-self:center}.p-menubar-button{display:none;cursor:pointer;align-items:center;justify-content:center}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Menubar;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MenubarModule = /*#__PURE__*/(() => {\n  class MenubarModule {\n    static ɵfac = function MenubarModule_Factory(t) {\n      return new (t || MenubarModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MenubarModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, BarsIcon, AngleDownIcon, AngleRightIcon, RouterModule, TooltipModule, SharedModule]\n    });\n  }\n  return MenubarModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Menubar, MenubarModule, MenubarService, MenubarSub };\n//# sourceMappingURL=primeng-menubar.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}