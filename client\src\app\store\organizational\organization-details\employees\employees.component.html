<div class="p-3 w-full surface-card border-round shadow-1 mb-5">
    <div class="card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2">
        <h4 class="m-0 pl-3 left-border relative flex">Employees</h4>
        <div class="flex gap-3 ml-auto align-items-center">
            <p-button label="Add New" (click)="showNewDialog('right','employee')" icon="pi pi-plus-circle"
                iconPos="right" class="ml-auto" [styleClass]="'font-semibold px-3'" [rounded]="true" />

            <p-multiSelect [options]="cols" [ngModel]="getSelectedColumns('employee')"
                (ngModelChange)="setSelectedColumns('employee', $event)" optionLabel="header"
                class="table-multiselect-dropdown" [styleClass]="
          'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'
        ">
            </p-multiSelect>
        </div>
    </div>

    <div class="table-sec">
        <p-table [value]="employeeDetails" dataKey="id" [rows]="14" [paginator]="true" [lazy]="true"
            responsiveLayout="scroll" [scrollable]="true" class="scrollable-table" [reorderableColumns]="true"
            (onColReorder)="onColumnReorder($event,'employee')" (onSort)="customSort($event.field,'employee')">
            <ng-template pTemplate="header">
                <tr>
                    <th pFrozenColumn class="border-round-left-lg pl-3 w-2rem table-checkbox text-center">
                        <p-tableHeaderCheckbox />
                    </th>
                    <th pFrozenColumn (click)="customSort('start_date','employee')">
                        <div class="flex align-items-center cursor-pointer">
                            Valid From
                            <i *ngIf="sortFieldMap['employee'] === 'start_date'" class="ml-2 pi" [ngClass]="
                  sortOrderMap['employee'] === 1
                    ? 'pi-sort-amount-up-alt'
                    : 'pi-sort-amount-down'
                ">
                            </i>
                            <i *ngIf="sortFieldMap['employee'] !== 'start_date'" class="ml-2 pi pi-sort"></i>
                        </div>
                    </th>
                    <ng-container *ngFor="let col of getSelectedColumns('employee')">
                        <th [pSortableColumn]="col.field" pReorderableColumn (click)="customSort(col.field,'employee')">
                            <div class="flex align-items-center cursor-pointer">
                                {{ col.header }}
                                <i *ngIf="sortFieldMap['employee'] === col.field" class="ml-2 pi" [ngClass]="
                    sortOrderMap['employee'] === 1
                      ? 'pi-sort-amount-up-alt'
                      : 'pi-sort-amount-down'
                  ">
                                </i>
                                <i *ngIf="sortFieldMap['employee'] !== col.field" class="ml-2 pi pi-sort"></i>
                            </div>
                        </th>
                    </ng-container>
                    <th>
                        <div class="flex align-items-center">Actions</div>
                    </th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-employee let-columns="columns">
                <tr class="cursor-pointer">
                    <td pFrozenColumn class="border-round-left-lg pl-3 w-2rem text-center">
                        <p-tableCheckbox [value]="employee" />
                    </td>
                    <td pFrozenColumn class="font-medium">
                        {{ employee?.start_date ? (employee.start_date | date: 'dd/MM/yyyy') : '-' }}
                    </td>

                    <ng-container *ngFor="let col of getSelectedColumns('employee')">
                        <td>
                            <ng-container [ngSwitch]="col.field">
                                <ng-container *ngSwitchCase="'end_date'">
                                    {{ employee?.end_date ? (employee.end_date | date: 'dd/MM/yyyy') : '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'business_partner.bp_full_name'">
                                    {{ employee?.business_partner?.bp_full_name || '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'job_id'">
                                    {{ employee?.job_id || '-' }}
                                </ng-container>
                            </ng-container>
                        </td>
                    </ng-container>

                    <td>
                        <div class="flex align-items-center">
                            <button pButton type="button" class="mr-2" icon="pi pi-pencil" pTooltip="Edit"
                                (click)="editEmployee(employee)"></button>
                            <button pButton type="button" icon="pi pi-trash" pTooltip="Delete"
                                (click)="$event.stopPropagation(); confirmRemove(employee,'employee')"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td class="border-round-left-lg" colspan="6">No employees found.</td>
                </tr>
            </ng-template>
            <ng-template pTemplate="loadingbody">
                <tr>
                    <td colspan="6" class="border-round-left-lg">
                        Loading employees data. Please wait...
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>
<p-dialog [modal]="true" [(visible)]="addEmployeeDialogVisible" [style]="{ width: '45rem' }" [position]="'right'"
    [draggable]="false" class="opportunity-contact-popup">
    <ng-template pTemplate="header">
        <h4>Employee</h4>
    </ng-template>

    <form [formGroup]="EmployeeForm" class="relative flex flex-column gap-1">
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Valid From">
                <span class="material-symbols-rounded">badge</span>Valid From
                <span class="text-red-500">*</span>
            </label>
            <div class="form-input flex-1 relative">
                <p-calendar formControlName="start_date" inputId="calendar-12h" hourFormat="12" [showIcon]="true"
                    styleClass="h-3rem w-full" placeholder="Valid From" appendTo="body"
                    [ngClass]="{ 'is-invalid': submitted && f['start_date'].errors }" />
                <div *ngIf="submitted && f['start_date'].errors" class="p-error">
                    <div *ngIf="
              submitted &&
              f['start_date'].errors &&
              f['start_date'].errors['required']
            ">
                        Valid From is required.
                    </div>
                </div>
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Valid To">
                <span class="material-symbols-rounded">badge</span>Valid To
                <span class="text-red-500">*</span>
            </label>
            <div class="form-input flex-1 relative">
                <p-calendar formControlName="end_date" inputId="calendar-12h" hourFormat="12" [showIcon]="true"
                    styleClass="h-3rem w-full" placeholder="Valid To" appendTo="body"
                    [ngClass]="{ 'is-invalid': submitted && f['end_date'].errors }" />
                <div *ngIf="submitted && f['end_date'].errors" class="p-error">
                    <div *ngIf="
              submitted &&
              f['end_date'].errors &&
              f['end_date'].errors['required']
            ">
                        Valid To is required.
                    </div>
                </div>
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Employee">
                <span class="material-symbols-rounded">person</span>Employee
            </label>
            <div class="form-input flex-1 relative">
                <ng-select pInputText [items]="employees$ | async" bindLabel="bp_full_name" bindValue="bp_id"
                    [hideSelected]="true" [loading]="employeeLoading" [minTermLength]="0"
                    formControlName="business_partner_internal_id" [typeahead]="employeeInput$" [maxSelectedItems]="10"
                    appendTo="body" [class]="'multiselect-dropdown p-inputtext p-component p-element'"
                    placeholder="Search for an employee">
                    <ng-template ng-option-tmp let-item="item">
                        <span>{{ item.bp_id }}</span>
                        <span *ngIf="item.bp_full_name"> : {{ item.bp_full_name }}</span>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="flex align-items-center p-3 gap-3 mt-1">
            <button pButton type="button" label="Cancel"
                class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem"
                (click)="addEmployeeDialogVisible = false"></button>
            <button pButton type="submit" label="Save" class="p-button-rounded justify-content-center w-9rem h-3rem"
                (click)="onSubmit()"></button>
        </div>
    </form>
</p-dialog>

<div class="p-3 w-full surface-card border-round shadow-1">
    <div class="card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2">
        <h4 class="m-0 pl-3 left-border relative flex">Managers</h4>
        <div class="flex gap-3 ml-auto align-items-center">
            <p-button label="Add New" (click)="showNewDialog('right','manager')" icon="pi pi-plus-circle"
                iconPos="right" class="ml-auto" [styleClass]="'font-semibold px-3'" [rounded]="true" />

            <p-multiSelect [options]="colsmanager" [ngModel]="getSelectedColumns('manager')"
                (ngModelChange)="setSelectedColumns('manager', $event)" optionLabel="header"
                class="table-multiselect-dropdown" [styleClass]="
          'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'
        ">
            </p-multiSelect>
        </div>
    </div>

    <div class="table-sec">
        <p-table [value]="managerDetails" dataKey="id" [rows]="14" [paginator]="true" [lazy]="true"
            responsiveLayout="scroll" [scrollable]="true" class="scrollable-table" [reorderableColumns]="true"
            (onColReorder)="onColumnReorder($event,'manager')">
            <ng-template pTemplate="header">
                <tr>
                    <th pFrozenColumn class="border-round-left-lg pl-3 w-2rem table-checkbox text-center">
                        <p-tableHeaderCheckbox />
                    </th>
                    <th pFrozenColumn (click)="customSort('start_date','manager')">
                        <div class="flex align-items-center cursor-pointer">
                            Valid From
                            <i *ngIf="sortFieldMap['manager'] === 'start_date'" class="ml-2 pi" [ngClass]="
                  sortOrderMap['manager'] === 1
                    ? 'pi-sort-amount-up-alt'
                    : 'pi-sort-amount-down'
                ">
                            </i>
                            <i *ngIf="sortFieldMap['manager'] !== 'start_date'" class="ml-2 pi pi-sort"></i>
                        </div>
                    </th>
                    <ng-container *ngFor="let col of getSelectedColumns('manager')">
                        <th [pSortableColumn]="col.field" pReorderableColumn (click)="customSort(col.field,'manager')">
                            <div class="flex align-items-center cursor-pointer">
                                {{ col.header }}
                                <i *ngIf="sortFieldMap['manager'] === col.field" class="ml-2 pi" [ngClass]="
                    sortOrderMap['manager'] === 1
                      ? 'pi-sort-amount-up-alt'
                      : 'pi-sort-amount-down'
                  ">
                                </i>
                                <i *ngIf="sortFieldMap['manager'] !== col.field" class="ml-2 pi pi-sort"></i>
                            </div>
                        </th>
                    </ng-container>
                    <th>
                        <div class="flex align-items-center">Actions</div>
                    </th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-manager let-columns="columns">
                <tr class="cursor-pointer">
                    <td pFrozenColumn class="border-round-left-lg pl-3 w-2rem text-center">
                        <p-tableCheckbox [value]="manager" />
                    </td>
                    <td pFrozenColumn class="font-medium">
                        {{ manager?.start_date ? (manager.start_date | date: 'dd/MM/yyyy') : '-' }}
                    </td>

                    <ng-container *ngFor="let col of getSelectedColumns('manager')">
                        <td>
                            <ng-container [ngSwitch]="col.field">
                                <ng-container *ngSwitchCase="'end_date'">
                                    {{ manager?.end_date ? (manager.end_date | date: 'dd/MM/yyyy') : '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'business_partner.bp_full_name'">
                                    {{ manager?.business_partner?.bp_full_name || '-' }}
                                </ng-container>
                            </ng-container>
                        </td>
                    </ng-container>

                    <td>
                        <div class="flex align-items-center">
                            <button pButton type="button" class="mr-2" icon="pi pi-pencil" pTooltip="Edit"
                                (click)="editManager(manager)"></button>
                            <button pButton type="button" icon="pi pi-trash" pTooltip="Delete"
                                (click)="$event.stopPropagation(); confirmRemove(manager,'manager')"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td class="border-round-left-lg" colspan="6">No managers found.</td>
                </tr>
            </ng-template>
            <ng-template pTemplate="loadingbody">
                <tr>
                    <td colspan="6" class="border-round-left-lg">
                        Loading managers data. Please wait...
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>
<p-dialog [modal]="true" [(visible)]="addManagerDialogVisible" [style]="{ width: '45rem' }" [position]="'right'"
    [draggable]="false" class="opportunity-contact-popup">
    <ng-template pTemplate="header">
        <h4>Manager</h4>
    </ng-template>

    <form [formGroup]="ManagerForm" class="relative flex flex-column gap-1">
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Valid From">
                <span class="material-symbols-rounded">badge</span>Valid From
                <span class="text-red-500">*</span>
            </label>
            <div class="form-input flex-1 relative">
                <p-calendar formControlName="start_date" inputId="calendar-12h" hourFormat="12" [showIcon]="true"
                    styleClass="h-3rem w-full" placeholder="Valid From" appendTo="body"
                    [ngClass]="{ 'is-invalid': submitted && fManager['start_date'].errors }" />
                <div *ngIf="submitted && fManager['start_date'].errors" class="p-error">
                    <div *ngIf="
              submitted &&
              fManager['start_date'].errors &&
              fManager['start_date'].errors['required']
            ">
                        Valid From is required.
                    </div>
                </div>
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Valid To">
                <span class="material-symbols-rounded">badge</span>Valid To
                <span class="text-red-500">*</span>
            </label>
            <div class="form-input flex-1 relative">
                <p-calendar formControlName="end_date" inputId="calendar-12h" hourFormat="12" [showIcon]="true"
                    styleClass="h-3rem w-full" placeholder="Valid To" appendTo="body"
                    [ngClass]="{ 'is-invalid': submitted && fManager['end_date'].errors }" />
                <div *ngIf="submitted && fManager['end_date'].errors" class="p-error">
                    <div *ngIf="
              submitted &&
              fManager['end_date'].errors &&
              fManager['end_date'].errors['required']
            ">
                        Valid To is required.
                    </div>
                </div>
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Manager">
                <span class="material-symbols-rounded">person</span>Manager
            </label>
            <div class="form-input flex-1 relative">
                <ng-select pInputText [items]="employees$ | async" bindLabel="bp_full_name" bindValue="bp_id"
                    [hideSelected]="true" [loading]="employeeLoading" [minTermLength]="0"
                    formControlName="business_partner_internal_id" [typeahead]="employeeInput$" [maxSelectedItems]="10"
                    appendTo="body" [class]="'multiselect-dropdown p-inputtext p-component p-element'"
                    placeholder="Search for a manager">
                    <ng-template ng-option-tmp let-item="item">
                        <span>{{ item.bp_id }}</span>
                        <span *ngIf="item.bp_full_name"> : {{ item.bp_full_name }}</span>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="flex align-items-center p-3 gap-3 mt-1">
            <button pButton type="button" label="Cancel"
                class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem"
                (click)="addManagerDialogVisible = false"></button>
            <button pButton type="submit" label="Save" class="p-button-rounded justify-content-center w-9rem h-3rem"
                (click)="onSubmitManager()"></button>
        </div>
    </form>
</p-dialog>