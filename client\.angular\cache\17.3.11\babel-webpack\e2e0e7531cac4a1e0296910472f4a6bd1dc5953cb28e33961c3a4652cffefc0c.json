{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"./layout/service/app.layout.service\";\nimport * as i3 from \"@angular/router\";\nexport class StoreComponent {\n  constructor(primengConfig, renderer, layoutService) {\n    this.primengConfig = primengConfig;\n    this.renderer = renderer;\n    this.layoutService = layoutService;\n  }\n  ngOnInit() {\n    // Inject theme \n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\n    const link = this.renderer.createElement('link');\n    this.renderer.setAttribute(link, 'id', 'theme-link');\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\n    this.renderer.setAttribute(link, 'type', 'text/css');\n    this.renderer.setAttribute(link, 'href', href);\n    // Append the link tag to the head of the document\n    this.renderer.appendChild(document.head, link);\n    this.primengConfig.ripple = true; //enables core ripple functionality\n    //optional configuration with the default configuration\n    const config = {\n      ripple: false,\n      //toggles ripple on and off\n      menuMode: 'reveal',\n      //layout mode of the menu, valid values are \"static\", \"overlay\", \"slim\", \"horizontal\", \"drawer\" and \"reveal\"\n      colorScheme: 'light',\n      //color scheme of the template, valid values are \"light\" and \"dark\"\n      theme: 'snjya',\n      //default component theme for PrimeNG\n      scale: 14 //size of the body font size to scale the whole application\n    };\n    this.layoutService.config.set(config);\n  }\n  ngOnDestroy() {\n    // Find and remove the link tag when the component is destroyed\n    const link = document.getElementById('theme-link');\n    if (link) {\n      link.remove();\n    }\n  }\n  static {\n    this.ɵfac = function StoreComponent_Factory(t) {\n      return new (t || StoreComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.LayoutService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoreComponent,\n      selectors: [[\"app-store\"]],\n      decls: 1,\n      vars: 0,\n      template: function StoreComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i3.RouterOutlet],\n      styles: [\"{\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n  .bg-light-blue {\\n  background: #c3dbff !important;\\n}\\n  .object-fit-contain {\\n  object-fit: contain;\\n}\\n  .object-fit-cover {\\n  object-fit: cover;\\n}\\n  .transition-03 {\\n  transition: all 0.3s ease-in-out;\\n}\\n  .h-36rem {\\n  height: 36rem !important;\\n}\\n  .h-34rem {\\n  height: 34rem !important;\\n}\\n  .surface-b {\\n  background: var(--surface-b) !important;\\n}\\n  .h-3-3rem {\\n  height: 3.3rem;\\n}\\n  .d-grid {\\n  display: grid !important;\\n}\\n  .w-fit {\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n  .min-w-3rem {\\n  min-width: 3rem !important;\\n}\\n  .min-w-16 {\\n  min-width: 16rem !important;\\n}\\n  .min-w-14 {\\n  min-width: 14rem !important;\\n}\\n  .min-h-14 {\\n  min-height: 14rem !important;\\n}\\n  .min-h-18 {\\n  min-height: 18rem !important;\\n}\\n  .min-h-30 {\\n  min-height: 30rem !important;\\n}\\n  .font-900 {\\n  font-weight: 900 !important;\\n}\\n  .surface-b {\\n  background: var(--surface-b) !important;\\n}\\n  .object-fit-contain {\\n  object-fit: contain;\\n}\\n  .transition-03 {\\n  transition: all 0.3s ease-in-out;\\n}\\n  .p-datatable-wrapper thead p-sorticon svg {\\n  color: var(--white);\\n}\\n  .header-title:before,   .left-border:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  top: 0;\\n  bottom: 0;\\n  margin: auto;\\n  width: 5px;\\n  height: 24px;\\n  background: var(--primary-color);\\n  border-radius: 50px;\\n}\\n  .layout-sidebar {\\n  width: 20rem;\\n}\\n  .layout-content-wrapper {\\n  background: var(--surface-0);\\n}\\n  .bg-whight-light {\\n  background: #f6f7f9;\\n}\\n  .all-overview-body {\\n  min-height: calc(100vh - 90px);\\n}\\n  .all-overview-body .p-galleria .p-galleria-item-nav {\\n  color: var(--text-color) !important;\\n  background: var(--surface-0) !important;\\n  width: 3rem !important;\\n  height: 3rem !important;\\n  transform: translateY(-50%);\\n  z-index: 99;\\n}\\n  .all-overview-body .p-galleria .p-galleria-item-nav .p-icon-wrapper .p-icon {\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n  .all-overview-body .card-list .v-details-list .v-details-box .text {\\n  min-width: 120px;\\n}\\n  .all-overview-body p-table table thead th {\\n  height: 44px;\\n  background: var(--primary-color);\\n  color: var(--surface-0);\\n}\\n  .all-overview-body p-table table tbody td {\\n  height: 44px;\\n}\\n  .all-overview-body .v-details-list .v-details-box .text {\\n  min-width: 182px;\\n  min-width: 182px;\\n}\\n  .all-overview-body .order-details-list {\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n}\\n  .all-overview-body .order-details-list {\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n}\\n  .p-inputtext {\\n  appearance: auto !important;\\n}\\n  .border-left-5 {\\n  border-left: 5px solid var(--orange-200);\\n}\\n  .p-calendar {\\n  display: flex;\\n}\\n  .p-calendar .p-button-icon-only {\\n  width: 3rem;\\n}\\n  .max-w-1200 {\\n  max-width: 1200px;\\n}\\n  .text-shadow-l-blue {\\n  text-shadow: 0 2px 6px rgba(0, 63, 147, 0.8);\\n}\\n  .h-32rem {\\n  height: 32rem !important;\\n}\\n  .h-2-8rem {\\n  height: 2.8rem !important;\\n}\\n  .p-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-text {\\n  color: var(--text-color);\\n  font-weight: 600;\\n}\\n  p-paginator .p-paginator {\\n  padding: 20px 0;\\n  margin: 1.5rem 0 0 0;\\n  border-top: 1px solid var(--surface-d);\\n  border-radius: 0;\\n}\\n  p-paginator .p-paginator button {\\n  width: 2rem;\\n  height: 2rem;\\n  border-radius: 0.3rem;\\n  border: 1px solid var(--surface-c);\\n}\\n  p-paginator .p-paginator p-dropdown {\\n  display: none;\\n}\\n  .table-sec tbody:before {\\n  line-height: 20px;\\n  content: \\\"_\\\";\\n  color: transparent;\\n  display: block;\\n}\\n  .table-sec tbody tr:nth-child(odd) td {\\n  background: var(--surface-b);\\n}\\n  .table-sec thead th .p-checkbox .p-checkbox-box.p-highlight {\\n  border-color: var(--surface-0);\\n}\\n  .table-sec thead th:last-child {\\n  border-top-right-radius: 0.5rem !important;\\n  border-bottom-right-radius: 0.5rem !important;\\n}\\n  .table-sec tbody td:last-child {\\n  border-top-right-radius: 0.5rem !important;\\n  border-bottom-right-radius: 0.5rem !important;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav {\\n  padding: 0 16px;\\n  border-color: var(--surface-100);\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li {\\n  position: relative;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  right: -1px;\\n  top: 0;\\n  bottom: 0;\\n  margin: auto;\\n  background: var(--surface-50);\\n  width: 1px;\\n  height: 20px;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link {\\n  border: none;\\n  padding: 0;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link {\\n  padding: 8px 14px;\\n  min-height: 40px;\\n  color: var(--gray-600);\\n  gap: 0 6px;\\n  border-radius: 10px 10px 0 0;\\n  cursor: pointer;\\n  font-weight: 500;\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link:hover {\\n  color: var(--primary-color);\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav li .p-tabview-nav-link .tab-link.active-tab {\\n  background: #f6f7f9;\\n  border: 2px solid var(--surface-100);\\n  border-bottom: none;\\n  font-weight: 600;\\n  color: var(--gray-800);\\n}\\n  .details-tabs-list .p-tabview-nav-content .p-tabview-nav .p-tabview-ink-bar {\\n  display: none !important;\\n}\\n  .details-tabs-list .p-tabview-panels {\\n  display: none;\\n}\\n  .details-tabs-list .p-tabview-nav-btn.p-link {\\n  background: var(--surface-0);\\n}\\n  .details-tabs-result {\\n  min-height: calc(100vh - 192px);\\n}\\n  .layout-sidebar .layout-menu li:nth-child(2) .layout-menuitem-root-text {\\n  margin: 12px 0;\\n  padding: 12px 0;\\n  border-top: 1px solid var(--surface-c);\\n  font-size: 14px;\\n  font-weight: 600;\\n}\\n  .card-heading h4 {\\n  margin-left: 10px !important;\\n  min-height: 30px;\\n  align-items: center;\\n}\\n  .sidebar-hide {\\n  display: none;\\n}\\n  .arrow-btn {\\n  top: 29px;\\n  left: 0;\\n  z-index: 99;\\n}\\n  .arrow-round {\\n  transform: rotate(180deg);\\n}\\n  .layout-sidebar .sidebar-header .app-logo .arrow-icon {\\n  transform: rotateY(180deg);\\n  position: absolute;\\n  right: 11px;\\n  top: 14px;\\n  color: var(--primary-color);\\n}\\n  .layout-container.layout-reveal.layout-sidebar-active .layout-sidebar .sidebar-header .app-logo .arrow-icon {\\n  display: none !important;\\n}\\n  .p-dropdown-label {\\n  display: flex;\\n  align-items: center;\\n}\\n  .filter-sec p-dropdown .p-dropdown-label,   .filter-sec p-dropdown .p-dropdown-trigger {\\n  color: var(--primary-700);\\n}\\n  .scrollable-table table thead th {\\n  min-width: 150px;\\n  white-space: nowrap;\\n}\\n  .scrollable-table table thead th.table-checkbox {\\n  min-width: 32px;\\n}\\n  .scrollable-table table tbody tr:nth-child(odd) td:first-child {\\n  background: #f2f2f5;\\n}\\n  .scrollable-table table tbody tr:nth-child(odd) td:nth-child(2) {\\n  background: #f2f2f5;\\n}\\n  .all-page-details {\\n  width: calc(100% - 28rem);\\n}\\n  p-paginator .p-paginator button {\\n  width: 3rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["StoreComponent", "constructor", "primengConfig", "renderer", "layoutService", "ngOnInit", "href", "link", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "document", "head", "ripple", "config", "menuMode", "colorScheme", "theme", "scale", "set", "ngOnDestroy", "getElementById", "remove", "i0", "ɵɵdirectiveInject", "i1", "PrimeNGConfig", "Renderer2", "i2", "LayoutService", "selectors", "decls", "vars", "template", "StoreComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\store.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\store.component.html"], "sourcesContent": ["import { Component, Renderer2 } from '@angular/core';\r\nimport { PrimeNGConfig } from 'primeng/api';\r\nimport { AppConfig, LayoutService } from './layout/service/app.layout.service';\r\n\r\n@Component({\r\n  selector: 'app-store',\r\n  templateUrl: './store.component.html',\r\n  styleUrl: './store.component.scss',\r\n})\r\nexport class StoreComponent {\r\n  constructor(\r\n    private primengConfig: PrimeNGConfig,\r\n    private renderer: Renderer2,\r\n    private layoutService: LayoutService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Inject theme \r\n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\r\n    const link = this.renderer.createElement('link');\r\n    this.renderer.setAttribute(link, 'id', 'theme-link');\r\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\r\n    this.renderer.setAttribute(link, 'type', 'text/css');\r\n    this.renderer.setAttribute(link, 'href', href);\r\n\r\n    // Append the link tag to the head of the document\r\n    this.renderer.appendChild(document.head, link);\r\n\r\n    this.primengConfig.ripple = true; //enables core ripple functionality\r\n    //optional configuration with the default configuration\r\n    const config: AppConfig = {\r\n      ripple: false, //toggles ripple on and off\r\n      menuMode: 'reveal', //layout mode of the menu, valid values are \"static\", \"overlay\", \"slim\", \"horizontal\", \"drawer\" and \"reveal\"\r\n      colorScheme: 'light', //color scheme of the template, valid values are \"light\" and \"dark\"\r\n      theme: 'snjya', //default component theme for PrimeNG\r\n      scale: 14, //size of the body font size to scale the whole application\r\n    };\r\n    this.layoutService.config.set(config);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Find and remove the link tag when the component is destroyed\r\n    const link = document.getElementById('theme-link');\r\n    if (link) {\r\n      link.remove();\r\n    }\r\n  }\r\n}\r\n", "<router-outlet></router-outlet>"], "mappings": ";;;;AASA,OAAM,MAAOA,cAAc;EACzBC,YACUC,aAA4B,EAC5BC,QAAmB,EACnBC,aAA4B;IAF5B,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;EACpB;EAEHC,QAAQA,CAAA;IACN;IACA,MAAMC,IAAI,GAAG,wDAAwD;IACrE,MAAMC,IAAI,GAAG,IAAI,CAACJ,QAAQ,CAACK,aAAa,CAAC,MAAM,CAAC;IAChD,IAAI,CAACL,QAAQ,CAACM,YAAY,CAACF,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC;IACpD,IAAI,CAACJ,QAAQ,CAACM,YAAY,CAACF,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC;IACrD,IAAI,CAACJ,QAAQ,CAACM,YAAY,CAACF,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC;IACpD,IAAI,CAACJ,QAAQ,CAACM,YAAY,CAACF,IAAI,EAAE,MAAM,EAAED,IAAI,CAAC;IAE9C;IACA,IAAI,CAACH,QAAQ,CAACO,WAAW,CAACC,QAAQ,CAACC,IAAI,EAAEL,IAAI,CAAC;IAE9C,IAAI,CAACL,aAAa,CAACW,MAAM,GAAG,IAAI,CAAC,CAAC;IAClC;IACA,MAAMC,MAAM,GAAc;MACxBD,MAAM,EAAE,KAAK;MAAE;MACfE,QAAQ,EAAE,QAAQ;MAAE;MACpBC,WAAW,EAAE,OAAO;MAAE;MACtBC,KAAK,EAAE,OAAO;MAAE;MAChBC,KAAK,EAAE,EAAE,CAAE;KACZ;IACD,IAAI,CAACd,aAAa,CAACU,MAAM,CAACK,GAAG,CAACL,MAAM,CAAC;EACvC;EAEAM,WAAWA,CAAA;IACT;IACA,MAAMb,IAAI,GAAGI,QAAQ,CAACU,cAAc,CAAC,YAAY,CAAC;IAClD,IAAId,IAAI,EAAE;MACRA,IAAI,CAACe,MAAM,EAAE;IACf;EACF;;;uBArCWtB,cAAc,EAAAuB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,SAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAd7B,cAAc;MAAA8B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT3BZ,EAAA,CAAAc,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}