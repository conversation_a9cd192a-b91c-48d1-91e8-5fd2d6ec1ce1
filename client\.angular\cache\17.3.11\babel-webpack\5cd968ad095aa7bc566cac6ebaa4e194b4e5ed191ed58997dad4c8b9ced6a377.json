{"ast": null, "code": "import { map, of } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let SettingsService = /*#__PURE__*/(() => {\n  class SettingsService {\n    constructor(http) {\n      this.http = http;\n    }\n    getCurrencies() {\n      return ['USD'];\n    }\n    getCountries() {\n      return ['USA'];\n    }\n    getSettings() {\n      if (this.settings) {\n        return of(this.settings);\n      }\n      return this.http.get(`${CMS_APIContstant.SETTINGS}`, {}).pipe(map(res => {\n        this.settings = res.data;\n        return res.data;\n      }));\n    }\n    saveSettings(data) {\n      return this.http.post(`${CMS_APIContstant.SETTINGS}`, data).pipe(map(res => {\n        this.settings = res.data;\n        return res.data;\n      }));\n    }\n    static {\n      this.ɵfac = function SettingsService_Factory(t) {\n        return new (t || SettingsService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SettingsService,\n        factory: SettingsService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SettingsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}