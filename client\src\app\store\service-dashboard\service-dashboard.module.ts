import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ServiceDashboardRoutingModule } from './service-dashboard-routing.module';
import { ServiceDashboardComponent } from './service-dashboard.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { MultiSelectModule } from 'primeng/multiselect';


@NgModule({
  declarations: [
    ServiceDashboardComponent
  ],
  imports: [
    CommonModule,
    ServiceDashboardRoutingModule,
    FormsModule,
    TableModule,
    ReactiveFormsModule,
    ButtonModule,
    DropdownModule,
    TabViewModule,
    AutoCompleteModule,
    BreadcrumbModule,
    CalendarModule,
    MultiSelectModule
  ]
})
export class ServiceDashboardModule { }
