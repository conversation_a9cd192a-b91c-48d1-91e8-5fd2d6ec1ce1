{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let CompetitorsService = /*#__PURE__*/(() => {\n  class CompetitorsService {\n    constructor(http) {\n      this.http = http;\n    }\n    getCompetitors(page, pageSize, sortField, sortOrder, searchTerm, filter) {\n      let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n      if (sortField && sortOrder !== undefined) {\n        const order = sortOrder === 1 ? 'asc' : 'desc';\n        params = params.set('sort', `${sortField}:${order}`);\n      } else {\n        // Default sort by updatedAt descending\n        params = params.set('sort', 'updatedAt:desc');\n      }\n      if (searchTerm) {\n        params = params.set('filters[$or][0][name][$containsi]', searchTerm);\n      }\n      return this.http.get(`${CMS_APIContstant.CRM_COMPETITORS}`, {\n        params\n      });\n    }\n    static {\n      this.ɵfac = function CompetitorsService_Factory(t) {\n        return new (t || CompetitorsService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: CompetitorsService,\n        factory: CompetitorsService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return CompetitorsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}