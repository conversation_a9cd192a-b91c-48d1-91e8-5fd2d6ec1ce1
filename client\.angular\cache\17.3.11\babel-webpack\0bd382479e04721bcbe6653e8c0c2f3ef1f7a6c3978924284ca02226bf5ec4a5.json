{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../prospects.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/tabview\";\nimport * as i9 from \"primeng/breadcrumb\";\nimport * as i10 from \"primeng/toast\";\nimport * as i11 from \"primeng/confirmdialog\";\nimport * as i12 from \"../../../shared/initials.pipe\";\nfunction ProspectsDetailsComponent_p_tabPanel_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tab_r1.label, \" \");\n  }\n}\nfunction ProspectsDetailsComponent_p_tabPanel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 38);\n    i0.ɵɵtemplate(1, ProspectsDetailsComponent_p_tabPanel_9_ng_template_1_Template, 2, 2, \"ng-template\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport let ProspectsDetailsComponent = /*#__PURE__*/(() => {\n  class ProspectsDetailsComponent {\n    constructor(route, router, messageservice, formBuilder, confirmationservice, prospectsservice) {\n      this.route = route;\n      this.router = router;\n      this.messageservice = messageservice;\n      this.formBuilder = formBuilder;\n      this.confirmationservice = confirmationservice;\n      this.prospectsservice = prospectsservice;\n      this.unsubscribe$ = new Subject();\n      this.prospectDetails = null;\n      this.sidebarDetails = null;\n      this.NoteDetails = null;\n      this.customerData = null;\n      this.items = [];\n      this.activeItem = null;\n      this.breadcrumbitems = [];\n      this.id = '';\n      this.partner_role = '';\n      this.bp_status = '';\n      this.bp_doc_id = '';\n      this.Actions = [];\n      this.activeIndex = 0;\n      this.isSidebarHidden = false;\n      this.submitted = false;\n      this.saving = false;\n      this.GlobalNoteForm = this.formBuilder.group({\n        note: ['']\n      });\n    }\n    ngOnInit() {\n      this.id = this.route.snapshot.paramMap.get('id') || '';\n      this.prospectsservice.getGlobalNote(this.id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          this.NoteDetails = response?.data[0] || [];\n          this.GlobalNoteForm.patchValue({\n            note: this.stripHtml(response?.data[0]?.note)\n          });\n        },\n        error: error => {\n          console.error('Error fetching global note:', error);\n        }\n      });\n      this.home = {\n        icon: 'pi pi-home text-lg',\n        routerLink: ['/']\n      };\n      this.makeMenuItems(this.id);\n      if (this.items.length > 0) {\n        this.activeItem = this.items[0];\n      }\n      this.setActiveTabFromURL();\n      this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n        const prospectId = params.get('id');\n        if (prospectId) {\n          this.loadProspectData(prospectId);\n        }\n      });\n      // Listen for route changes to keep active tab in sync\n      this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n        this.setActiveTabFromURL();\n      });\n      this.prospectsservice.prospect.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        const partner_role = response?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n        this.partner_role = partner_role?.business_partner?.bp_full_name || null;\n        this.prospectDetails = response || null;\n        this.sidebarDetails = this.formatSidebarDetails(response?.addresses || []);\n      });\n    }\n    makeMenuItems(id) {\n      this.items = [{\n        label: 'Overview',\n        routerLink: `/store/prospects/${id}/overview`\n      }, {\n        label: 'Contacts',\n        routerLink: `/store/prospects/${id}/contacts`\n      }, {\n        label: 'Sales Team',\n        routerLink: `/store/prospects/${id}/sales-team`\n      },\n      // {\n      //   label: 'AI Insights',\n      //   routerLink: `/store/prospects/${id}/ai-insights`,\n      // },\n      // {\n      //   label: 'Organization Data',\n      //   routerLink: `/store/prospects/${id}/organization-data`,\n      // },\n      {\n        label: 'Attachments',\n        routerLink: `/store/prospects/${id}/attachments`\n      }, {\n        label: 'Notes',\n        routerLink: `/store/prospects/${id}/notes`\n      }, {\n        label: 'Activities',\n        routerLink: `/store/prospects/${id}/activities`\n      }];\n    }\n    setActiveTabFromURL() {\n      const fullPath = this.router.url;\n      const currentTab = fullPath.split('/').pop() || 'overview';\n      if (this.items.length === 0) return;\n      const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n      this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n      this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n      this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n    }\n    updateBreadcrumb(activeTab) {\n      this.breadcrumbitems = [{\n        label: 'Prospects',\n        routerLink: ['/store/prospects']\n      }, {\n        label: activeTab,\n        routerLink: []\n      }];\n    }\n    onTabChange(event) {\n      if (this.items.length === 0) return;\n      this.activeIndex = event.index;\n      const selectedTab = this.items[this.activeIndex];\n      if (selectedTab?.routerLink) {\n        this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n      }\n    }\n    loadProspectData(prospectId) {\n      this.prospectsservice.getProspectByID(prospectId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          if (response) {\n            this.bp_doc_id = response?.data?.[0]?.documentId;\n            this.bp_status = response?.data?.[0]?.is_marked_for_archiving;\n            this.Actions = [{\n              name: 'Convert to Customer',\n              code: 'CI'\n            }, {\n              name: this.bp_status ? 'Set As Active' : 'Set As Obsolete',\n              code: this.bp_status ? 'SAA' : 'SAO'\n            }];\n          }\n        },\n        error: error => {\n          console.error('Error fetching data:', error);\n        }\n      });\n    }\n    formatSidebarDetails(addresses) {\n      return addresses.filter(address => address?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT')).map(address => ({\n        ...address,\n        address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n        email_address: address?.emails?.[0]?.email_address || '-',\n        phone_number: address?.phone_numbers?.[0]?.phone_number || '-',\n        website_url: address?.home_page_urls?.[0]?.website_url || '-'\n      }));\n    }\n    onActionChange(event) {\n      const actionCode = event.value?.code;\n      const actionsMap = {\n        CI: () => this.ConvertToCustomer(event),\n        SAA: () => this.UpdateStatus(this.bp_doc_id, 'false'),\n        SAO: () => this.UpdateStatus(this.bp_doc_id, 'true')\n      };\n      const action = actionsMap[actionCode];\n      if (action) {\n        this.confirmationservice.confirm({\n          message: 'Are you sure you want to proceed with this action?',\n          header: 'Confirm',\n          icon: 'pi pi-exclamation-triangle',\n          accept: action\n        });\n      }\n    }\n    ConvertToCustomer(event) {\n      this.customerData = this.createBusinessPartnerObject(this.prospectDetails);\n      this.prospectsservice.bpCreation(this.customerData).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: response => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Convert to Customer Successfully!'\n          });\n          this.prospectsservice.getProspectByID(this.id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n        },\n        error: error => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: error?.error?.message || 'Error while processing your request.'\n          });\n          console.error('Error fetching data:', error);\n        }\n      });\n    }\n    createBusinessPartnerObject(data) {\n      const formatAddress = address => ({\n        Country: address.country_code || 'US',\n        Region: address.region || '',\n        HouseNumber: address.house_number || '',\n        AdditionalStreetPrefixName: address.additional_street_prefix_name || '',\n        AdditionalStreetSuffixName: address.additional_street_suffix_name || '',\n        StreetName: address.street_name || '',\n        PostalCode: address.postal_code || '',\n        CityName: address.city_name || '',\n        PrfrdCommMediumType: address.prfrd_comm_medium_type || '',\n        Language: 'EN',\n        to_AddressUsage: address.address_usages?.map(usage => ({\n          AddressUsage: usage.address_usage || 'XXDEFAULT'\n        })) || [],\n        to_EmailAddress: address.emails?.map(email => ({\n          EmailAddress: email.email_address || ''\n        })) || [],\n        to_PhoneNumber: address.phone_numbers?.filter(ph => ph.phone_number_type === '1').map(ph => ({\n          PhoneNumber: ph.phone_number || ''\n        })) || [],\n        to_URLAddress: address.home_page_urls?.map(url => ({\n          WebsiteURL: url.website_url || ''\n        })) || [],\n        to_FaxNumber: address.fax_numbers?.map(fax => ({\n          FaxNumber: fax.fax_number || ''\n        })) || [],\n        to_MobilePhoneNumber: address.phone_numbers?.filter(ph => ph.phone_number_type === '3').map(ph => ({\n          PhoneNumber: ph.phone_number || ''\n        })) || []\n      });\n      const mainPartner = {\n        BusinessPartner: data.bp_id || '',\n        Name1: data.bp_full_name || '',\n        Name2: '',\n        Name3: '',\n        CategoryCode: '2',\n        IsMarkedForArchiving: data?.is_marked_for_archiving || false,\n        to_BusinessPartnerAddress: data.addresses?.map(formatAddress) || [],\n        to_BusinessPartnerRole: [{\n          BusinessPartnerRole: 'FLCU01'\n        }, {\n          BusinessPartnerRole: 'FLCU00'\n        }],\n        to_Customer: {\n          Customer: data.customer?.customer_id || '',\n          DeletedIndicator: false,\n          DeliveryIsBlockedForCustomer: 'ZN',\n          // ZN means new customer code\n          to_CustomerSalesArea: (data.customer?.partner_functions || []).reduce((acc, pf) => {\n            const key = `${pf.sales_organization}-${pf.distribution_channel}-${pf.division}`;\n            if (!acc[key]) {\n              acc[key] = {\n                SalesOrganization: pf.sales_organization || '',\n                DistributionChannel: pf.distribution_channel || '',\n                Division: pf.division || '',\n                Currency: 'USD',\n                to_PartnerFunction: []\n              };\n              // Add partner functions only once per SalesArea\n              acc[key].to_PartnerFunction.push(...['AG', 'RE', 'RG', 'WE'].map(func => ({\n                PartnerFunction: func,\n                BPCustomerNumber: data.customer.customer_id || ''\n              })));\n              // Sales Team Data for Prospect to customer\n              acc[key].to_PartnerFunction.push(...data.customer.partner_functions.map(partner => ({\n                PartnerFunction: partner.partner_function || '',\n                BPCustomerNumber: partner?.bp_customer_number || ''\n              })));\n              // data.contact_companies?.forEach((company: any) => {\n              //   acc[key].to_PartnerFunction.push({\n              //     PartnerFunction: 'CP',\n              //     BPCustomerNumber: company?.bp_person_id || '',\n              //   });\n              // });\n            }\n            return acc;\n          }, {})\n        }\n      };\n      mainPartner.to_Customer.to_CustomerSalesArea = Object.values(mainPartner.to_Customer.to_CustomerSalesArea);\n      const contacts = (data.contact_companies || []).map(company => ({\n        BusinessPartner: company.bp_person_id || '',\n        Name1: company.business_partner_person?.first_name || '',\n        Name2: company.business_partner_person?.middle_name || '',\n        Name3: company.business_partner_person?.last_name || '',\n        CategoryCode: '1',\n        IsMarkedForArchiving: company?.business_partner_person?.is_marked_for_archiving || false,\n        to_BusinessPartnerAddress: company.business_partner_person?.addresses?.map(formatAddress) || [],\n        to_BusinessPartnerRole: [{\n          BusinessPartnerRole: 'BUP001'\n        }]\n      }));\n      return {\n        BP: [mainPartner, ...contacts],\n        BP_Relationship: (data?.contact_companies || []).map(company => ({\n          BusinessPartnerID: company?.bp_company_id || '',\n          RelationshipBusinessPartnerID: company?.bp_person_id || '',\n          ValidityStartDate: company?.validity_start_date || new Date(),\n          ValidityEndDate: company?.validity_end_date || new Date('9999-12-29T23:59:59.000Z'),\n          RoleCode: 'BUR001',\n          ContactPerson: {\n            ContactPersonVipType: company?.person_func_and_dept?.contact_person_vip_type ? '1' : '',\n            ContactPersonDepartment: company?.person_func_and_dept?.contact_person_department || '',\n            ContactPersonFunction: company?.person_func_and_dept?.contact_person_function || ''\n          }\n        }))\n      };\n    }\n    onNoteSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        if (_this.GlobalNoteForm.invalid) {\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.GlobalNoteForm.value\n        };\n        const data = {\n          note: value?.note,\n          bp_id: _this?.id,\n          ...(!_this.NoteDetails.documentId ? {\n            is_global_note: true\n          } : {})\n        };\n        const apiCall = _this.NoteDetails && _this.NoteDetails.documentId ? _this.prospectsservice.updateNote(_this.NoteDetails.documentId, data) // Update if exists\n        : _this.prospectsservice.createNote(data); // Create if not exists\n        apiCall.pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          next: () => {\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Prospect Note Updated SuccessFully!'\n            });\n            _this.prospectsservice.getProspectByID(_this.id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n          },\n          error: () => {\n            _this.saving = false;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      })();\n    }\n    UpdateStatus(docid, status) {\n      const data = {\n        is_marked_for_archiving: status\n      };\n      this.prospectsservice.updateBpStatus(docid, data).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: () => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Action Updated Successfully!'\n          });\n          setTimeout(() => {\n            window.location.reload();\n          }, 1000);\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n    stripHtml(html) {\n      const temp = document.createElement('div');\n      temp.innerHTML = html;\n      return temp.textContent || temp.innerText || '';\n    }\n    goToBack() {\n      this.router.navigate(['/store/prospects']);\n    }\n    toggleSidebar() {\n      this.isSidebarHidden = !this.isSidebarHidden;\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function ProspectsDetailsComponent_Factory(t) {\n        return new (t || ProspectsDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i2.ConfirmationService), i0.ɵɵdirectiveInject(i4.ProspectsService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProspectsDetailsComponent,\n        selectors: [[\"app-prospects-details\"]],\n        decls: 84,\n        vars: 29,\n        consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"onChange\", \"options\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"activeIndexChange\", \"onChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\", \"flex-nowrap\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\", \"font-semibold\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [3, \"formGroup\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\", \"mt-5\", \"p-3\"], [1, \"mb-3\", \"font-semibold\", \"text-color\"], [1, \"flex\", \"flex-column\", \"gap-3\"], [\"formControlName\", \"note\", \"rows\", \"4\", \"placeholder\", \"Enter your note here...\", 1, \"w-full\", \"h-8rem\", \"p-2\", \"border-1\", \"border-round\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Save Note\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\", \"all-page-details\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [1, \"confirm-popup\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n        template: function ProspectsDetailsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"p-toast\", 0);\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"p-breadcrumb\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p-dropdown\", 5);\n            i0.ɵɵlistener(\"onChange\", function ProspectsDetailsComponent_Template_p_dropdown_onChange_5_listener($event) {\n              return ctx.onActionChange($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"p-tabView\", 8);\n            i0.ɵɵtwoWayListener(\"activeIndexChange\", function ProspectsDetailsComponent_Template_p_tabView_activeIndexChange_8_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"onChange\", function ProspectsDetailsComponent_Template_p_tabView_onChange_8_listener($event) {\n              return ctx.onTabChange($event);\n            });\n            i0.ɵɵtemplate(9, ProspectsDetailsComponent_p_tabPanel_9_Template, 2, 1, \"p-tabPanel\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15)(16, \"div\", 16)(17, \"h5\", 17);\n            i0.ɵɵtext(18);\n            i0.ɵɵpipe(19, \"initials\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 18)(21, \"h5\", 19);\n            i0.ɵɵtext(22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"ul\", 20)(24, \"li\", 21)(25, \"span\", 22);\n            i0.ɵɵtext(26, \"CRM ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(27);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"li\", 21)(29, \"span\", 22);\n            i0.ɵɵtext(30, \"Account Owner \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(31);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"li\", 21)(33, \"span\", 22);\n            i0.ɵɵtext(34, \"Main Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(35);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(36, \"div\", 23)(37, \"ul\", 24)(38, \"li\", 25)(39, \"span\", 26)(40, \"i\", 27);\n            i0.ɵɵtext(41, \"location_on\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(42, \" Address\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"span\", 28);\n            i0.ɵɵtext(44);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(45, \"li\", 25)(46, \"span\", 26)(47, \"i\", 27);\n            i0.ɵɵtext(48, \"phone_in_talk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(49, \" Phone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"span\", 28);\n            i0.ɵɵtext(51);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(52, \"li\", 25)(53, \"span\", 26)(54, \"i\", 27);\n            i0.ɵɵtext(55, \"phone_in_talk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(56, \" Main Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"span\", 28);\n            i0.ɵɵtext(58);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(59, \"li\", 25)(60, \"span\", 26)(61, \"i\", 27);\n            i0.ɵɵtext(62, \"mail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(63, \" Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"span\", 28);\n            i0.ɵɵtext(65);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(66, \"li\", 25)(67, \"span\", 26)(68, \"i\", 27);\n            i0.ɵɵtext(69, \"language\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(70, \" Website\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"span\", 28);\n            i0.ɵɵtext(72);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(73, \"form\", 29)(74, \"div\", 30)(75, \"h4\", 31);\n            i0.ɵɵtext(76, \"Global Note\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"div\", 32);\n            i0.ɵɵelement(78, \"textarea\", 33);\n            i0.ɵɵelementStart(79, \"button\", 34);\n            i0.ɵɵlistener(\"click\", function ProspectsDetailsComponent_Template_button_click_79_listener() {\n              return ctx.onNoteSubmit();\n            });\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(80, \"div\", 35)(81, \"p-button\", 36);\n            i0.ɵɵlistener(\"click\", function ProspectsDetailsComponent_Template_p_button_click_81_listener() {\n              return ctx.toggleSidebar();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(82, \"router-outlet\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelement(83, \"p-confirmDialog\", 37);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"life\", 3000);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"options\", ctx.Actions)(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"scrollable\", true);\n            i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.items);\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 27, ctx.prospectDetails == null ? null : ctx.prospectDetails.bp_full_name), \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate((ctx.prospectDetails == null ? null : ctx.prospectDetails.bp_full_name) || \"-\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" : \", (ctx.prospectDetails == null ? null : ctx.prospectDetails.bp_id) || \"-\", \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" : \", ctx.partner_role || \"-\", \"\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" : \", ((ctx.prospectDetails == null ? null : ctx.prospectDetails.contact_companies == null ? null : ctx.prospectDetails.contact_companies[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.first_name) || \"-\") + \" \" + ((ctx.prospectDetails == null ? null : ctx.prospectDetails.contact_companies == null ? null : ctx.prospectDetails.contact_companies[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.last_name) || \"-\"), \" \");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.prospectDetails == null ? null : ctx.prospectDetails.contact_companies == null ? null : ctx.prospectDetails.contact_companies[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.addresses == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.addresses[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.addresses[0].phone_numbers[0].phone_number) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.GlobalNoteForm);\n            i0.ɵɵadvance(8);\n            i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n            i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n          }\n        },\n        dependencies: [i5.NgForOf, i1.RouterOutlet, i1.RouterLink, i1.RouterLinkActive, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i2.PrimeTemplate, i3.FormGroupDirective, i3.FormControlName, i6.ButtonDirective, i6.Button, i7.Dropdown, i8.TabView, i8.TabPanel, i9.Breadcrumb, i10.Toast, i11.ConfirmDialog, i12.InitialsPipe],\n        styles: [\".prospect-popup .p-dialog{margin-right:50px}  .prospect-popup .p-dialog .p-dialog-header{background:var(--surface-0);border-bottom:1px solid var(--surface-100)}  .prospect-popup .p-dialog .p-dialog-header h4{margin:0}  .prospect-popup .p-dialog .p-dialog-content{background:var(--surface-0);padding:1.714rem}\"]\n      });\n    }\n  }\n  return ProspectsDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}