{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let CustomerService = /*#__PURE__*/(() => {\n  class CustomerService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = 'https://displaymaxsnjyadev.cfapps.us10-001.hana.ondemand.com/api';\n      this.headers = new HttpHeaders({\n        Authorization: 'bearer 81fee757cde39bf0217839d6dbbfea69cc1fc65a899a51b0bbf86741b9d28ab48b6aaf190edcb03f51f95ae15aeb5147b517301ab736ccd46e9437e646051f3e125446640c210ed0bc8d7fc64ca0a39badd3c9aca86fb24ac1c8bdacaaa0641c38e5d835970bf33971327fbc50602d6753ce1dcd424e6a44b55b882cc8c34695' // Replace with your actual token\n      });\n    }\n    getCustomers(page, pageSize, sortField, sortOrder, searchTerm) {\n      let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n      if (sortField && sortOrder !== undefined) {\n        const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n        params = params.set('sort', `${sortField}:${order}`);\n      }\n      if (searchTerm) {\n        params = params.set('filters[$or][0][bp_id][$containsi]', searchTerm).set('filters[$or][1][bp_full_name][$containsi]', searchTerm).set('filters[$or][2][email][$containsi]', searchTerm).set('filters[$or][3][phone][$containsi]', searchTerm);\n      }\n      return this.http.get(`${this.apiUrl}/business-partners`, {\n        headers: this.headers,\n        params\n      });\n    }\n    getCustomerByID(custid) {\n      return this.http.get(`${this.apiUrl}/customers?filters[bp_id][$eq]=${custid}&populate=*`, {\n        headers: this.headers\n      });\n    }\n    getCustomerByIDName(customerdata) {\n      let params = new HttpParams();\n      if (customerdata) {\n        params = params.set('filters[$or][0][customer_id][$containsi]', customerdata).set('filters[$or][1][customer_name][$containsi]', customerdata);\n      }\n      return this.http.get(`${this.apiUrl}/customers`, {\n        headers: this.headers,\n        params\n      });\n    }\n    static {\n      this.ɵfac = function CustomerService_Factory(t) {\n        return new (t || CustomerService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: CustomerService,\n        factory: CustomerService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return CustomerService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}