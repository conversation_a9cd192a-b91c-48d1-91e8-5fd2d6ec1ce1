{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { CompetitorsRoutingModule } from './competitors-routing.module';\nimport { CompetitorsComponent } from './competitors.component';\nimport { TabViewModule } from 'primeng/tabview';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { InputTextModule } from 'primeng/inputtext';\nimport * as i0 from \"@angular/core\";\nexport class CompetitorsModule {\n  static {\n    this.ɵfac = function CompetitorsModule_Factory(t) {\n      return new (t || CompetitorsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CompetitorsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, CompetitorsRoutingModule, BreadcrumbModule, TableModule, FormsModule, ReactiveFormsModule, ButtonModule, CheckboxModule, TabViewModule, InputTextModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CompetitorsModule, {\n    declarations: [CompetitorsComponent],\n    imports: [CommonModule, CompetitorsRoutingModule, BreadcrumbModule, TableModule, FormsModule, ReactiveFormsModule, ButtonModule, CheckboxModule, TabViewModule, InputTextModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "BreadcrumbModule", "FormsModule", "ReactiveFormsModule", "TableModule", "ButtonModule", "CompetitorsRoutingModule", "CompetitorsComponent", "TabViewModule", "CheckboxModule", "InputTextModule", "CompetitorsModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\competitors\\competitors.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { FormsModule,ReactiveFormsModule } from '@angular/forms';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CompetitorsRoutingModule } from './competitors-routing.module';\r\nimport { CompetitorsComponent } from './competitors.component';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    CompetitorsComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n        CompetitorsRoutingModule,\r\n        BreadcrumbModule,\r\n        TableModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        ButtonModule,\r\n        CheckboxModule,\r\n        TabViewModule,\r\n        InputTextModule,\r\n  ]\r\n})\r\nexport class CompetitorsModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,WAAW,EAACC,mBAAmB,QAAQ,gBAAgB;AAChE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;;AAmBnD,OAAM,MAAOC,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAZ1BX,YAAY,EACRM,wBAAwB,EACxBL,gBAAgB,EAChBG,WAAW,EACXF,WAAW,EACXC,mBAAmB,EACnBE,YAAY,EACZI,cAAc,EACdD,aAAa,EACbE,eAAe;IAAA;EAAA;;;2EAGVC,iBAAiB;IAAAC,YAAA,GAf1BL,oBAAoB;IAAAM,OAAA,GAGpBb,YAAY,EACRM,wBAAwB,EACxBL,gBAAgB,EAChBG,WAAW,EACXF,WAAW,EACXC,mBAAmB,EACnBE,YAAY,EACZI,cAAc,EACdD,aAAa,EACbE,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}