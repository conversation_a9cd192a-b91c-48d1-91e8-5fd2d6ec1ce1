<div class="col-12 all-overview-body m-0 p-0 border-round-lg">
    <div
        class="filter-sec my-4 flex align-items-start justify-content-between flex-column gap-2 md:flex-row md:align-items-center">
        <div class="breadcrumb-sec">
            <p-breadcrumb [model]="breadcrumbitems" [home]="home" [styleClass]="'py-2 px-0 border-none'" />
        </div>
        <div class="flex align-items-start gap-3 md:flex-row md:align-items-center flex-wrap w-full md:w-auto">
            <!-- Search Box -->
            <div class="h-search-box w-full sm:w-24rem">
                <span class="p-input-icon-right w-full md:w-24rem">
                    <input type="text" #filter [(ngModel)]="globalSearchTerm" (input)="onGlobalFilter(dt1, $event)"
                        placeholder="Search Organization"
                        class="p-inputtext p-component p-element w-full sm:w-24rem h-3rem px-3 border-round-3xl border-1 surface-border">
                    <i class="pi pi-search" style="right: 16px;"></i>
                </span>
            </div>

            <!-- <button type="button" (click)="create()"
                class="h-3rem p-element p-ripple p-button p-component border-round-3xl w-8rem justify-content-center gap-2 font-semibold border-none">
                <span class="material-symbols-rounded text-2xl">box_edit</span> Create
            </button> -->
            <p-dropdown [options]="createOptions" placeholder="Create" optionLabel="label" (onChange)="create($event)"
                class="w-full sm:w-13rem"
                [styleClass]="'w-full sm:w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'" />

            <p-multiSelect [options]="OrgCols" [(ngModel)]="selectedOrgColumns" optionLabel="header"
                class="table-multiselect-dropdown"
                [styleClass]="'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'">
            </p-multiSelect>
        </div>
    </div>

    <div class="table-sec">
        <p-table #dt1 [value]="organization" dataKey="id" [rows]="14" (onLazyLoad)="loadOrganization($event)"
            styleClass="w-full" [paginator]="true" [loading]="loading" [paginator]="true" [lazy]="true"
            [totalRecords]="totalRecords" [scrollable]="true" [reorderableColumns]="true"
            (onColReorder)="onOrgColumnReorder($event)" [(selection)]="selectedOrganizations"
            (selectionChange)="updateCreateOptions()" responsiveLayout="scroll" class="scrollable-table">

            <ng-template pTemplate="header">
                <tr>
                    <th pFrozenColumn class="border-round-left-lg pl-3 w-2rem text-center table-checkbox">
                        <p-tableHeaderCheckbox />
                    </th>
                    <th pFrozenColumn (click)="customSort('name', organization, 'ORG')">
                        <div class="flex align-items-center cursor-pointer">
                            Name
                            <i *ngIf="sortFieldOrg === 'name'" class="ml-2 pi"
                                [ngClass]="sortOrderOrg === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                            <i *ngIf="sortFieldOrg !== 'name'" class="ml-2 pi pi-sort"></i>
                        </div>
                    </th>
                    <ng-container *ngFor="let col of selectedOrgColumns">
                        <th *ngIf="col.field !== 'sales_organisation_indicator' && col.field !== 'sales_indicator' && col.field !== 'reporting_line_indicator'; else nonSortableColumn"
                            [pSortableColumn]="col.field" pReorderableColumn
                            (click)="customSort(col.field,organization, 'ORG')">
                            <div class="flex align-items-center cursor-pointer">
                                {{ col.header }}
                                <i *ngIf="sortFieldOrg === col.field" class="ml-2 pi"
                                    [ngClass]="sortOrderOrg === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                                <i *ngIf="sortFieldOrg !== col.field" class="ml-2 pi pi-sort"></i>
                            </div>
                        </th>

                        <ng-template #nonSortableColumn>
                            <th pReorderableColumn>
                                <div class="flex align-items-center">
                                    {{ col.header }}
                                </div>
                            </th>
                        </ng-template>
                    </ng-container>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-organization>
                <tr class="cursor-pointer">
                    <td pFrozenColumn class="border-round-left-lg pl-3 w-2rem text-center">
                        <p-tableCheckbox [value]="organization" />
                    </td>

                    <td pFrozenColumn class="border-round-left-lg">
                        <div class="flex align-items-center gap-2">
                            <button *ngIf="organization?.child_organisational_units?.length > 0" pButton type="button"
                                class="p-button p-button-text p-button-rounded p-button-plain p-button-icon-only p-button-sm"
                                (click)="toggle(organization)">
                                <span class="pi" [ngClass]="{
      'pi-chevron-down': organization.expanded,
      'pi-chevron-right': !organization.expanded
    }" style="font-size: 0.75rem;"></span>
                            </button>


                            <a [routerLink]="'/store/organization/' + organization.organisational_unit_id"
                                class="text-orange-600 font-medium underline">
                                {{ organization.name || '-' }}
                            </a>
                        </div>
                    </td>


                    <ng-container *ngFor="let col of selectedOrgColumns">
                        <td>
                            <ng-container [ngSwitch]="col.field">
                                <ng-container *ngSwitchCase="'crm_org_unit_managers.business_partner.bp_full_name'">
                                    {{ organization?.crm_org_unit_managers?.[0]?.business_partner?.bp_full_name || '-'
                                    }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'parent_organisational_unit.name'">
                                    {{ organization?.parent_organisational_unit?.name || '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'organisational_unit_id'">
                                    {{ organization?.organisational_unit_id || '-'}}
                                </ng-container>

                                <ng-container *ngSwitchCase="'parent_organisational_unit_id'">
                                    {{ organization?.parent_organisational_unit_id || '-'}}
                                </ng-container>

                                <ng-container *ngSwitchCase="'sales_organisation_indicator'">
                                    <p-checkbox [binary]="true" [disabled]="true"
                                        [ngModel]="organization?.crm_org_unit_functions?.[0]?.sales_organisation_indicator"></p-checkbox>
                                </ng-container>
                                <ng-container *ngSwitchCase="'sales_indicator'">
                                    <p-checkbox [binary]="true" [disabled]="true"
                                        [ngModel]="organization?.crm_org_unit_functions?.[0]?.sales_indicator"></p-checkbox>
                                </ng-container>

                                <ng-container *ngSwitchCase="'reporting_line_indicator'">
                                    <p-checkbox [binary]="true" [disabled]="true"
                                        [ngModel]="organization?.crm_org_unit_functions?.[0]?.reporting_line_indicator"></p-checkbox>
                                </ng-container>

                            </ng-container>
                        </td>
                    </ng-container>
                </tr>
                <!-- ───── reusable row template ───── -->
                <ng-template #rowTpl let-node let-indent="indent">
                    <tr class="bg-surface-50 text-sm org-child-row">

                        <!-- checkbox -->
                        <td pFrozenColumn class="pl-3 w-2rem text-center">
                            <p-tableCheckbox [value]="node"></p-tableCheckbox>
                        </td>

                        <!-- name + expander -->
                        <td pFrozenColumn class="border-round-left-lg">
                            <div class="flex align-items-center gap-2 ml-5" [style.margin-left.px]="indent">
                                <button *ngIf="node.child_organisational_units?.length" pButton type="button"
                                    class="p-button p-button-text p-button-rounded p-button-plain p-button-icon-only p-button-sm"
                                    (click)="toggle(node)">
                                    <span class="pi" [ngClass]="{
                  'pi-chevron-down': node.expanded,
                  'pi-chevron-right': !node.expanded
                }" style="font-size:0.75rem;"></span>
                                </button>

                                <a [routerLink]="'/store/organization/' + node.organisational_unit_id"
                                    class="text-orange-600 font-medium underline">
                                    {{ node.name || '-' }}
                                </a>
                            </div>
                        </td>

                        <!-- dynamic columns (unchanged, but now use 'node') -->
                        <ng-container *ngFor="let col of selectedOrgColumns">
                            <td>
                                <ng-container [ngSwitch]="col.field">

                                    <ng-container *ngSwitchCase="'crm_org_unit_managers.business_partner.bp_full_name'">
                                        {{ node.crm_org_unit_managers?.[0]?.business_partner?.bp_full_name || '-' }}
                                    </ng-container>

                                    <ng-container *ngSwitchCase="'parent_organisational_unit.name'">
                                        {{ node.parent_organisational_unit?.name || '-' }}
                                    </ng-container>

                                    <ng-container *ngSwitchCase="'organisational_unit_id'">
                                        {{ node.organisational_unit_id || '-' }}
                                    </ng-container>

                                    <ng-container *ngSwitchCase="'parent_organisational_unit_id'">
                                        {{ node.parent_organisational_unit_id || '-' }}
                                    </ng-container>

                                    <ng-container *ngSwitchCase="'sales_organisation_indicator'">
                                        <p-checkbox [binary]="true" [disabled]="true"
                                            [ngModel]="node.crm_org_unit_functions?.[0]?.sales_organisation_indicator">
                                        </p-checkbox>
                                    </ng-container>

                                    <ng-container *ngSwitchCase="'sales_indicator'">
                                        <p-checkbox [binary]="true" [disabled]="true"
                                            [ngModel]="node.crm_org_unit_functions?.[0]?.sales_indicator">
                                        </p-checkbox>
                                    </ng-container>

                                    <ng-container *ngSwitchCase="'reporting_line_indicator'">
                                        <p-checkbox [binary]="true" [disabled]="true"
                                            [ngModel]="node.crm_org_unit_functions?.[0]?.reporting_line_indicator">
                                        </p-checkbox>
                                    </ng-container>

                                    <ng-container *ngSwitchDefault>
                                        {{ node[col.field] || '-' }}
                                    </ng-container>

                                </ng-container>
                            </td>
                        </ng-container>
                    </tr>

                    <!-- ───── recurse to children, grandchildren, … ───── -->
                    <ng-container *ngIf="node.expanded">
                        <ng-container *ngFor="let wrap of node.details">
                            <ng-container *ngFor="let child of wrap.data">
                                <ng-container
                                    *ngTemplateOutlet="rowTpl; context:{ $implicit: child, indent: indent + 16 }"></ng-container>
                            </ng-container>
                        </ng-container>
                    </ng-container>
                </ng-template>

                <!-- ───── render first‑level children of current "organization" ───── -->
                <ng-container *ngIf="organization.expanded">
                    <ng-container *ngFor="let wrap of organization.details">
                        <ng-container *ngFor="let child of wrap.data">
                            <ng-container
                                *ngTemplateOutlet="rowTpl; context:{ $implicit: child, indent: 12 }"></ng-container>
                        </ng-container>
                    </ng-container>
                </ng-container>


            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="10" class="border-round-left-lg">No organization found.</td>
                </tr>
            </ng-template>
            <ng-template pTemplate="loadingbody">
                <tr>
                    <td colspan="10" class="border-round-left-lg">Loading organization data. Please wait.</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>