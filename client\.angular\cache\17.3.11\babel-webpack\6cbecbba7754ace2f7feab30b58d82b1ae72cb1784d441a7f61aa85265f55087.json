{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, startWith, catchError, debounceTime } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../activities.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ng-select/ng-select\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/calendar\";\nimport * as i11 from \"primeng/inputtext\";\nimport * as i12 from \"primeng/dialog\";\nimport * as i13 from \"primeng/editor\";\nimport * as i14 from \"primeng/multiselect\";\nconst _c0 = () => ({\n  width: \"50rem\"\n});\nconst _c1 = () => ({\n  height: \"200px\"\n});\nconst _c2 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction TaskOverviewComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"label\", 29)(4, \"span\", 30);\n    i0.ɵɵtext(5, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 27)(10, \"div\", 28)(11, \"label\", 29)(12, \"span\", 30);\n    i0.ɵɵtext(13, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Subject \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 31);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 27)(18, \"div\", 28)(19, \"label\", 29)(20, \"span\", 30);\n    i0.ɵɵtext(21, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \"Transaction Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 31);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 27)(26, \"div\", 28)(27, \"label\", 29)(28, \"span\", 30);\n    i0.ɵɵtext(29, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Account \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 32)(32, \"a\", 33);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 27)(35, \"div\", 28)(36, \"label\", 29)(37, \"span\", 30);\n    i0.ɵɵtext(38, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(39, \" Contact \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 32)(41, \"a\", 33);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(43, \"div\", 27)(44, \"div\", 28)(45, \"label\", 29)(46, \"span\", 30);\n    i0.ɵɵtext(47, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(48, \" Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 31);\n    i0.ɵɵtext(50);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(51, \"div\", 27)(52, \"div\", 28)(53, \"label\", 29)(54, \"span\", 30);\n    i0.ɵɵtext(55, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(56, \" Disposition Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\", 31);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(59, \"div\", 27)(60, \"div\", 28)(61, \"label\", 29)(62, \"span\", 30);\n    i0.ɵɵtext(63, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(64, \" Reason \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"div\", 31);\n    i0.ɵɵtext(66);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(67, \"div\", 27)(68, \"div\", 28)(69, \"label\", 29)(70, \"span\", 30);\n    i0.ɵɵtext(71, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(72, \" Call Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"div\", 31);\n    i0.ɵɵtext(74);\n    i0.ɵɵpipe(75, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(76, \"div\", 27)(77, \"div\", 28)(78, \"label\", 29)(79, \"span\", 30);\n    i0.ɵɵtext(80, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(81, \" End Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"div\", 31);\n    i0.ɵɵtext(83);\n    i0.ɵɵpipe(84, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(85, \"div\", 27)(86, \"div\", 28)(87, \"label\", 29)(88, \"span\", 30);\n    i0.ɵɵtext(89, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(90, \" Owner \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"div\", 31);\n    i0.ɵɵtext(92);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(93, \"div\", 27)(94, \"div\", 28)(95, \"label\", 29)(96, \"span\", 30);\n    i0.ɵɵtext(97, \"branding_watermark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(98, \" Brand \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(99, \"div\", 31);\n    i0.ɵɵtext(100);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(101, \"div\", 27)(102, \"div\", 28)(103, \"label\", 29)(104, \"span\", 30);\n    i0.ɵɵtext(105, \"group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(106, \" Customer Group \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(107, \"div\", 31);\n    i0.ɵɵtext(108);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(109, \"div\", 27)(110, \"div\", 28)(111, \"label\", 29)(112, \"span\", 30);\n    i0.ɵɵtext(113, \"emoji_events\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(114, \" Ranking \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(115, \"div\", 31);\n    i0.ɵɵtext(116);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(117, \"div\", 27)(118, \"div\", 28)(119, \"label\", 29)(120, \"span\", 30);\n    i0.ɵɵtext(121, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(122, \" Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(123, \"div\", 31);\n    i0.ɵɵtext(124);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(125, \"div\", 27)(126, \"div\", 28)(127, \"label\", 29)(128, \"span\", 30);\n    i0.ɵɵtext(129, \"label\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(130, \" Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(131, \"div\", 31);\n    i0.ɵɵtext(132);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(133, \"div\", 27)(134, \"div\", 28)(135, \"label\", 29)(136, \"span\", 30);\n    i0.ɵɵtext(137, \"access_time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(138, \" Customer TimeZone \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(139, \"div\", 31);\n    i0.ɵɵtext(140);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.activity_id) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.subject) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"activityDocumentType\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.document_type) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"href\", \"/#/store/account/\" + (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner == null ? null : ctx_r0.overviewDetails.business_partner.documentId) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner == null ? null : ctx_r0.overviewDetails.business_partner.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"href\", \"/#/store/contacts/\" + (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_contact == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons[0] == null ? null : ctx_r0.overviewDetails.business_partner_contact.contact_persons[0].documentId) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_contact == null ? null : ctx_r0.overviewDetails.business_partner_contact.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"activityCategory\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.phone_call_category) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getLabelFromDropdown(\"activitydisposition\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.disposition_code) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.reason) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.start_date) ? i0.ɵɵpipeBind3(75, 19, ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.start_date, \"MM-dd-yyyy hh:mm a\", \"CST\") + \" CST\" : \"-\", \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.end_date) ? i0.ɵɵpipeBind3(84, 23, ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.end_date, \"MM-dd-yyyy hh:mm a\", \"CST\") + \" CST\" : \"-\", \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.business_partner_owner == null ? null : ctx_r0.overviewDetails.business_partner_owner.bp_full_name) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.brand) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.customer_group) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.ranking) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"activityStatus\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.activity_status) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getLabelFromDropdown(\"activityInitiatorCode\", ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.initiator_code) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.overviewDetails == null ? null : ctx_r0.overviewDetails.customer_timezone) || \"-\", \" \");\n  }\n}\nfunction TaskOverviewComponent_form_6_ng_template_12_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r3.bp_full_name, \"\");\n  }\n}\nfunction TaskOverviewComponent_form_6_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, TaskOverviewComponent_form_6_ng_template_12_span_2_Template, 2, 1, \"span\", 54);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.bp_full_name);\n  }\n}\nfunction TaskOverviewComponent_form_6_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_form_6_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, TaskOverviewComponent_form_6_div_13_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_account_party_id\"].errors && ctx_r0.f[\"main_account_party_id\"].errors[\"required\"]);\n  }\n}\nfunction TaskOverviewComponent_form_6_ng_template_24_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r5.email, \"\");\n  }\n}\nfunction TaskOverviewComponent_form_6_ng_template_24_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r5.mobile, \"\");\n  }\n}\nfunction TaskOverviewComponent_form_6_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"input\", 57);\n    i0.ɵɵlistener(\"change\", function TaskOverviewComponent_form_6_ng_template_24_Template_input_change_1_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).item;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.toggleSelection(item_r5.bp_id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TaskOverviewComponent_form_6_ng_template_24_span_4_Template, 2, 1, \"span\", 54)(5, TaskOverviewComponent_form_6_ng_template_24_span_5_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.item;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r0.isSelected(item_r5.bp_id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r5.bp_id, \": \", item_r5.bp_full_name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.mobile);\n  }\n}\nfunction TaskOverviewComponent_form_6_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Contact is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_form_6_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, TaskOverviewComponent_form_6_div_25_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_contact_party_id\"].errors && ctx_r0.f[\"main_contact_party_id\"].errors[\"required\"]);\n  }\n}\nfunction TaskOverviewComponent_form_6_div_35_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Subject is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_form_6_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, TaskOverviewComponent_form_6_div_35_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"subject\"].errors && ctx_r0.f[\"subject\"].errors[\"required\"]);\n  }\n}\nfunction TaskOverviewComponent_form_6_div_45_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Category is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_form_6_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, TaskOverviewComponent_form_6_div_45_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"phone_call_category\"].errors && ctx_r0.f[\"phone_call_category\"].errors[\"required\"]);\n  }\n}\nfunction TaskOverviewComponent_form_6_div_104_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Status is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_form_6_div_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, TaskOverviewComponent_form_6_div_104_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"activity_status\"].errors && ctx_r0.f[\"activity_status\"].errors[\"required\"]);\n  }\n}\nfunction TaskOverviewComponent_form_6_div_114_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_form_6_div_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, TaskOverviewComponent_form_6_div_114_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"initiator_code\"].errors && ctx_r0.f[\"initiator_code\"].errors[\"required\"]);\n  }\n}\nfunction TaskOverviewComponent_form_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 34)(1, \"div\", 26)(2, \"div\", 27)(3, \"div\", 28)(4, \"label\", 35)(5, \"span\", 36);\n    i0.ɵɵtext(6, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \"Account \");\n    i0.ɵɵelementStart(8, \"span\", 37);\n    i0.ɵɵtext(9, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"ng-select\", 38);\n    i0.ɵɵpipe(11, \"async\");\n    i0.ɵɵtemplate(12, TaskOverviewComponent_form_6_ng_template_12_Template, 3, 2, \"ng-template\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, TaskOverviewComponent_form_6_div_13_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 27)(15, \"div\", 28)(16, \"label\", 35)(17, \"span\", 36);\n    i0.ɵɵtext(18, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19, \"Contact \");\n    i0.ɵɵelementStart(20, \"span\", 37);\n    i0.ɵɵtext(21, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"ng-select\", 41);\n    i0.ɵɵpipe(23, \"async\");\n    i0.ɵɵtemplate(24, TaskOverviewComponent_form_6_ng_template_24_Template, 6, 5, \"ng-template\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, TaskOverviewComponent_form_6_div_25_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 27)(27, \"div\", 28)(28, \"label\", 35)(29, \"span\", 36);\n    i0.ɵɵtext(30, \"subject\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" Subject \");\n    i0.ɵɵelementStart(32, \"span\", 37);\n    i0.ɵɵtext(33, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(34, \"input\", 42);\n    i0.ɵɵtemplate(35, TaskOverviewComponent_form_6_div_35_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 27)(37, \"div\", 28)(38, \"label\", 35)(39, \"span\", 36);\n    i0.ɵɵtext(40, \"category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41, \"Category \");\n    i0.ɵɵelementStart(42, \"span\", 37);\n    i0.ɵɵtext(43, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(44, \"p-dropdown\", 43);\n    i0.ɵɵtemplate(45, TaskOverviewComponent_form_6_div_45_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 27)(47, \"div\", 28)(48, \"label\", 35)(49, \"span\", 36);\n    i0.ɵɵtext(50, \"code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(51, \"Disposition Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(52, \"p-dropdown\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 27)(54, \"div\", 28)(55, \"label\", 35)(56, \"span\", 36);\n    i0.ɵɵtext(57, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(58, \"Reason \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"input\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 27)(61, \"div\", 28)(62, \"label\", 35)(63, \"span\", 36);\n    i0.ɵɵtext(64, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(65, \"Call Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(66, \"p-calendar\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"div\", 27)(68, \"div\", 28)(69, \"label\", 35)(70, \"span\", 36);\n    i0.ɵɵtext(71, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(72, \"End Date/Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(73, \"p-calendar\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(74, \"div\", 27)(75, \"div\", 28)(76, \"label\", 35)(77, \"span\", 36);\n    i0.ɵɵtext(78, \"branding_watermark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(79, \"Brand \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(80, \"input\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(81, \"div\", 27)(82, \"div\", 28)(83, \"label\", 35)(84, \"span\", 36);\n    i0.ɵɵtext(85, \"group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(86, \"Customer Group \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(87, \"input\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(88, \"div\", 27)(89, \"div\", 28)(90, \"label\", 35)(91, \"span\", 36);\n    i0.ɵɵtext(92, \"emoji_events\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(93, \"Ranking \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(94, \"input\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(95, \"div\", 27)(96, \"div\", 28)(97, \"label\", 35)(98, \"span\", 36);\n    i0.ɵɵtext(99, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(100, \"Status \");\n    i0.ɵɵelementStart(101, \"span\", 37);\n    i0.ɵɵtext(102, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(103, \"p-dropdown\", 51);\n    i0.ɵɵtemplate(104, TaskOverviewComponent_form_6_div_104_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(105, \"div\", 27)(106, \"div\", 28)(107, \"label\", 35)(108, \"span\", 36);\n    i0.ɵɵtext(109, \"label\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(110, \"Type \");\n    i0.ɵɵelementStart(111, \"span\", 37);\n    i0.ɵɵtext(112, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(113, \"p-dropdown\", 52);\n    i0.ɵɵtemplate(114, TaskOverviewComponent_form_6_div_114_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(115, \"div\", 53)(116, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function TaskOverviewComponent_form_6_Template_button_click_116_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSubmit());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.SalesCallOverviewForm);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(11, 38, ctx_r0.accounts$))(\"hideSelected\", true)(\"loading\", ctx_r0.accountLoading)(\"minTermLength\", 0)(\"compareWith\", ctx_r0.compareById)(\"typeahead\", ctx_r0.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(42, _c2, ctx_r0.submitted && ctx_r0.f[\"main_account_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_account_party_id\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(23, 40, ctx_r0.contacts$))(\"hideSelected\", true)(\"loading\", ctx_r0.contactLoading)(\"minTermLength\", 0)(\"typeahead\", ctx_r0.contactInput$)(\"maxSelectedItems\", 10)(\"multiple\", true)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(44, _c2, ctx_r0.submitted && ctx_r0.f[\"main_contact_party_id\"].errors));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"main_contact_party_id\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(46, _c2, ctx_r0.submitted && ctx_r0.f[\"subject\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"subject\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activityCategory\"])(\"styleClass\", \"h-3rem w-full\")(\"ngClass\", i0.ɵɵpureFunction1(48, _c2, ctx_r0.submitted && ctx_r0.f[\"phone_call_category\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"phone_call_category\"].errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activitydisposition\"])(\"styleClass\", \"h-3rem w-full\");\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(30);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activityStatus\"])(\"ngClass\", i0.ɵɵpureFunction1(50, _c2, ctx_r0.submitted && ctx_r0.f[\"activity_status\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"activity_status\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdowns[\"activityInitiatorCode\"])(\"ngClass\", i0.ɵɵpureFunction1(52, _c2, ctx_r0.submitted && ctx_r0.f[\"initiator_code\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"initiator_code\"].errors);\n  }\n}\nfunction TaskOverviewComponent_ng_template_16_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 64);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.sortOrderNotes === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction TaskOverviewComponent_ng_template_16_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 65);\n  }\n}\nfunction TaskOverviewComponent_ng_template_16_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 64);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.sortOrderNotes === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction TaskOverviewComponent_ng_template_16_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 65);\n  }\n}\nfunction TaskOverviewComponent_ng_template_16_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 66);\n    i0.ɵɵlistener(\"click\", function TaskOverviewComponent_ng_template_16_ng_container_6_Template_th_click_1_listener() {\n      const col_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.customSort(col_r8.field, ctx_r0.notedetails, \"Notes\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 59);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, TaskOverviewComponent_ng_template_16_ng_container_6_i_4_Template, 1, 1, \"i\", 60)(5, TaskOverviewComponent_ng_template_16_ng_container_6_i_5_Template, 1, 0, \"i\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r8 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r8.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r8.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNotes === col_r8.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNotes !== col_r8.field);\n  }\n}\nfunction TaskOverviewComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 58);\n    i0.ɵɵlistener(\"click\", function TaskOverviewComponent_ng_template_16_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.customSort(\"note\", ctx_r0.notedetails, \"Notes\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 59);\n    i0.ɵɵtext(3, \" Note \");\n    i0.ɵɵtemplate(4, TaskOverviewComponent_ng_template_16_i_4_Template, 1, 1, \"i\", 60)(5, TaskOverviewComponent_ng_template_16_i_5_Template, 1, 0, \"i\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TaskOverviewComponent_ng_template_16_ng_container_6_Template, 6, 4, \"ng-container\", 62);\n    i0.ɵɵelementStart(7, \"th\", 63);\n    i0.ɵɵtext(8, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNotes === \"note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortFieldNotes !== \"note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedNotesColumns);\n  }\n}\nfunction TaskOverviewComponent_ng_template_17_ng_container_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notes_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, notes_r10 == null ? null : notes_r10.updatedAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction TaskOverviewComponent_ng_template_17_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notes_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (notes_r10 == null ? null : notes_r10.updatedBy) || \"-\", \" \");\n  }\n}\nfunction TaskOverviewComponent_ng_template_17_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 71);\n    i0.ɵɵtemplate(3, TaskOverviewComponent_ng_template_17_ng_container_2_ng_container_3_Template, 3, 4, \"ng-container\", 72)(4, TaskOverviewComponent_ng_template_17_ng_container_2_ng_container_4_Template, 2, 1, \"ng-container\", 72);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r11.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedAt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedBy\");\n  }\n}\nfunction TaskOverviewComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 67);\n    i0.ɵɵelement(1, \"td\", 68);\n    i0.ɵɵtemplate(2, TaskOverviewComponent_ng_template_17_ng_container_2_Template, 5, 3, \"ng-container\", 62);\n    i0.ɵɵelementStart(3, \"td\", 63)(4, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function TaskOverviewComponent_ng_template_17_Template_button_click_4_listener() {\n      const notes_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.editNote(notes_r10));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function TaskOverviewComponent_ng_template_17_Template_button_click_5_listener($event) {\n      const notes_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r0.confirmRemove(notes_r10));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notes_r10 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", (notes_r10 == null ? null : notes_r10.note) || \"-\", i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedNotesColumns);\n  }\n}\nfunction TaskOverviewComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 73);\n    i0.ɵɵtext(2, \"No notes found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskOverviewComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 73);\n    i0.ɵɵtext(2, \"Loading notes data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskOverviewComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Note\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Note is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskOverviewComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtemplate(1, TaskOverviewComponent_div_26_div_1_Template, 2, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.fNote[\"note\"].errors[\"required\"]);\n  }\n}\nexport class TaskOverviewComponent {\n  constructor(formBuilder, activitiesservice, messageservice, confirmationservice) {\n    this.formBuilder = formBuilder;\n    this.activitiesservice = activitiesservice;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.ngUnsubscribe = new Subject();\n    this.overviewDetails = null;\n    this.accountLoading = false;\n    this.accountInput$ = new Subject();\n    this.contactLoading = false;\n    this.contactInput$ = new Subject();\n    this.employeeLoading = false;\n    this.employeeInput$ = new Subject();\n    this.defaultOptions = [];\n    this.submitted = false;\n    this.saving = false;\n    this.id = '';\n    this.editid = '';\n    this.isEditMode = false;\n    this.notedetails = null;\n    this.notevisible = false;\n    this.noteposition = 'right';\n    this.notesubmitted = false;\n    this.notesaving = false;\n    this.noteeditid = '';\n    this.dropdowns = {\n      activityDocumentType: [],\n      activityStatus: [],\n      activityCategory: [],\n      activitydisposition: [],\n      activityInitiatorCode: []\n    };\n    this.SalesCallOverviewForm = this.formBuilder.group({\n      main_account_party_id: ['', [Validators.required]],\n      main_contact_party_id: ['', [Validators.required]],\n      subject: ['', [Validators.required]],\n      phone_call_category: ['', [Validators.required]],\n      disposition_code: [''],\n      reason: [''],\n      start_date: [''],\n      end_date: [''],\n      brand: [''],\n      ranking: [''],\n      customer_group: [''],\n      initiator_code: ['', [Validators.required]],\n      activity_status: ['', [Validators.required]]\n    });\n    this.NoteForm = this.formBuilder.group({\n      note: ['', [Validators.required]]\n    });\n    this._selectedNotesColumns = [];\n    this.NotesCols = [{\n      field: 'updatedAt',\n      header: 'Last Updated On'\n    }, {\n      field: 'updatedBy',\n      header: 'Updated By'\n    }];\n    this.sortFieldNotes = '';\n    this.sortOrderNotes = 1;\n    this.compareById = (a, b) => a === b;\n  }\n  ngOnInit() {\n    // sales call successfully added message.\n    setTimeout(() => {\n      const successMessage = sessionStorage.getItem('taskMessage');\n      if (successMessage) {\n        this.messageservice.add({\n          severity: 'success',\n          detail: successMessage\n        });\n        sessionStorage.removeItem('taskMessage');\n      }\n    }, 100);\n    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n    this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n    this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_PHONE_CALL_CATEGORY');\n    this.loadActivityDropDown('activitydisposition', 'CRM_ACTIVITY_DISPOSITION_CODE');\n    this.loadActivityDropDown('activityInitiatorCode', 'CRM_ACTIVITY_INITIATOR_CODE');\n    this.SalesCallOverviewForm.get('main_account_party_id')?.valueChanges.pipe(takeUntil(this.ngUnsubscribe), tap(selectedBpId => {\n      if (selectedBpId) {\n        this.loadAccountByContacts(selectedBpId);\n      } else {\n        this.contacts$ = of(this.defaultOptions);\n      }\n    }), catchError(err => {\n      console.error('Account selection error:', err);\n      this.contacts$ = of(this.defaultOptions);\n      return of();\n    })).subscribe();\n    this.loadAccounts();\n    this.activitiesservice.activity.pipe(takeUntil(this.ngUnsubscribe)).subscribe(response => {\n      if (!response) return;\n      this.id = response?.activity_id;\n      this.overviewDetails = response;\n      this.notedetails = response?.notes;\n      if (this.overviewDetails) {\n        this.fetchOverviewData(this.overviewDetails);\n      }\n    });\n    this._selectedNotesColumns = this.NotesCols;\n  }\n  get selectedNotesColumns() {\n    return this._selectedNotesColumns;\n  }\n  set selectedNotesColumns(val) {\n    this._selectedNotesColumns = this.NotesCols.filter(col => val.includes(col));\n  }\n  onNotesColumnReorder(event) {\n    const draggedCol = this.NotesCols[event.dragIndex];\n    this.NotesCols.splice(event.dragIndex, 1);\n    this.NotesCols.splice(event.dropIndex, 0, draggedCol);\n  }\n  customSort(field, data, type) {\n    if (type === 'Notes') {\n      this.sortFieldNotes = field;\n      this.sortOrderNotes = this.sortOrderNotes === 1 ? -1 : 1;\n    }\n    data.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrderNotes * result;\n    });\n  }\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      let fields = field.split('.');\n      let value = data;\n      for (let i = 0; i < fields.length; i++) {\n        if (value == null) return null;\n        value = value[fields[i]];\n      }\n      return value;\n    }\n  }\n  loadActivityDropDown(target, type) {\n    this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  editNote(note) {\n    this.notevisible = true;\n    this.noteeditid = note?.documentId;\n    this.NoteForm.patchValue(note);\n  }\n  fetchOverviewData(activity) {\n    this.existingActivity = {\n      main_account_party_id: activity?.main_account_party_id,\n      main_contact_party_id: activity?.main_contact_party_id,\n      subject: activity?.subject,\n      phone_call_category: activity?.phone_call_category,\n      disposition_code: activity?.disposition_code,\n      reason: activity?.reason,\n      start_date: activity?.start_date ? new Date(activity?.start_date) : null,\n      end_date: activity?.end_date ? new Date(activity?.end_date) : null,\n      owner_party_id: activity?.owner_party_id,\n      brand: activity?.brand,\n      customer_group: activity?.customer_group,\n      ranking: activity?.ranking,\n      initiator_code: activity?.initiator_code,\n      activity_status: activity?.activity_status\n    };\n    this.editid = activity.documentId;\n    this.SalesCallOverviewForm.patchValue(this.existingActivity);\n  }\n  loadAccounts() {\n    this.accounts$ = concat(of(this.defaultOptions),\n    // Default empty options\n    this.accountInput$.pipe(distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n      const params = {\n        [`filters[roles][bp_role][$eq][0]`]: 'FLCU01',\n        [`filters[roles][bp_role][$eq][1]`]: 'FLCU00',\n        [`fields[0]`]: 'bp_id',\n        [`fields[1]`]: 'first_name',\n        [`fields[2]`]: 'last_name',\n        [`fields[3]`]: 'bp_full_name'\n      };\n      if (term) {\n        params[`filters[$or][0][bp_id][$containsi]`] = term;\n        params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n      }\n      return this.activitiesservice.getPartners(params).pipe(map(data => {\n        return data || []; // Make sure to return correct data structure\n      }), tap(() => this.accountLoading = false), catchError(error => {\n        this.accountLoading = false;\n        return of([]);\n      }));\n    })));\n  }\n  loadAccountByContacts(bpId) {\n    this.contacts$ = this.contactInput$.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), tap(() => this.contactLoading = true), switchMap(term => {\n      const params = {\n        'filters[bp_company_id][$eq]': bpId,\n        'populate[business_partner_person][populate][addresses][populate]': '*'\n      };\n      if (term) {\n        params['filters[$or][0][business_partner_person][bp_id][$containsi]'] = term;\n        params['filters[$or][1][business_partner_person][bp_full_name][$containsi]'] = term;\n      }\n      return this.activitiesservice.getPartnersContact(params).pipe(map(response => response || []), tap(contacts => {\n        this.contactLoading = false;\n      }), catchError(error => {\n        console.error('Contact loading failed:', error);\n        this.contactLoading = false;\n        return of([]);\n      }));\n    }));\n  }\n  toggleSelection(id) {\n    const control = this.SalesCallOverviewForm.get('main_contact_party_id');\n    let currentValue = control?.value || [];\n    if (currentValue.includes(id)) {\n      currentValue = currentValue.filter(v => v !== id);\n    } else {\n      currentValue = [...currentValue, id];\n    }\n    control?.setValue(currentValue);\n  }\n  isSelected(id) {\n    return this.SalesCallOverviewForm.get('main_contact_party_id')?.value?.includes(id);\n  }\n  onNoteSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.notesubmitted = true;\n      _this.notevisible = true;\n      if (_this.NoteForm.invalid) {\n        _this.notevisible = true;\n        return;\n      }\n      _this.notesaving = true;\n      const value = {\n        ..._this.NoteForm.value\n      };\n      const data = {\n        activity_id: _this.id,\n        note: value?.note\n      };\n      if (_this.noteeditid) {\n        _this.activitiesservice.updateNote(_this.noteeditid, data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n          complete: () => {\n            _this.notesaving = false;\n            _this.notevisible = false;\n            _this.NoteForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Note Updated Successfully!.'\n            });\n            _this.activitiesservice.getActivityByID(_this.id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n          },\n          error: res => {\n            _this.notesaving = false;\n            _this.notevisible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      } else {\n        _this.activitiesservice.createNote(data).pipe(takeUntil(_this.ngUnsubscribe)).subscribe({\n          complete: () => {\n            _this.notesaving = false;\n            _this.notevisible = false;\n            _this.NoteForm.reset();\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Note Created Successfully!.'\n            });\n            _this.activitiesservice.getActivityByID(_this.id).pipe(takeUntil(_this.ngUnsubscribe)).subscribe();\n          },\n          error: res => {\n            _this.notesaving = false;\n            _this.notevisible = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      }\n    })();\n  }\n  onSubmit() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.submitted = true;\n      if (_this2.SalesCallOverviewForm.invalid) {\n        return;\n      }\n      _this2.saving = true;\n      const value = {\n        ..._this2.SalesCallOverviewForm.value\n      };\n      const data = {\n        subject: value?.subject,\n        main_account_party_id: value?.main_account_party_id,\n        main_contact_party_id: value?.main_contact_party_id,\n        phone_call_category: value?.phone_call_category,\n        start_date: value?.start_date ? _this2.formatDate(value.start_date) : null,\n        end_date: value?.end_date ? _this2.formatDate(value.end_date) : null,\n        disposition_code: value?.disposition_code,\n        initiator_code: value?.initiator_code,\n        //owner_party_id: value?.owner_party_id,\n        activity_status: value?.activity_status,\n        reason: value?.reason,\n        brand: value?.brand,\n        customer_group: value?.customer_group,\n        ranking: value?.ranking\n      };\n      _this2.activitiesservice.updateActivity(_this2.editid, data).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe({\n        next: response => {\n          _this2.messageservice.add({\n            severity: 'success',\n            detail: 'Sales Call Updated successFully!'\n          });\n          _this2.activitiesservice.getActivityByID(_this2.id).pipe(takeUntil(_this2.ngUnsubscribe)).subscribe();\n          _this2.isEditMode = false;\n        },\n        error: res => {\n          _this2.saving = false;\n          _this2.isEditMode = true;\n          _this2.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  confirmRemove(item) {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.remove(item);\n      }\n    });\n  }\n  remove(item) {\n    this.activitiesservice.deleteNote(item.documentId).pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Deleted Successfully!'\n        });\n        this.activitiesservice.getActivityByID(this.id).pipe(takeUntil(this.ngUnsubscribe)).subscribe();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  stripHtml(html) {\n    const temp = document.createElement('div');\n    temp.innerHTML = html;\n    return temp.textContent || temp.innerText || '';\n  }\n  formatDate(date) {\n    if (!date) return '';\n    const yyyy = date.getFullYear();\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\n    const dd = String(date.getDate()).padStart(2, '0');\n    return `${yyyy}-${mm}-${dd}`;\n  }\n  get f() {\n    return this.SalesCallOverviewForm.controls;\n  }\n  get fNote() {\n    return this.NoteForm.controls;\n  }\n  showDialog(position) {\n    this.noteposition = position;\n    this.notevisible = true;\n    this.notesubmitted = false;\n    this.NoteForm.reset();\n  }\n  toggleEdit() {\n    this.isEditMode = !this.isEditMode;\n  }\n  onReset() {\n    this.submitted = false;\n    this.SalesCallOverviewForm.reset();\n  }\n  ngOnDestroy() {\n    this.ngUnsubscribe.next();\n    this.ngUnsubscribe.complete();\n  }\n  static {\n    this.ɵfac = function TaskOverviewComponent_Factory(t) {\n      return new (t || TaskOverviewComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivitiesService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TaskOverviewComponent,\n      selectors: [[\"app-task-overview\"]],\n      decls: 30,\n      vars: 31,\n      consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"label\", \"icon\", \"styleClass\", \"rounded\"], [\"class\", \"p-fluid p-formgrid grid m-0\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\", \"mt-5\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"bp_id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"note-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"form-input\", \"flex-1\", \"relative\"], [\"formControlName\", \"note\", \"placeholder\", \"Enter text here...\", 3, \"ngClass\"], [\"class\", \"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"m-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"mb-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-primary\"], [1, \"readonly-field\", \"font-medium\", \"text-700\", \"p-2\"], [1, \"readonly-field\", \"font-medium\", \"p-2\", \"text-orange-600\", \"cursor-pointer\"], [\"target\", \"_blank\", 2, \"text-decoration\", \"none\", \"color\", \"inherit\", 3, \"href\"], [3, \"formGroup\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_id\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_account_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"compareWith\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"main_contact_party_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"multiple\", \"closeOnSelect\", \"ngClass\"], [\"pInputText\", \"\", \"id\", \"subject\", \"type\", \"text\", \"formControlName\", \"subject\", \"placeholder\", \"Subject\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"formControlName\", \"phone_call_category\", \"placeholder\", \"Select Category\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"options\", \"styleClass\", \"ngClass\"], [\"formControlName\", \"disposition_code\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"placeholder\", \"Select Disposition Code\", 3, \"options\", \"styleClass\"], [\"pInputText\", \"\", \"id\", \"reason\", \"type\", \"text\", \"formControlName\", \"reason\", \"placeholder\", \"Reason\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"start_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Call Date/Time\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"formControlName\", \"end_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"End Date/Time\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"pInputText\", \"\", \"id\", \"brand\", \"type\", \"text\", \"formControlName\", \"brand\", \"placeholder\", \"Brand'\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"customer_group\", \"type\", \"text\", \"formControlName\", \"customer_group\", \"placeholder\", \"Customer Group'\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"ranking\", \"type\", \"text\", \"formControlName\", \"ranking\", \"placeholder\", \"Ranking'\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"activity_status\", \"placeholder\", \"Select a Status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [\"formControlName\", \"initiator_code\", \"placeholder\", \"Select a Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\", \"ngClass\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [4, \"ngIf\"], [1, \"p-error\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"checkbox\", 2, \"width\", \"20px\", \"height\", \"20px\", \"margin-right\", \"6px\", 3, \"change\", \"checked\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"note-text\", 3, \"innerHTML\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", 1, \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"colspan\", \"8\", 1, \"border-round-left-lg\"], [1, \"invalid-feedback\", \"absolute\", \"top-0\", \"bottom-0\", \"h-1rem\", \"m-auto\"]],\n      template: function TaskOverviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p-button\", 3);\n          i0.ɵɵlistener(\"click\", function TaskOverviewComponent_Template_p_button_click_4_listener() {\n            return ctx.toggleEdit();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(5, TaskOverviewComponent_div_5_Template, 141, 27, \"div\", 4)(6, TaskOverviewComponent_form_6_Template, 117, 54, \"form\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"h4\", 2);\n          i0.ɵɵtext(10, \"Notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"p-button\", 9);\n          i0.ɵɵlistener(\"click\", function TaskOverviewComponent_Template_p_button_click_12_listener() {\n            return ctx.showDialog(\"right\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p-multiSelect\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TaskOverviewComponent_Template_p_multiSelect_ngModelChange_13_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedNotesColumns, $event) || (ctx.selectedNotesColumns = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 11)(15, \"p-table\", 12);\n          i0.ɵɵlistener(\"onColReorder\", function TaskOverviewComponent_Template_p_table_onColReorder_15_listener($event) {\n            return ctx.onNotesColumnReorder($event);\n          });\n          i0.ɵɵtemplate(16, TaskOverviewComponent_ng_template_16_Template, 9, 3, \"ng-template\", 13)(17, TaskOverviewComponent_ng_template_17_Template, 6, 2, \"ng-template\", 14)(18, TaskOverviewComponent_ng_template_18_Template, 3, 0, \"ng-template\", 15)(19, TaskOverviewComponent_ng_template_19_Template, 3, 0, \"ng-template\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"p-dialog\", 17);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function TaskOverviewComponent_Template_p_dialog_visibleChange_20_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.notevisible, $event) || (ctx.notevisible = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(21, TaskOverviewComponent_ng_template_21_Template, 2, 0, \"ng-template\", 13);\n          i0.ɵɵelementStart(22, \"form\", 18)(23, \"div\", 19)(24, \"div\", 20);\n          i0.ɵɵelement(25, \"p-editor\", 21);\n          i0.ɵɵtemplate(26, TaskOverviewComponent_div_26_Template, 2, 1, \"div\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 23)(28, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function TaskOverviewComponent_Template_button_click_28_listener() {\n            return ctx.notevisible = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function TaskOverviewComponent_Template_button_click_29_listener() {\n            return ctx.onNoteSubmit();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"label\", ctx.isEditMode ? \"Close\" : \"Edit\")(\"icon\", !ctx.isEditMode ? \"pi pi-pencil\" : \"\")(\"styleClass\", \"w-5rem font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.NotesCols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedNotesColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.notedetails)(\"rows\", 10)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(27, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.notevisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.NoteForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(28, _c1));\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(29, _c2, ctx.notesubmitted && ctx.fNote[\"note\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.notesubmitted && ctx.fNote[\"note\"].errors);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i5.NgSelectComponent, i5.NgOptionTemplateDirective, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.FormGroupDirective, i1.FormControlName, i6.Table, i3.PrimeTemplate, i6.SortableColumn, i6.FrozenColumn, i6.ReorderableColumn, i7.ButtonDirective, i7.Button, i8.Dropdown, i9.Tooltip, i10.Calendar, i11.InputText, i12.Dialog, i13.Editor, i14.MultiSelect, i4.AsyncPipe, i4.DatePipe],\n      styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%] {\\n  color: var(--red-500);\\n  right: 10px;\\n}\\n\\n  .note-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .note-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .note-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .note-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvYWN0aXZpdGllcy90YXNrL3Rhc2stZGV0YWlscy90YXNrLW92ZXJ2aWV3L3Rhc2stb3ZlcnZpZXcuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7RUFJSSxxQkFBQTtFQUNBLFdBQUE7QUFDSjs7QUFJUTtFQUNJLGtCQUFBO0FBRFo7QUFHWTtFQUNJLDRCQUFBO0VBQ0EsMkNBQUE7QUFEaEI7QUFHZ0I7RUFDSSxTQUFBO0FBRHBCO0FBS1k7RUFDSSw0QkFBQTtFQUNBLGlCQUFBO0FBSGhCIiwic291cmNlc0NvbnRlbnQiOlsiLmludmFsaWQtZmVlZGJhY2ssXHJcbi5wLWlucHV0dGV4dDppbnZhbGlkLFxyXG4uaXMtY2hlY2tib3gtaW52YWxpZCxcclxuLnAtaW5wdXR0ZXh0LmlzLWludmFsaWQge1xyXG4gICAgY29sb3I6IHZhcigtLXJlZC01MDApO1xyXG4gICAgcmlnaHQ6IDEwcHg7XHJcbn1cclxuXHJcbjo6bmctZGVlcCB7XHJcbiAgICAubm90ZS1wb3B1cCB7XHJcbiAgICAgICAgLnAtZGlhbG9nIHtcclxuICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA1MHB4O1xyXG5cclxuICAgICAgICAgICAgLnAtZGlhbG9nLWhlYWRlciB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTApO1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLXN1cmZhY2UtMTAwKTtcclxuXHJcbiAgICAgICAgICAgICAgICBoNCB7XHJcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAucC1kaWFsb2ctY29udGVudCB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTApO1xyXG4gICAgICAgICAgICAgICAgcGFkZGluZzogMS43MTRyZW07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "concat", "map", "of", "distinctUntilChanged", "switchMap", "tap", "startWith", "catchError", "debounceTime", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "overviewDetails", "activity_id", "subject", "getLabelFromDropdown", "document_type", "ɵɵproperty", "business_partner", "documentId", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "bp_full_name", "business_partner_contact", "contact_persons", "phone_call_category", "disposition_code", "reason", "start_date", "ɵɵpipeBind3", "end_date", "business_partner_owner", "brand", "customer_group", "ranking", "activity_status", "initiator_code", "customer_timezone", "item_r3", "ɵɵtemplate", "TaskOverviewComponent_form_6_ng_template_12_span_2_Template", "bp_id", "TaskOverviewComponent_form_6_div_13_div_1_Template", "submitted", "f", "errors", "item_r5", "email", "mobile", "ɵɵlistener", "TaskOverviewComponent_form_6_ng_template_24_Template_input_change_1_listener", "ɵɵrestoreView", "_r4", "item", "ɵɵnextContext", "ɵɵresetView", "toggleSelection", "TaskOverviewComponent_form_6_ng_template_24_span_4_Template", "TaskOverviewComponent_form_6_ng_template_24_span_5_Template", "isSelected", "ɵɵtextInterpolate2", "TaskOverviewComponent_form_6_div_25_div_1_Template", "TaskOverviewComponent_form_6_div_35_div_1_Template", "TaskOverviewComponent_form_6_div_45_div_1_Template", "TaskOverviewComponent_form_6_div_104_div_1_Template", "TaskOverviewComponent_form_6_div_114_div_1_Template", "TaskOverviewComponent_form_6_ng_template_12_Template", "TaskOverviewComponent_form_6_div_13_Template", "TaskOverviewComponent_form_6_ng_template_24_Template", "TaskOverviewComponent_form_6_div_25_Template", "ɵɵelement", "TaskOverviewComponent_form_6_div_35_Template", "TaskOverviewComponent_form_6_div_45_Template", "TaskOverviewComponent_form_6_div_104_Template", "TaskOverviewComponent_form_6_div_114_Template", "TaskOverviewComponent_form_6_Template_button_click_116_listener", "_r2", "onSubmit", "SalesCallOverviewForm", "ɵɵpipeBind1", "accounts$", "accountLoading", "compareById", "accountInput$", "ɵɵpureFunction1", "_c2", "contacts$", "contactLoading", "contactInput$", "dropdowns", "sortOrderNotes", "ɵɵelementContainerStart", "TaskOverviewComponent_ng_template_16_ng_container_6_Template_th_click_1_listener", "col_r8", "_r7", "$implicit", "customSort", "field", "notedetails", "TaskOverviewComponent_ng_template_16_ng_container_6_i_4_Template", "TaskOverviewComponent_ng_template_16_ng_container_6_i_5_Template", "header", "sortFieldNotes", "TaskOverviewComponent_ng_template_16_Template_th_click_1_listener", "_r6", "TaskOverviewComponent_ng_template_16_i_4_Template", "TaskOverviewComponent_ng_template_16_i_5_Template", "TaskOverviewComponent_ng_template_16_ng_container_6_Template", "selectedNotesColumns", "ɵɵpipeBind2", "notes_r10", "updatedAt", "updatedBy", "TaskOverviewComponent_ng_template_17_ng_container_2_ng_container_3_Template", "TaskOverviewComponent_ng_template_17_ng_container_2_ng_container_4_Template", "col_r11", "TaskOverviewComponent_ng_template_17_ng_container_2_Template", "TaskOverviewComponent_ng_template_17_Template_button_click_4_listener", "_r9", "editNote", "TaskOverviewComponent_ng_template_17_Template_button_click_5_listener", "$event", "stopPropagation", "confirmRemove", "note", "ɵɵsanitizeHtml", "TaskOverviewComponent_div_26_div_1_Template", "fNote", "TaskOverviewComponent", "constructor", "formBuilder", "activitiesservice", "messageservice", "confirmationservice", "ngUnsubscribe", "employeeLoading", "employeeInput$", "defaultOptions", "saving", "id", "editid", "isEditMode", "notevisible", "noteposition", "notesubmitted", "notesaving", "noteeditid", "activityDocumentType", "activityStatus", "activityCategory", "activitydisposition", "activityInitiatorCode", "group", "main_account_party_id", "required", "main_contact_party_id", "NoteForm", "_selectedNotesColumns", "NotesCols", "a", "b", "ngOnInit", "setTimeout", "successMessage", "sessionStorage", "getItem", "add", "severity", "detail", "removeItem", "loadActivityDropDown", "get", "valueChanges", "pipe", "selectedBpId", "loadAccountByContacts", "err", "console", "error", "subscribe", "loadAccounts", "activity", "response", "notes", "fetchOverviewData", "val", "filter", "col", "includes", "onNotesColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "data", "type", "sort", "value1", "resolveFieldData", "value2", "result", "localeCompare", "indexOf", "fields", "split", "value", "i", "length", "target", "getActivityDropdownOptions", "res", "attr", "label", "description", "code", "dropdownKey", "find", "opt", "patchValue", "existingActivity", "Date", "owner_party_id", "term", "params", "getPartners", "bpId", "getPartnersContact", "contacts", "control", "currentValue", "v", "setValue", "onNoteSubmit", "_this", "_asyncToGenerator", "invalid", "updateNote", "complete", "reset", "getActivityByID", "createNote", "_this2", "formatDate", "updateActivity", "next", "confirm", "message", "icon", "accept", "remove", "deleteNote", "stripHtml", "html", "temp", "document", "createElement", "innerHTML", "textContent", "innerText", "date", "yyyy", "getFullYear", "mm", "String", "getMonth", "padStart", "dd", "getDate", "controls", "showDialog", "position", "toggleEdit", "onReset", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivitiesService", "i3", "MessageService", "ConfirmationService", "selectors", "decls", "vars", "consts", "template", "TaskOverviewComponent_Template", "rf", "ctx", "TaskOverviewComponent_Template_p_button_click_4_listener", "TaskOverviewComponent_div_5_Template", "TaskOverviewComponent_form_6_Template", "TaskOverviewComponent_Template_p_button_click_12_listener", "ɵɵtwoWayListener", "TaskOverviewComponent_Template_p_multiSelect_ngModelChange_13_listener", "ɵɵtwoWayBindingSet", "TaskOverviewComponent_Template_p_table_onColReorder_15_listener", "TaskOverviewComponent_ng_template_16_Template", "TaskOverviewComponent_ng_template_17_Template", "TaskOverviewComponent_ng_template_18_Template", "TaskOverviewComponent_ng_template_19_Template", "TaskOverviewComponent_Template_p_dialog_visibleChange_20_listener", "TaskOverviewComponent_ng_template_21_Template", "TaskOverviewComponent_div_26_Template", "TaskOverviewComponent_Template_button_click_28_listener", "TaskOverviewComponent_Template_button_click_29_listener", "ɵɵtwoWayProperty", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "_c1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\task\\task-details\\task-overview\\task-overview.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\task\\task-details\\task-overview\\task-overview.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';\r\nimport { ActivitiesService } from '../../../activities.service';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport {\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n  startWith,\r\n  catchError,\r\n  debounceTime,\r\n} from 'rxjs/operators';\r\n\r\ninterface NotesColumn {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-task-overview',\r\n  templateUrl: './task-overview.component.html',\r\n  styleUrl: './task-overview.component.scss',\r\n})\r\nexport class TaskOverviewComponent implements OnInit {\r\n  private ngUnsubscribe = new Subject<void>();\r\n  public overviewDetails: any = null;\r\n  public accounts$?: Observable<any[]>;\r\n  public accountLoading = false;\r\n  public accountInput$ = new Subject<string>();\r\n  public contacts$?: Observable<any[]>;\r\n  public contactLoading = false;\r\n  public contactInput$ = new Subject<string>();\r\n  public employees$?: Observable<any[]>;\r\n  public employeeLoading = false;\r\n  public employeeInput$ = new Subject<string>();\r\n  private defaultOptions: any = [];\r\n  public submitted = false;\r\n  public saving = false;\r\n  public existingActivity: any;\r\n  public id: string = '';\r\n  public editid: string = '';\r\n  public isEditMode = false;\r\n  public notedetails: any = null;\r\n  public notevisible: boolean = false;\r\n  public noteposition: string = 'right';\r\n  public notesubmitted = false;\r\n  public notesaving = false;\r\n  public noteeditid: string = '';\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    activityDocumentType: [],\r\n    activityStatus: [],\r\n    activityCategory: [],\r\n    activitydisposition: [],\r\n    activityInitiatorCode: [],\r\n  };\r\n\r\n  public SalesCallOverviewForm: FormGroup = this.formBuilder.group({\r\n    main_account_party_id: ['', [Validators.required]],\r\n    main_contact_party_id: ['', [Validators.required]],\r\n    subject: ['', [Validators.required]],\r\n    phone_call_category: ['', [Validators.required]],\r\n    disposition_code: [''],\r\n    reason: [''],\r\n    start_date: [''],\r\n    end_date: [''],\r\n    brand: [''],\r\n    ranking: [''],\r\n    customer_group: [''],\r\n    initiator_code: ['', [Validators.required]],\r\n    activity_status: ['', [Validators.required]],\r\n  });\r\n\r\n  public NoteForm: FormGroup = this.formBuilder.group({\r\n    note: ['', [Validators.required]],\r\n  });\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private activitiesservice: ActivitiesService,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) {}\r\n\r\n  private _selectedNotesColumns: NotesColumn[] = [];\r\n\r\n  public NotesCols: NotesColumn[] = [\r\n    { field: 'updatedAt', header: 'Last Updated On' },\r\n    { field: 'updatedBy', header: 'Updated By' },\r\n  ];\r\n\r\n  sortFieldNotes: string = '';\r\n  sortOrderNotes: number = 1;\r\n\r\n  ngOnInit(): void {\r\n    // sales call successfully added message.\r\n    setTimeout(() => {\r\n      const successMessage = sessionStorage.getItem('taskMessage');\r\n      if (successMessage) {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: successMessage,\r\n        });\r\n        sessionStorage.removeItem('taskMessage');\r\n      }\r\n    }, 100);\r\n    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\r\n    this.loadActivityDropDown(\r\n      'activityDocumentType',\r\n      'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL'\r\n    );\r\n    this.loadActivityDropDown(\r\n      'activityCategory',\r\n      'CRM_ACTIVITY_PHONE_CALL_CATEGORY'\r\n    );\r\n    this.loadActivityDropDown(\r\n      'activitydisposition',\r\n      'CRM_ACTIVITY_DISPOSITION_CODE'\r\n    );\r\n    this.loadActivityDropDown(\r\n      'activityInitiatorCode',\r\n      'CRM_ACTIVITY_INITIATOR_CODE'\r\n    );\r\n    this.SalesCallOverviewForm.get('main_account_party_id')\r\n      ?.valueChanges.pipe(\r\n        takeUntil(this.ngUnsubscribe),\r\n        tap((selectedBpId) => {\r\n          if (selectedBpId) {\r\n            this.loadAccountByContacts(selectedBpId);\r\n          } else {\r\n            this.contacts$ = of(this.defaultOptions);\r\n          }\r\n        }),\r\n        catchError((err) => {\r\n          console.error('Account selection error:', err);\r\n          this.contacts$ = of(this.defaultOptions);\r\n          return of();\r\n        })\r\n      )\r\n      .subscribe();\r\n    this.loadAccounts();\r\n    this.activitiesservice.activity\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe((response: any) => {\r\n        if (!response) return;\r\n        this.id = response?.activity_id;\r\n        this.overviewDetails = response;\r\n        this.notedetails = response?.notes;\r\n        if (this.overviewDetails) {\r\n          this.fetchOverviewData(this.overviewDetails);\r\n        }\r\n      });\r\n\r\n    this._selectedNotesColumns = this.NotesCols;\r\n  }\r\n\r\n  get selectedNotesColumns(): any[] {\r\n    return this._selectedNotesColumns;\r\n  }\r\n\r\n  set selectedNotesColumns(val: any[]) {\r\n    this._selectedNotesColumns = this.NotesCols.filter((col) =>\r\n      val.includes(col)\r\n    );\r\n  }\r\n\r\n  onNotesColumnReorder(event: any) {\r\n    const draggedCol = this.NotesCols[event.dragIndex];\r\n    this.NotesCols.splice(event.dragIndex, 1);\r\n    this.NotesCols.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  customSort(field: string, data: any[], type: 'Notes') {\r\n    if (type === 'Notes') {\r\n      this.sortFieldNotes = field;\r\n      this.sortOrderNotes = this.sortOrderNotes === 1 ? -1 : 1;\r\n    }\r\n\r\n    data.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\r\n\r\n      return this.sortOrderNotes * result;\r\n    });\r\n  }\r\n\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      let fields = field.split('.');\r\n      let value = data;\r\n      for (let i = 0; i < fields.length; i++) {\r\n        if (value == null) return null;\r\n        value = value[fields[i]];\r\n      }\r\n      return value;\r\n    }\r\n  }\r\n\r\n  loadActivityDropDown(target: string, type: string): void {\r\n    this.activitiesservice\r\n      .getActivityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  editNote(note: any) {\r\n    this.notevisible = true;\r\n    this.noteeditid = note?.documentId;\r\n    this.NoteForm.patchValue(note);\r\n  }\r\n\r\n  fetchOverviewData(activity: any) {\r\n    this.existingActivity = {\r\n      main_account_party_id: activity?.main_account_party_id,\r\n      main_contact_party_id: activity?.main_contact_party_id,\r\n      subject: activity?.subject,\r\n      phone_call_category: activity?.phone_call_category,\r\n      disposition_code: activity?.disposition_code,\r\n      reason: activity?.reason,\r\n      start_date: activity?.start_date ? new Date(activity?.start_date) : null,\r\n      end_date: activity?.end_date ? new Date(activity?.end_date) : null,\r\n      owner_party_id: activity?.owner_party_id,\r\n      brand: activity?.brand,\r\n      customer_group: activity?.customer_group,\r\n      ranking: activity?.ranking,\r\n      initiator_code: activity?.initiator_code,\r\n      activity_status: activity?.activity_status,\r\n    };\r\n\r\n    this.editid = activity.documentId;\r\n    this.SalesCallOverviewForm.patchValue(this.existingActivity);\r\n  }\r\n\r\n  private loadAccounts() {\r\n    this.accounts$ = concat(\r\n      of(this.defaultOptions), // Default empty options\r\n      this.accountInput$.pipe(\r\n        distinctUntilChanged(),\r\n        tap(() => (this.accountLoading = true)),\r\n        switchMap((term: string) => {\r\n          const params: any = {\r\n            [`filters[roles][bp_role][$eq][0]`]: 'FLCU01',\r\n            [`filters[roles][bp_role][$eq][1]`]: 'FLCU00',\r\n            [`fields[0]`]: 'bp_id',\r\n            [`fields[1]`]: 'first_name',\r\n            [`fields[2]`]: 'last_name',\r\n            [`fields[3]`]: 'bp_full_name',\r\n          };\r\n\r\n          if (term) {\r\n            params[`filters[$or][0][bp_id][$containsi]`] = term;\r\n            params[`filters[$or][1][bp_full_name][$containsi]`] = term;\r\n          }\r\n          return this.activitiesservice.getPartners(params).pipe(\r\n            map((data: any) => {\r\n              return data || []; // Make sure to return correct data structure\r\n            }),\r\n            tap(() => (this.accountLoading = false)),\r\n            catchError((error) => {\r\n              this.accountLoading = false;\r\n              return of([]);\r\n            })\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  private loadAccountByContacts(bpId: string): void {\r\n    this.contacts$ = this.contactInput$.pipe(\r\n      startWith(''),\r\n      debounceTime(300),\r\n      distinctUntilChanged(),\r\n      tap(() => (this.contactLoading = true)),\r\n      switchMap((term: string) => {\r\n        const params: any = {\r\n          'filters[bp_company_id][$eq]': bpId,\r\n          'populate[business_partner_person][populate][addresses][populate]':\r\n            '*',\r\n        };\r\n\r\n        if (term) {\r\n          params[\r\n            'filters[$or][0][business_partner_person][bp_id][$containsi]'\r\n          ] = term;\r\n          params[\r\n            'filters[$or][1][business_partner_person][bp_full_name][$containsi]'\r\n          ] = term;\r\n        }\r\n\r\n        return this.activitiesservice.getPartnersContact(params).pipe(\r\n          map((response: any[]) => response || []),\r\n          tap((contacts: any[]) => {\r\n            this.contactLoading = false;\r\n          }),\r\n          catchError((error) => {\r\n            console.error('Contact loading failed:', error);\r\n            this.contactLoading = false;\r\n            return of([]);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  toggleSelection(id: string): void {\r\n    const control = this.SalesCallOverviewForm.get('main_contact_party_id');\r\n    let currentValue = control?.value || [];\r\n\r\n    if (currentValue.includes(id)) {\r\n      currentValue = currentValue.filter((v: any) => v !== id);\r\n    } else {\r\n      currentValue = [...currentValue, id];\r\n    }\r\n\r\n    control?.setValue(currentValue);\r\n  }\r\n\r\n  isSelected(id: string): boolean {\r\n    return this.SalesCallOverviewForm.get(\r\n      'main_contact_party_id'\r\n    )?.value?.includes(id);\r\n  }\r\n\r\n  async onNoteSubmit() {\r\n    this.notesubmitted = true;\r\n    this.notevisible = true;\r\n\r\n    if (this.NoteForm.invalid) {\r\n      this.notevisible = true;\r\n      return;\r\n    }\r\n\r\n    this.notesaving = true;\r\n    const value = { ...this.NoteForm.value };\r\n\r\n    const data = {\r\n      activity_id: this.id,\r\n      note: value?.note,\r\n    };\r\n\r\n    if (this.noteeditid) {\r\n      this.activitiesservice\r\n        .updateNote(this.noteeditid, data)\r\n        .pipe(takeUntil(this.ngUnsubscribe))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.notesaving = false;\r\n            this.notevisible = false;\r\n            this.NoteForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Note Updated Successfully!.',\r\n            });\r\n            this.activitiesservice\r\n              .getActivityByID(this.id)\r\n              .pipe(takeUntil(this.ngUnsubscribe))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.notesaving = false;\r\n            this.notevisible = true;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    } else {\r\n      this.activitiesservice\r\n        .createNote(data)\r\n        .pipe(takeUntil(this.ngUnsubscribe))\r\n        .subscribe({\r\n          complete: () => {\r\n            this.notesaving = false;\r\n            this.notevisible = false;\r\n            this.NoteForm.reset();\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Note Created Successfully!.',\r\n            });\r\n            this.activitiesservice\r\n              .getActivityByID(this.id)\r\n              .pipe(takeUntil(this.ngUnsubscribe))\r\n              .subscribe();\r\n          },\r\n          error: (res: any) => {\r\n            this.notesaving = false;\r\n            this.notevisible = true;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  async onSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.SalesCallOverviewForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const value = { ...this.SalesCallOverviewForm.value };\r\n\r\n    const data = {\r\n      subject: value?.subject,\r\n      main_account_party_id: value?.main_account_party_id,\r\n      main_contact_party_id: value?.main_contact_party_id,\r\n      phone_call_category: value?.phone_call_category,\r\n      start_date: value?.start_date ? this.formatDate(value.start_date) : null,\r\n      end_date: value?.end_date ? this.formatDate(value.end_date) : null,\r\n      disposition_code: value?.disposition_code,\r\n      initiator_code: value?.initiator_code,\r\n      //owner_party_id: value?.owner_party_id,\r\n      activity_status: value?.activity_status,\r\n      reason: value?.reason,\r\n      brand: value?.brand,\r\n      customer_group: value?.customer_group,\r\n      ranking: value?.ranking,\r\n    };\r\n\r\n    this.activitiesservice\r\n      .updateActivity(this.editid, data)\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Sales Call Updated successFully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.id)\r\n            .pipe(takeUntil(this.ngUnsubscribe))\r\n            .subscribe();\r\n          this.isEditMode = false;\r\n        },\r\n        error: (res: any) => {\r\n          this.saving = false;\r\n          this.isEditMode = true;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  confirmRemove(item: any) {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.remove(item);\r\n      },\r\n    });\r\n  }\r\n\r\n  remove(item: any) {\r\n    this.activitiesservice\r\n      .deleteNote(item.documentId)\r\n      .pipe(takeUntil(this.ngUnsubscribe))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Deleted Successfully!',\r\n          });\r\n          this.activitiesservice\r\n            .getActivityByID(this.id)\r\n            .pipe(takeUntil(this.ngUnsubscribe))\r\n            .subscribe();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  stripHtml(html: string): string {\r\n    const temp = document.createElement('div');\r\n    temp.innerHTML = html;\r\n    return temp.textContent || temp.innerText || '';\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    if (!date) return '';\r\n    const yyyy = date.getFullYear();\r\n    const mm = String(date.getMonth() + 1).padStart(2, '0');\r\n    const dd = String(date.getDate()).padStart(2, '0');\r\n    return `${yyyy}-${mm}-${dd}`;\r\n  }\r\n\r\n  get f(): any {\r\n    return this.SalesCallOverviewForm.controls;\r\n  }\r\n\r\n  get fNote(): any {\r\n    return this.NoteForm.controls;\r\n  }\r\n\r\n  showDialog(position: string) {\r\n    this.noteposition = position;\r\n    this.notevisible = true;\r\n    this.notesubmitted = false;\r\n    this.NoteForm.reset();\r\n  }\r\n\r\n  toggleEdit() {\r\n    this.isEditMode = !this.isEditMode;\r\n  }\r\n\r\n  onReset(): void {\r\n    this.submitted = false;\r\n    this.SalesCallOverviewForm.reset();\r\n  }\r\n\r\n  compareById = (a: any, b: any) => a === b;\r\n\r\n  ngOnDestroy() {\r\n    this.ngUnsubscribe.next();\r\n    this.ngUnsubscribe.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading mb-2 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Overview</h4>\r\n        <p-button [label]=\"isEditMode ? 'Close' : 'Edit'\" [icon]=\"!isEditMode ? 'pi pi-pencil':''\" iconPos=\"right\"\r\n            class=\"ml-auto\" [styleClass]=\"'w-5rem font-semibold px-3'\" (click)=\"toggleEdit()\" [rounded]=\"true\" />\r\n    </div>\r\n    <div *ngIf=\"!isEditMode\" class=\"p-fluid p-formgrid grid m-0\">\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">badge</span> ID\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.activity_id || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">subject</span> Subject\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{overviewDetails?.subject || '-'}}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">description</span>Transaction Type\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('activityDocumentType',\r\n                    overviewDetails?.document_type) || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">account_circle</span> Account\r\n                </label>\r\n                <div class=\"readonly-field font-medium p-2 text-orange-600 cursor-pointer\">\r\n                    <a [href]=\"'/#/store/account/' + overviewDetails?.business_partner?.documentId + '/overview'\"\r\n                        style=\"text-decoration: none; color: inherit;\" target=\"_blank\">\r\n                        {{ overviewDetails?.business_partner?.bp_full_name || '-' }}\r\n                    </a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">person</span> Contact\r\n                </label>\r\n                <div class=\"readonly-field font-medium p-2 text-orange-600 cursor-pointer\">\r\n                    <a [href]=\"'/#/store/contacts/' + overviewDetails?.business_partner_contact?.contact_persons?.[0]?.documentId + '/overview'\"\r\n                        style=\"text-decoration: none; color: inherit;\" target=\"_blank\">\r\n                        {{ overviewDetails?.business_partner_contact?.bp_full_name || '-' }}\r\n                    </a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">category</span> Category\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    getLabelFromDropdown('activityCategory',overviewDetails?.phone_call_category)\r\n                    || '-'}}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">code</span> Disposition Code\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    getLabelFromDropdown('activitydisposition',overviewDetails?.disposition_code)\r\n                    || '-'}}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">info</span> Reason\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.reason || '-'\r\n                    }}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">schedule</span> Call Date/Time\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.start_date ?\r\n                    (overviewDetails?.start_date | date: 'MM-dd-yyyy hh:mm a' : 'CST') + ' CST' : '-'\r\n                    }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">schedule</span> End Date/Time\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.end_date ?\r\n                    (overviewDetails?.end_date | date: 'MM-dd-yyyy hh:mm a' : 'CST') + ' CST' : '-'\r\n                    }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">account_circle</span> Owner\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    overviewDetails?.business_partner_owner?.bp_full_name || '-'\r\n                    }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">branding_watermark</span> Brand\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{\r\n                    overviewDetails?.brand || '-'\r\n                    }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">group</span> Customer Group\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.customer_group || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">emoji_events</span> Ranking\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.ranking || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">check_circle</span> Status\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('activityStatus',\r\n                    overviewDetails?.activity_status) || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">label</span> Type\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ getLabelFromDropdown('activityInitiatorCode',\r\n                    overviewDetails?.initiator_code) || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n            <div class=\"input-main\">\r\n                <label class=\"flex align-items-center gap-2 mb-2 text-800 font-semibold\">\r\n                    <span class=\"material-symbols-rounded text-2xl text-primary\">access_time</span> Customer TimeZone\r\n                </label>\r\n                <div class=\"readonly-field font-medium text-700 p-2\">{{ overviewDetails?.customer_timezone || '-' }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <form *ngIf=\"isEditMode\" [formGroup]=\"SalesCallOverviewForm\">\r\n        <div class=\"p-fluid p-formgrid grid m-0\">\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">account_circle</span>Account\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"accounts$ | async\" bindLabel=\"bp_id\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"accountLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"main_account_party_id\" [compareWith]=\"compareById\" [typeahead]=\"accountInput$\"\r\n                        [maxSelectedItems]=\"10\" appendTo=\"body\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['main_account_party_id'].errors }\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <span>{{ item.bp_id }}</span>\r\n                            <span *ngIf=\"item.bp_full_name\"> : {{ item.bp_full_name }}</span>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                    <div *ngIf=\"submitted && f['main_account_party_id'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['main_account_party_id'].errors &&\r\n                                f['main_account_party_id'].errors['required']\r\n                              \">\r\n                            Account is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">person</span>Contact\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <ng-select pInputText [items]=\"contacts$ | async\" bindLabel=\"bp_full_name\" bindValue=\"bp_id\"\r\n                        [hideSelected]=\"true\" [loading]=\"contactLoading\" [minTermLength]=\"0\"\r\n                        formControlName=\"main_contact_party_id\" [typeahead]=\"contactInput$\" [maxSelectedItems]=\"10\"\r\n                        appendTo=\"body\" [multiple]=\"true\" [closeOnSelect]=\"false\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['main_contact_party_id'].errors }\">\r\n                        <ng-template ng-option-tmp let-item=\"item\">\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <input type=\"checkbox\" [checked]=\"isSelected(item.bp_id)\"\r\n                                    (change)=\"toggleSelection(item.bp_id)\"\r\n                                    style=\"width: 20px; height: 20px; margin-right: 6px;\" />\r\n                                <span>{{ item.bp_id }}: {{ item.bp_full_name }}</span>\r\n                                <span *ngIf=\"item.email\"> : {{ item.email }}</span>\r\n                                <span *ngIf=\"item.mobile\"> : {{ item.mobile }}</span>\r\n                            </div>\r\n                        </ng-template>\r\n                    </ng-select>\r\n                    <div *ngIf=\"submitted && f['main_contact_party_id'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['main_contact_party_id'].errors &&\r\n                                f['main_contact_party_id'].errors['required']\r\n                              \">\r\n                            Contact is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">subject</span> Subject\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <input pInputText id=\"subject\" type=\"text\" formControlName=\"subject\" placeholder=\"Subject\"\r\n                        class=\"h-3rem w-full\" [ngClass]=\"{ 'is-invalid': submitted && f['subject'].errors }\" />\r\n                    <div *ngIf=\"submitted && f['subject'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                    submitted &&\r\n                                    f['subject'].errors &&\r\n                                    f['subject'].errors['required']\r\n                                  \">\r\n                            Subject is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">category</span>Category\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['activityCategory']\" formControlName=\"phone_call_category\"\r\n                        placeholder=\"Select Category\" optionLabel=\"label\" optionValue=\"value\"\r\n                        [styleClass]=\"'h-3rem w-full'\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['phone_call_category'].errors }\">\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"submitted && f['phone_call_category'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['phone_call_category'].errors &&\r\n                                f['phone_call_category'].errors['required']\r\n                              \">\r\n                            Category is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">code</span>Disposition Code\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['activitydisposition']\" formControlName=\"disposition_code\"\r\n                        optionLabel=\"label\" optionValue=\"value\" placeholder=\"Select Disposition Code\"\r\n                        [styleClass]=\"'h-3rem w-full'\">\r\n                    </p-dropdown>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">info</span>Reason\r\n                    </label>\r\n                    <input pInputText id=\"reason\" type=\"text\" formControlName=\"reason\" placeholder=\"Reason\"\r\n                        class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">schedule</span>Call Date/Time\r\n                    </label>\r\n                    <p-calendar formControlName=\"start_date\" [showButtonBar]=\"true\" dateFormat=\"yy-mm-dd\"\r\n                        placeholder=\"Call Date/Time\" [showIcon]=\"true\" styleClass=\"h-3rem w-full\"></p-calendar>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">schedule</span>End Date/Time\r\n                    </label>\r\n                    <p-calendar formControlName=\"end_date\" [showButtonBar]=\"true\" dateFormat=\"yy-mm-dd\"\r\n                        placeholder=\"End Date/Time\" [showIcon]=\"true\" styleClass=\"h-3rem w-full\"></p-calendar>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">branding_watermark</span>Brand\r\n                    </label>\r\n                    <input pInputText id=\"brand\" type=\"text\" formControlName=\"brand\" placeholder=\"Brand'\"\r\n                        class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">group</span>Customer Group\r\n                    </label>\r\n                    <input pInputText id=\"customer_group\" type=\"text\" formControlName=\"customer_group\"\r\n                        placeholder=\"Customer Group'\" class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">emoji_events</span>Ranking\r\n                    </label>\r\n                    <input pInputText id=\"ranking\" type=\"text\" formControlName=\"ranking\" placeholder=\"Ranking'\"\r\n                        class=\"h-3rem w-full\" />\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">check_circle</span>Status\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['activityStatus']\" formControlName=\"activity_status\"\r\n                        placeholder=\"Select a Status\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['activity_status'].errors }\">\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"submitted && f['activity_status'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['activity_status'].errors &&\r\n                                f['activity_status'].errors['required']\r\n                              \">\r\n                            Status is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-4 md:col-4 sm:col-6\">\r\n                <div class=\"input-main\">\r\n                    <label class=\"flex align-items-center gap-1 mb-2 font-medium\">\r\n                        <span class=\"material-symbols-rounded text-2xl text-600\">label</span>Type\r\n                        <span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <p-dropdown [options]=\"dropdowns['activityInitiatorCode']\" formControlName=\"initiator_code\"\r\n                        placeholder=\"Select a Type\" optionLabel=\"label\" optionValue=\"value\" styleClass=\"h-3rem w-full\"\r\n                        [ngClass]=\"{ 'is-invalid': submitted && f['initiator_code'].errors }\">\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"submitted && f['initiator_code'].errors\" class=\"p-error\">\r\n                        <div *ngIf=\"\r\n                                submitted &&\r\n                                f['initiator_code'].errors &&\r\n                                f['initiator_code'].errors['required']\r\n                              \">\r\n                            Type is required.\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center p-3 gap-3 mt-1\">\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n</div>\r\n\r\n<div class=\"p-3 w-full surface-card border-round shadow-1 mt-5\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Notes</h4>\r\n\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <p-button label=\"Add\" (click)=\"showDialog('right')\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n                [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n\r\n            <p-multiSelect [options]=\"NotesCols\" [(ngModel)]=\"selectedNotesColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n\r\n        <p-table [value]=\"notedetails\" dataKey=\"bp_id\" [rows]=\"10\" styleClass=\"w-full\" [paginator]=\"true\"\r\n            [scrollable]=\"true\" [reorderableColumns]=\"true\" (onColReorder)=\"onNotesColumnReorder($event)\"\r\n            responsiveLayout=\"scroll\" class=\"scrollable-table\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg\" (click)=\"customSort('note', notedetails, 'Notes')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Note\r\n                            <i *ngIf=\"sortFieldNotes === 'note'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrderNotes === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                            <i *ngIf=\"sortFieldNotes !== 'note'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedNotesColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn\r\n                            (click)=\"customSort(col.field, notedetails, 'Notes')\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortFieldNotes === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrderNotes === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                                <i *ngIf=\"sortFieldNotes !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                    <th class=\"border-round-right-lg\">Actions</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-notes>\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg note-text\" [innerHTML]=\"notes?.note || '-'\">\r\n                    </td>\r\n                    <ng-container *ngFor=\"let col of selectedNotesColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'updatedAt'\">\r\n                                    {{ notes?.updatedAt | date:'dd-MM-yyyy HH:mm:ss' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'updatedBy'\">\r\n                                    {{ notes?.updatedBy || '-'}}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                    <td class=\"border-round-right-lg\">\r\n                        <button pButton type=\"button\" class=\"mr-2\" icon=\"pi pi-pencil\" pTooltip=\"Edit\"\r\n                            (click)=\"editNote(notes)\"></button>\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation(); confirmRemove(notes);\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg\">No notes found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\" class=\"border-round-left-lg\">Loading notes data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<p-dialog [modal]=\"true\" [(visible)]=\"notevisible\" [style]=\"{ width: '50rem' }\" [position]=\"'right'\" [draggable]=\"false\"\r\n    class=\"note-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Note</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"NoteForm\" class=\"relative flex flex-column gap-1\">\r\n        <div class=\"field flex align-items-center text-base\">\r\n            <div class=\"form-input flex-1 relative\">\r\n                <p-editor formControlName=\"note\" placeholder=\"Enter text here...\" [style]=\"{ height: '200px' }\"\r\n                    [ngClass]=\"{ 'is-invalid': notesubmitted && fNote['note'].errors }\" />\r\n                <div *ngIf=\"notesubmitted && fNote['note'].errors\"\r\n                    class=\"invalid-feedback absolute top-0 bottom-0 h-1rem m-auto\">\r\n                    <div *ngIf=\"fNote['note'].errors['required']\">Note is required.</div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex align-items-center gap-3 mt-1\">\r\n            <button pButton type=\"button\" label=\"Cancel\"\r\n                class=\"p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem\"\r\n                (click)=\"notevisible = false\"></button>\r\n            <button pButton type=\"submit\" label=\"Save\" class=\"p-button-rounded justify-content-center w-9rem h-3rem\"\r\n                (click)=\"onNoteSubmit()\"></button>\r\n        </div>\r\n    </form>\r\n\r\n</p-dialog>"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,OAAO,EAAEC,SAAS,EAAcC,MAAM,EAAEC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAGtE,SACEC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,YAAY,QACP,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;ICFHC,EAJhB,CAAAC,cAAA,cAA6D,cACV,cACnB,gBACqD,eACR;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,WAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,GAC/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,cAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBAChF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAEhGF,EAFgG,CAAAG,YAAA,EAAM,EAC5F,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,yBACnF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAE/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBACvF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEJH,EADJ,CAAAC,cAAA,eAA2E,aAEJ;IAC/DD,EAAA,CAAAE,MAAA,IACJ;IAGZF,EAHY,CAAAG,YAAA,EAAI,EACF,EACJ,EACJ;IAKMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBAC/E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEJH,EADJ,CAAAC,cAAA,eAA2E,aAEJ;IAC/DD,EAAA,CAAAE,MAAA,IACJ;IAGZF,EAHY,CAAAG,YAAA,EAAI,EACF,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,kBACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAEzC;IAEpBF,EAFoB,CAAAG,YAAA,EAAM,EAChB,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,0BAC7E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAEzC;IAEpBF,EAFoB,CAAAG,YAAA,EAAM,EAChB,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBAC7E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAC/C;IAEdF,EAFc,CAAAG,YAAA,EAAM,EACV,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,wBACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAGrD;;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,uBACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAGrD;;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,eACvF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,IAGrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBACqD,gBACR;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,eAC3F;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,KAGrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACqD,iBACR;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,yBAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACqD,iBACR;IAAAD,EAAA,CAAAE,MAAA,qBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,kBACrF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACqD,iBACR;IAAAD,EAAA,CAAAE,MAAA,qBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBACrF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAErD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACqD,iBACR;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,eAC9E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KAErD;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBACqD,iBACR;IAAAD,EAAA,CAAAE,MAAA,oBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,4BACpF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,KACrD;IAGZF,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;;;;IAzK2DH,EAAA,CAAAI,SAAA,GAC/C;IAD+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAC,WAAA,SAC/C;IAQ+CR,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAE,OAAA,SAAmC;IAQnCT,EAAA,CAAAI,SAAA,GAE/C;IAF+CJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAI,oBAAA,yBAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAI,aAAA,SAE/C;IASCX,EAAA,CAAAI,SAAA,GAA0F;IAA1FJ,EAAA,CAAAY,UAAA,gCAAAN,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAM,gBAAA,kBAAAP,MAAA,CAAAC,eAAA,CAAAM,gBAAA,CAAAC,UAAA,iBAAAd,EAAA,CAAAe,aAAA,CAA0F;IAEzFf,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAgB,kBAAA,OAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAM,gBAAA,kBAAAP,MAAA,CAAAC,eAAA,CAAAM,gBAAA,CAAAI,YAAA,cACJ;IAWGjB,EAAA,CAAAI,SAAA,GAAyH;IAAzHJ,EAAA,CAAAY,UAAA,iCAAAN,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAW,wBAAA,kBAAAZ,MAAA,CAAAC,eAAA,CAAAW,wBAAA,CAAAC,eAAA,kBAAAb,MAAA,CAAAC,eAAA,CAAAW,wBAAA,CAAAC,eAAA,qBAAAb,MAAA,CAAAC,eAAA,CAAAW,wBAAA,CAAAC,eAAA,IAAAL,UAAA,iBAAAd,EAAA,CAAAe,aAAA,CAAyH;IAExHf,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAgB,kBAAA,OAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAW,wBAAA,kBAAAZ,MAAA,CAAAC,eAAA,CAAAW,wBAAA,CAAAD,YAAA,cACJ;IASiDjB,EAAA,CAAAI,SAAA,GAEzC;IAFyCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAI,oBAAA,qBAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAa,mBAAA,SAEzC;IAQyCpB,EAAA,CAAAI,SAAA,GAEzC;IAFyCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAI,oBAAA,wBAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAc,gBAAA,SAEzC;IAQyCrB,EAAA,CAAAI,SAAA,GAC/C;IAD+CJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAe,MAAA,SAC/C;IAQ+CtB,EAAA,CAAAI,SAAA,GAGrD;IAHqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAgB,UAAA,IAAAvB,EAAA,CAAAwB,WAAA,SAAAlB,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAgB,UAAA,mDAGrD;IAQqDvB,EAAA,CAAAI,SAAA,GAGrD;IAHqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAkB,QAAA,IAAAzB,EAAA,CAAAwB,WAAA,SAAAlB,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAkB,QAAA,mDAGrD;IAQqDzB,EAAA,CAAAI,SAAA,GAGrD;IAHqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAmB,sBAAA,kBAAApB,MAAA,CAAAC,eAAA,CAAAmB,sBAAA,CAAAT,YAAA,cAGrD;IAQqDjB,EAAA,CAAAI,SAAA,GAGrD;IAHqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAoB,KAAA,cAGrD;IAQqD3B,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAqB,cAAA,cACrD;IAQqD5B,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAsB,OAAA,cACrD;IAQqD7B,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAgB,kBAAA,KAAAV,MAAA,CAAAI,oBAAA,mBAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAuB,eAAA,cAErD;IAQqD9B,EAAA,CAAAI,SAAA,GAErD;IAFqDJ,EAAA,CAAAgB,kBAAA,KAAAV,MAAA,CAAAI,oBAAA,0BAAAJ,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAwB,cAAA,cAErD;IAQqD/B,EAAA,CAAAI,SAAA,GACrD;IADqDJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAC,eAAA,kBAAAD,MAAA,CAAAC,eAAA,CAAAyB,iBAAA,cACrD;;;;;IAmBYhC,EAAA,CAAAC,cAAA,WAAgC;IAACD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAgB,kBAAA,QAAAiB,OAAA,CAAAhB,YAAA,KAAyB;;;;;IAD1DjB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAkC,UAAA,IAAAC,2DAAA,mBAAgC;;;;IAD1BnC,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAA4B,OAAA,CAAAG,KAAA,CAAgB;IACfpC,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAY,UAAA,SAAAqB,OAAA,CAAAhB,YAAA,CAAuB;;;;;IAIlCjB,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAkC,UAAA,IAAAG,kDAAA,kBAIQ;IAGZrC,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,0BAAAC,MAAA,IAAAlC,MAAA,CAAAiC,CAAA,0BAAAC,MAAA,aAID;;;;;IAuBGxC,EAAA,CAAAC,cAAA,WAAyB;IAACD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAgB,kBAAA,QAAAyB,OAAA,CAAAC,KAAA,KAAkB;;;;;IAC5C1C,EAAA,CAAAC,cAAA,WAA0B;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAgB,kBAAA,QAAAyB,OAAA,CAAAE,MAAA,KAAmB;;;;;;IAL9C3C,EADJ,CAAAC,cAAA,cAA2C,gBAGqB;IADxDD,EAAA,CAAA4C,UAAA,oBAAAC,6EAAA;MAAA,MAAAJ,OAAA,GAAAzC,EAAA,CAAA8C,aAAA,CAAAC,GAAA,EAAAC,IAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAiD,aAAA;MAAA,OAAAjD,EAAA,CAAAkD,WAAA,CAAU5C,MAAA,CAAA6C,eAAA,CAAAV,OAAA,CAAAL,KAAA,CAA2B;IAAA,EAAC;IAD1CpC,EAAA,CAAAG,YAAA,EAE4D;IAC5DH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtDH,EADA,CAAAkC,UAAA,IAAAkB,2DAAA,mBAAyB,IAAAC,2DAAA,mBACC;IAC9BrD,EAAA,CAAAG,YAAA,EAAM;;;;;IANqBH,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAAgD,UAAA,CAAAb,OAAA,CAAAL,KAAA,EAAkC;IAGnDpC,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAuD,kBAAA,KAAAd,OAAA,CAAAL,KAAA,QAAAK,OAAA,CAAAxB,YAAA,KAAyC;IACxCjB,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAY,UAAA,SAAA6B,OAAA,CAAAC,KAAA,CAAgB;IAChB1C,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAY,UAAA,SAAA6B,OAAA,CAAAE,MAAA,CAAiB;;;;;IAKhC3C,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAkC,UAAA,IAAAsB,kDAAA,kBAIQ;IAGZxD,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,0BAAAC,MAAA,IAAAlC,MAAA,CAAAiC,CAAA,0BAAAC,MAAA,aAID;;;;;IAeLxC,EAAA,CAAAC,cAAA,UAIY;IACRD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA8D;IAC1DD,EAAA,CAAAkC,UAAA,IAAAuB,kDAAA,kBAIY;IAGhBzD,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAIG;IAJHJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,YAAAC,MAAA,IAAAlC,MAAA,CAAAiC,CAAA,YAAAC,MAAA,aAIG;;;;;IAkBTxC,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,8BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAA0E;IACtED,EAAA,CAAAkC,UAAA,IAAAwB,kDAAA,kBAIQ;IAGZ1D,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,wBAAAC,MAAA,IAAAlC,MAAA,CAAAiC,CAAA,wBAAAC,MAAA,aAID;;;;;IAmFLxC,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,4BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAAkC,UAAA,IAAAyB,mDAAA,kBAIQ;IAGZ3D,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,oBAAAC,MAAA,IAAAlC,MAAA,CAAAiC,CAAA,oBAAAC,MAAA,aAID;;;;;IAiBLxC,EAAA,CAAAC,cAAA,UAIQ;IACJD,EAAA,CAAAE,MAAA,0BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPVH,EAAA,CAAAC,cAAA,cAAqE;IACjED,EAAA,CAAAkC,UAAA,IAAA0B,mDAAA,kBAIQ;IAGZ5D,EAAA,CAAAG,YAAA,EAAM;;;;IAPIH,EAAA,CAAAI,SAAA,EAID;IAJCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,mBAAAC,MAAA,IAAAlC,MAAA,CAAAiC,CAAA,mBAAAC,MAAA,aAID;;;;;;IAxMLxC,EALpB,CAAAC,cAAA,eAA6D,cAChB,cACU,cACnB,gBAC0C,eACD;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,eAC9E;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAC,cAAA,qBAIiF;;IAC7ED,EAAA,CAAAkC,UAAA,KAAA2B,oDAAA,0BAA2C;IAI/C7D,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAkC,UAAA,KAAA4B,4CAAA,kBAA4E;IAUpF9D,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,gBACtE;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAC,cAAA,qBAIiF;;IAC7ED,EAAA,CAAAkC,UAAA,KAAA6B,oDAAA,0BAA2C;IAU/C/D,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAkC,UAAA,KAAA8B,4CAAA,kBAA4E;IAUpFhE,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBACxE;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAiE,SAAA,iBAC2F;IAC3FjE,EAAA,CAAAkC,UAAA,KAAAgC,4CAAA,kBAA8D;IAUtElE,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,iBACxE;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAiE,SAAA,sBAIa;IACbjE,EAAA,CAAAkC,UAAA,KAAAiC,4CAAA,kBAA0E;IAUlFnE,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,yBACxE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAiE,SAAA,sBAGa;IAErBjE,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,eACxE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAiE,SAAA,iBAC4B;IAEpCjE,EADI,CAAAG,YAAA,EAAM,EACJ;IAKMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,uBAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAiE,SAAA,sBAC2F;IAEnGjE,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,sBAC5E;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAiE,SAAA,sBAC0F;IAElGjE,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,cACtF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAiE,SAAA,iBAC4B;IAEpCjE,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,uBACzE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAiE,SAAA,iBAC0D;IAElEjE,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,gBAChF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAiE,SAAA,iBAC4B;IAEpCjE,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,eAA+C,eACnB,iBAC0C,gBACD;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,gBAC5E;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAiE,SAAA,uBAGa;IACbjE,EAAA,CAAAkC,UAAA,MAAAkC,6CAAA,kBAAsE;IAU9EpE,EADI,CAAAG,YAAA,EAAM,EACJ;IAIMH,EAHZ,CAAAC,cAAA,gBAA+C,gBACnB,kBAC0C,iBACD;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,cACrE;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,UAAC;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAC/B;IACRH,EAAA,CAAAiE,SAAA,uBAGa;IACbjE,EAAA,CAAAkC,UAAA,MAAAmC,6CAAA,kBAAqE;IAWjFrE,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAoD,mBAEvB;IAArBD,EAAA,CAAA4C,UAAA,mBAAA0B,gEAAA;MAAAtE,EAAA,CAAA8C,aAAA,CAAAyB,GAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAiD,aAAA;MAAA,OAAAjD,EAAA,CAAAkD,WAAA,CAAS5C,MAAA,CAAAkE,QAAA,EAAU;IAAA,EAAC;IAEhCxE,EAFiC,CAAAG,YAAA,EAAS,EAChC,EACH;;;;IAxNkBH,EAAA,CAAAY,UAAA,cAAAN,MAAA,CAAAmE,qBAAA,CAAmC;IAQtBzE,EAAA,CAAAI,SAAA,IAA2B;IAI7CJ,EAJkB,CAAAY,UAAA,UAAAZ,EAAA,CAAA0E,WAAA,SAAApE,MAAA,CAAAqE,SAAA,EAA2B,sBACxB,YAAArE,MAAA,CAAAsE,cAAA,CAA2B,oBAAoB,gBAAAtE,MAAA,CAAAuE,WAAA,CACD,cAAAvE,MAAA,CAAAwE,aAAA,CAA4B,wBACxE,YAAA9E,EAAA,CAAA+E,eAAA,KAAAC,GAAA,EAAA1E,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,0BAAAC,MAAA,EACqD;IAM1ExC,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,0BAAAC,MAAA,CAAoD;IAiBpCxC,EAAA,CAAAI,SAAA,GAA2B;IAI7CJ,EAJkB,CAAAY,UAAA,UAAAZ,EAAA,CAAA0E,WAAA,SAAApE,MAAA,CAAA2E,SAAA,EAA2B,sBACxB,YAAA3E,MAAA,CAAA4E,cAAA,CAA2B,oBAAoB,cAAA5E,MAAA,CAAA6E,aAAA,CACD,wBAAwB,kBAC1D,wBAAwB,YAAAnF,EAAA,CAAA+E,eAAA,KAAAC,GAAA,EAAA1E,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,0BAAAC,MAAA,EACmB;IAY1ExC,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,0BAAAC,MAAA,CAAoD;IAkBhCxC,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA+E,eAAA,KAAAC,GAAA,EAAA1E,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,YAAAC,MAAA,EAA8D;IAClFxC,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,YAAAC,MAAA,CAAsC;IAiBhCxC,EAAA,CAAAI,SAAA,GAAyC;IAGjDJ,EAHQ,CAAAY,UAAA,YAAAN,MAAA,CAAA8E,SAAA,qBAAyC,+BAEnB,YAAApF,EAAA,CAAA+E,eAAA,KAAAC,GAAA,EAAA1E,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,wBAAAC,MAAA,EAC4C;IAExExC,EAAA,CAAAI,SAAA,EAAkD;IAAlDJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,wBAAAC,MAAA,CAAkD;IAgB5CxC,EAAA,CAAAI,SAAA,GAA4C;IAEpDJ,EAFQ,CAAAY,UAAA,YAAAN,MAAA,CAAA8E,SAAA,wBAA4C,+BAEtB;IAmBOpF,EAAA,CAAAI,SAAA,IAAsB;IAC9BJ,EADQ,CAAAY,UAAA,uBAAsB,kBACb;IAQXZ,EAAA,CAAAI,SAAA,GAAsB;IAC7BJ,EADO,CAAAY,UAAA,uBAAsB,kBACZ;IAoCrCZ,EAAA,CAAAI,SAAA,IAAuC;IAE/CJ,EAFQ,CAAAY,UAAA,YAAAN,MAAA,CAAA8E,SAAA,mBAAuC,YAAApF,EAAA,CAAA+E,eAAA,KAAAC,GAAA,EAAA1E,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,oBAAAC,MAAA,EAEuB;IAEpExC,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,oBAAAC,MAAA,CAA8C;IAiBxCxC,EAAA,CAAAI,SAAA,GAA8C;IAEtDJ,EAFQ,CAAAY,UAAA,YAAAN,MAAA,CAAA8E,SAAA,0BAA8C,YAAApF,EAAA,CAAA+E,eAAA,KAAAC,GAAA,EAAA1E,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,mBAAAC,MAAA,EAEe;IAEnExC,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAgC,SAAA,IAAAhC,MAAA,CAAAiC,CAAA,mBAAAC,MAAA,CAA6C;;;;;IA6C3CxC,EAAA,CAAAiE,SAAA,YAC2F;;;;IAAvFjE,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAA+E,cAAA,yDAAkF;;;;;IACtFrF,EAAA,CAAAiE,SAAA,YAAiE;;;;;IAS7DjE,EAAA,CAAAiE,SAAA,YAC2F;;;;IAAvFjE,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAA+E,cAAA,yDAAkF;;;;;IACtFrF,EAAA,CAAAiE,SAAA,YAAoE;;;;;;IAPhFjE,EAAA,CAAAsF,uBAAA,GAAuD;IACnDtF,EAAA,CAAAC,cAAA,aAC0D;IAAtDD,EAAA,CAAA4C,UAAA,mBAAA2C,iFAAA;MAAA,MAAAC,MAAA,GAAAxF,EAAA,CAAA8C,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAApF,MAAA,GAAAN,EAAA,CAAAiD,aAAA;MAAA,OAAAjD,EAAA,CAAAkD,WAAA,CAAS5C,MAAA,CAAAqF,UAAA,CAAAH,MAAA,CAAAI,KAAA,EAAAtF,MAAA,CAAAuF,WAAA,EAAmC,OAAO,CAAC;IAAA,EAAC;IACrD7F,EAAA,CAAAC,cAAA,cAAoD;IAChDD,EAAA,CAAAE,MAAA,GACA;IAEAF,EAFA,CAAAkC,UAAA,IAAA4D,gEAAA,gBACuF,IAAAC,gEAAA,gBACvB;IAExE/F,EADI,CAAAG,YAAA,EAAM,EACL;;;;;;IARDH,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAY,UAAA,oBAAA4E,MAAA,CAAAI,KAAA,CAA6B;IAGzB5F,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAgB,kBAAA,MAAAwE,MAAA,CAAAQ,MAAA,MACA;IAAIhG,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA2F,cAAA,KAAAT,MAAA,CAAAI,KAAA,CAAkC;IAElC5F,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA2F,cAAA,KAAAT,MAAA,CAAAI,KAAA,CAAkC;;;;;;IAhBlD5F,EADJ,CAAAC,cAAA,SAAI,aACkG;IAAnDD,EAAA,CAAA4C,UAAA,mBAAAsD,kEAAA;MAAAlG,EAAA,CAAA8C,aAAA,CAAAqD,GAAA;MAAA,MAAA7F,MAAA,GAAAN,EAAA,CAAAiD,aAAA;MAAA,OAAAjD,EAAA,CAAAkD,WAAA,CAAS5C,MAAA,CAAAqF,UAAA,CAAW,MAAM,EAAArF,MAAA,CAAAuF,WAAA,EAAe,OAAO,CAAC;IAAA,EAAC;IAC7F7F,EAAA,CAAAC,cAAA,cAAoD;IAChDD,EAAA,CAAAE,MAAA,aACA;IAEAF,EAFA,CAAAkC,UAAA,IAAAkE,iDAAA,gBACuF,IAAAC,iDAAA,gBAC1B;IAErErG,EADI,CAAAG,YAAA,EAAM,EACL;IAELH,EAAA,CAAAkC,UAAA,IAAAoE,4DAAA,2BAAuD;IAWvDtG,EAAA,CAAAC,cAAA,aAAkC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAC7CF,EAD6C,CAAAG,YAAA,EAAK,EAC7C;;;;IAlBWH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA2F,cAAA,YAA+B;IAE/BjG,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA2F,cAAA,YAA+B;IAIbjG,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAAiG,oBAAA,CAAuB;;;;;IAsBzCvG,EAAA,CAAAsF,uBAAA,GAA0C;IACtCtF,EAAA,CAAAE,MAAA,GACJ;;;;;;IADIF,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAgB,kBAAA,MAAAhB,EAAA,CAAAwG,WAAA,OAAAC,SAAA,kBAAAA,SAAA,CAAAC,SAAA,8BACJ;;;;;IAEA1G,EAAA,CAAAsF,uBAAA,GAA0C;IACtCtF,EAAA,CAAAE,MAAA,GACJ;;;;;IADIF,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAgB,kBAAA,OAAAyF,SAAA,kBAAAA,SAAA,CAAAE,SAAA,cACJ;;;;;IATZ3G,EAAA,CAAAsF,uBAAA,GAAuD;IACnDtF,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAsF,uBAAA,OAAqC;IAKjCtF,EAJA,CAAAkC,UAAA,IAAA0E,2EAAA,2BAA0C,IAAAC,2EAAA,2BAIA;;IAKlD7G,EAAA,CAAAG,YAAA,EAAK;;;;;IAVaH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAY,UAAA,aAAAkG,OAAA,CAAAlB,KAAA,CAAsB;IACjB5F,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAY,UAAA,6BAAyB;IAIzBZ,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAY,UAAA,6BAAyB;;;;;;IAVxDZ,EAAA,CAAAC,cAAA,aAA2B;IACvBD,EAAA,CAAAiE,SAAA,aACK;IACLjE,EAAA,CAAAkC,UAAA,IAAA6E,4DAAA,2BAAuD;IAenD/G,EADJ,CAAAC,cAAA,aAAkC,iBAEA;IAA1BD,EAAA,CAAA4C,UAAA,mBAAAoE,sEAAA;MAAA,MAAAP,SAAA,GAAAzG,EAAA,CAAA8C,aAAA,CAAAmE,GAAA,EAAAvB,SAAA;MAAA,MAAApF,MAAA,GAAAN,EAAA,CAAAiD,aAAA;MAAA,OAAAjD,EAAA,CAAAkD,WAAA,CAAS5C,MAAA,CAAA4G,QAAA,CAAAT,SAAA,CAAe;IAAA,EAAC;IAACzG,EAAA,CAAAG,YAAA,EAAS;IACvCH,EAAA,CAAAC,cAAA,iBAC8D;IAA1DD,EAAA,CAAA4C,UAAA,mBAAAuE,sEAAAC,MAAA;MAAA,MAAAX,SAAA,GAAAzG,EAAA,CAAA8C,aAAA,CAAAmE,GAAA,EAAAvB,SAAA;MAAA,MAAApF,MAAA,GAAAN,EAAA,CAAAiD,aAAA;MAASmE,MAAA,CAAAC,eAAA,EAAwB;MAAA,OAAArH,EAAA,CAAAkD,WAAA,CAAE5C,MAAA,CAAAgH,aAAA,CAAAb,SAAA,CAAoB;IAAA,EAAE;IAErEzG,EAFsE,CAAAG,YAAA,EAAS,EACtE,EACJ;;;;;IAtBwDH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAY,UAAA,eAAA6F,SAAA,kBAAAA,SAAA,CAAAc,IAAA,UAAAvH,EAAA,CAAAwH,cAAA,CAAgC;IAE3DxH,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAAiG,oBAAA,CAAuB;;;;;IAyBrDvG,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAChEF,EADgE,CAAAG,YAAA,EAAK,EAChE;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IACnFF,EADmF,CAAAG,YAAA,EAAK,EACnF;;;;;IAQbH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAUDH,EAAA,CAAAC,cAAA,UAA8C;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFzEH,EAAA,CAAAC,cAAA,cACmE;IAC/DD,EAAA,CAAAkC,UAAA,IAAAuF,2CAAA,kBAA8C;IAClDzH,EAAA,CAAAG,YAAA,EAAM;;;;IADIH,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAoH,KAAA,SAAAlF,MAAA,aAAsC;;;AD7dhE,OAAM,MAAOmF,qBAAqB;EAsDhCC,YACUC,WAAwB,EACxBC,iBAAoC,EACpCC,cAA8B,EAC9BC,mBAAwC;IAHxC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAzDrB,KAAAC,aAAa,GAAG,IAAI5I,OAAO,EAAQ;IACpC,KAAAkB,eAAe,GAAQ,IAAI;IAE3B,KAAAqE,cAAc,GAAG,KAAK;IACtB,KAAAE,aAAa,GAAG,IAAIzF,OAAO,EAAU;IAErC,KAAA6F,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAI9F,OAAO,EAAU;IAErC,KAAA6I,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,IAAI9I,OAAO,EAAU;IACrC,KAAA+I,cAAc,GAAQ,EAAE;IACzB,KAAA9F,SAAS,GAAG,KAAK;IACjB,KAAA+F,MAAM,GAAG,KAAK;IAEd,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAA3C,WAAW,GAAQ,IAAI;IACvB,KAAA4C,WAAW,GAAY,KAAK;IAC5B,KAAAC,YAAY,GAAW,OAAO;IAC9B,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAW,EAAE;IAEvB,KAAAzD,SAAS,GAA0B;MACxC0D,oBAAoB,EAAE,EAAE;MACxBC,cAAc,EAAE,EAAE;MAClBC,gBAAgB,EAAE,EAAE;MACpBC,mBAAmB,EAAE,EAAE;MACvBC,qBAAqB,EAAE;KACxB;IAEM,KAAAzE,qBAAqB,GAAc,IAAI,CAACoD,WAAW,CAACsB,KAAK,CAAC;MAC/DC,qBAAqB,EAAE,CAAC,EAAE,EAAE,CAAChK,UAAU,CAACiK,QAAQ,CAAC,CAAC;MAClDC,qBAAqB,EAAE,CAAC,EAAE,EAAE,CAAClK,UAAU,CAACiK,QAAQ,CAAC,CAAC;MAClD5I,OAAO,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACiK,QAAQ,CAAC,CAAC;MACpCjI,mBAAmB,EAAE,CAAC,EAAE,EAAE,CAAChC,UAAU,CAACiK,QAAQ,CAAC,CAAC;MAChDhI,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBE,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdE,KAAK,EAAE,CAAC,EAAE,CAAC;MACXE,OAAO,EAAE,CAAC,EAAE,CAAC;MACbD,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBG,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC3C,UAAU,CAACiK,QAAQ,CAAC,CAAC;MAC3CvH,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC1C,UAAU,CAACiK,QAAQ,CAAC;KAC5C,CAAC;IAEK,KAAAE,QAAQ,GAAc,IAAI,CAAC1B,WAAW,CAACsB,KAAK,CAAC;MAClD5B,IAAI,EAAE,CAAC,EAAE,EAAE,CAACnI,UAAU,CAACiK,QAAQ,CAAC;KACjC,CAAC;IASM,KAAAG,qBAAqB,GAAkB,EAAE;IAE1C,KAAAC,SAAS,GAAkB,CAChC;MAAE7D,KAAK,EAAE,WAAW;MAAEI,MAAM,EAAE;IAAiB,CAAE,EACjD;MAAEJ,KAAK,EAAE,WAAW;MAAEI,MAAM,EAAE;IAAY,CAAE,CAC7C;IAED,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAZ,cAAc,GAAW,CAAC;IAyc1B,KAAAR,WAAW,GAAG,CAAC6E,CAAM,EAAEC,CAAM,KAAKD,CAAC,KAAKC,CAAC;EAndtC;EAYHC,QAAQA,CAAA;IACN;IACAC,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGC,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC;MAC5D,IAAIF,cAAc,EAAE;QAClB,IAAI,CAAC/B,cAAc,CAACkC,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAEL;SACT,CAAC;QACFC,cAAc,CAACK,UAAU,CAAC,aAAa,CAAC;MAC1C;IACF,CAAC,EAAE,GAAG,CAAC;IACP,IAAI,CAACC,oBAAoB,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;IAClE,IAAI,CAACA,oBAAoB,CACvB,sBAAsB,EACtB,kCAAkC,CACnC;IACD,IAAI,CAACA,oBAAoB,CACvB,kBAAkB,EAClB,kCAAkC,CACnC;IACD,IAAI,CAACA,oBAAoB,CACvB,qBAAqB,EACrB,+BAA+B,CAChC;IACD,IAAI,CAACA,oBAAoB,CACvB,uBAAuB,EACvB,6BAA6B,CAC9B;IACD,IAAI,CAAC5F,qBAAqB,CAAC6F,GAAG,CAAC,uBAAuB,CAAC,EACnDC,YAAY,CAACC,IAAI,CACjBlL,SAAS,CAAC,IAAI,CAAC2I,aAAa,CAAC,EAC7BrI,GAAG,CAAE6K,YAAY,IAAI;MACnB,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACC,qBAAqB,CAACD,YAAY,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI,CAACxF,SAAS,GAAGxF,EAAE,CAAC,IAAI,CAAC2I,cAAc,CAAC;MAC1C;IACF,CAAC,CAAC,EACFtI,UAAU,CAAE6K,GAAG,IAAI;MACjBC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC;MAC9C,IAAI,CAAC1F,SAAS,GAAGxF,EAAE,CAAC,IAAI,CAAC2I,cAAc,CAAC;MACxC,OAAO3I,EAAE,EAAE;IACb,CAAC,CAAC,CACH,CACAqL,SAAS,EAAE;IACd,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACjD,iBAAiB,CAACkD,QAAQ,CAC5BR,IAAI,CAAClL,SAAS,CAAC,IAAI,CAAC2I,aAAa,CAAC,CAAC,CACnC6C,SAAS,CAAEG,QAAa,IAAI;MAC3B,IAAI,CAACA,QAAQ,EAAE;MACf,IAAI,CAAC3C,EAAE,GAAG2C,QAAQ,EAAEzK,WAAW;MAC/B,IAAI,CAACD,eAAe,GAAG0K,QAAQ;MAC/B,IAAI,CAACpF,WAAW,GAAGoF,QAAQ,EAAEC,KAAK;MAClC,IAAI,IAAI,CAAC3K,eAAe,EAAE;QACxB,IAAI,CAAC4K,iBAAiB,CAAC,IAAI,CAAC5K,eAAe,CAAC;MAC9C;IACF,CAAC,CAAC;IAEJ,IAAI,CAACiJ,qBAAqB,GAAG,IAAI,CAACC,SAAS;EAC7C;EAEA,IAAIlD,oBAAoBA,CAAA;IACtB,OAAO,IAAI,CAACiD,qBAAqB;EACnC;EAEA,IAAIjD,oBAAoBA,CAAC6E,GAAU;IACjC,IAAI,CAAC5B,qBAAqB,GAAG,IAAI,CAACC,SAAS,CAAC4B,MAAM,CAAEC,GAAG,IACrDF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAClB;EACH;EAEAE,oBAAoBA,CAACC,KAAU;IAC7B,MAAMC,UAAU,GAAG,IAAI,CAACjC,SAAS,CAACgC,KAAK,CAACE,SAAS,CAAC;IAClD,IAAI,CAAClC,SAAS,CAACmC,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IACzC,IAAI,CAAClC,SAAS,CAACmC,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EACvD;EAEA/F,UAAUA,CAACC,KAAa,EAAEkG,IAAW,EAAEC,IAAa;IAClD,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB,IAAI,CAAC9F,cAAc,GAAGL,KAAK;MAC3B,IAAI,CAACP,cAAc,GAAG,IAAI,CAACA,cAAc,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC1D;IAEAyG,IAAI,CAACE,IAAI,CAAC,CAACtC,CAAC,EAAEC,CAAC,KAAI;MACjB,MAAMsC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACxC,CAAC,EAAE9D,KAAK,CAAC;MAC9C,MAAMuG,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACvC,CAAC,EAAE/D,KAAK,CAAC;MAE9C,IAAIwG,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAGH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MAE5D,OAAO,IAAI,CAAC9G,cAAc,GAAG+G,MAAM;IACrC,CAAC,CAAC;EACJ;EAEAF,gBAAgBA,CAACJ,IAAS,EAAElG,KAAa;IACvC,IAAI,CAACkG,IAAI,IAAI,CAAClG,KAAK,EAAE,OAAO,IAAI;IAEhC,IAAIA,KAAK,CAAC0G,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOR,IAAI,CAAClG,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,IAAI2G,MAAM,GAAG3G,KAAK,CAAC4G,KAAK,CAAC,GAAG,CAAC;MAC7B,IAAIC,KAAK,GAAGX,IAAI;MAChB,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAID,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI;QAC9BA,KAAK,GAAGA,KAAK,CAACF,MAAM,CAACG,CAAC,CAAC,CAAC;MAC1B;MACA,OAAOD,KAAK;IACd;EACF;EAEApC,oBAAoBA,CAACuC,MAAc,EAAEb,IAAY;IAC/C,IAAI,CAACjE,iBAAiB,CACnB+E,0BAA0B,CAACd,IAAI,CAAC,CAChCjB,SAAS,CAAEgC,GAAQ,IAAI;MACtB,IAAI,CAAC1H,SAAS,CAACwH,MAAM,CAAC,GACpBE,GAAG,EAAEhB,IAAI,EAAEtM,GAAG,CAAEuN,IAAS,KAAM;QAC7BC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBR,KAAK,EAAEM,IAAI,CAACG;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEAxM,oBAAoBA,CAACyM,WAAmB,EAAEV,KAAa;IACrD,MAAMzJ,IAAI,GAAG,IAAI,CAACoC,SAAS,CAAC+H,WAAW,CAAC,EAAEC,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAACZ,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOzJ,IAAI,EAAEgK,KAAK,IAAIP,KAAK;EAC7B;EAEAvF,QAAQA,CAACK,IAAS;IAChB,IAAI,CAACkB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACI,UAAU,GAAGtB,IAAI,EAAEzG,UAAU;IAClC,IAAI,CAACyI,QAAQ,CAAC+D,UAAU,CAAC/F,IAAI,CAAC;EAChC;EAEA4D,iBAAiBA,CAACH,QAAa;IAC7B,IAAI,CAACuC,gBAAgB,GAAG;MACtBnE,qBAAqB,EAAE4B,QAAQ,EAAE5B,qBAAqB;MACtDE,qBAAqB,EAAE0B,QAAQ,EAAE1B,qBAAqB;MACtD7I,OAAO,EAAEuK,QAAQ,EAAEvK,OAAO;MAC1BW,mBAAmB,EAAE4J,QAAQ,EAAE5J,mBAAmB;MAClDC,gBAAgB,EAAE2J,QAAQ,EAAE3J,gBAAgB;MAC5CC,MAAM,EAAE0J,QAAQ,EAAE1J,MAAM;MACxBC,UAAU,EAAEyJ,QAAQ,EAAEzJ,UAAU,GAAG,IAAIiM,IAAI,CAACxC,QAAQ,EAAEzJ,UAAU,CAAC,GAAG,IAAI;MACxEE,QAAQ,EAAEuJ,QAAQ,EAAEvJ,QAAQ,GAAG,IAAI+L,IAAI,CAACxC,QAAQ,EAAEvJ,QAAQ,CAAC,GAAG,IAAI;MAClEgM,cAAc,EAAEzC,QAAQ,EAAEyC,cAAc;MACxC9L,KAAK,EAAEqJ,QAAQ,EAAErJ,KAAK;MACtBC,cAAc,EAAEoJ,QAAQ,EAAEpJ,cAAc;MACxCC,OAAO,EAAEmJ,QAAQ,EAAEnJ,OAAO;MAC1BE,cAAc,EAAEiJ,QAAQ,EAAEjJ,cAAc;MACxCD,eAAe,EAAEkJ,QAAQ,EAAElJ;KAC5B;IAED,IAAI,CAACyG,MAAM,GAAGyC,QAAQ,CAAClK,UAAU;IACjC,IAAI,CAAC2D,qBAAqB,CAAC6I,UAAU,CAAC,IAAI,CAACC,gBAAgB,CAAC;EAC9D;EAEQxC,YAAYA,CAAA;IAClB,IAAI,CAACpG,SAAS,GAAGpF,MAAM,CACrBE,EAAE,CAAC,IAAI,CAAC2I,cAAc,CAAC;IAAE;IACzB,IAAI,CAACtD,aAAa,CAAC0F,IAAI,CACrB9K,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACgF,cAAc,GAAG,IAAK,CAAC,EACvCjF,SAAS,CAAE+N,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,CAAC,iCAAiC,GAAG,QAAQ;QAC7C,CAAC,iCAAiC,GAAG,QAAQ;QAC7C,CAAC,WAAW,GAAG,OAAO;QACtB,CAAC,WAAW,GAAG,YAAY;QAC3B,CAAC,WAAW,GAAG,WAAW;QAC1B,CAAC,WAAW,GAAG;OAChB;MAED,IAAID,IAAI,EAAE;QACRC,MAAM,CAAC,oCAAoC,CAAC,GAAGD,IAAI;QACnDC,MAAM,CAAC,2CAA2C,CAAC,GAAGD,IAAI;MAC5D;MACA,OAAO,IAAI,CAAC5F,iBAAiB,CAAC8F,WAAW,CAACD,MAAM,CAAC,CAACnD,IAAI,CACpDhL,GAAG,CAAEsM,IAAS,IAAI;QAChB,OAAOA,IAAI,IAAI,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,EACFlM,GAAG,CAAC,MAAO,IAAI,CAACgF,cAAc,GAAG,KAAM,CAAC,EACxC9E,UAAU,CAAE+K,KAAK,IAAI;QACnB,IAAI,CAACjG,cAAc,GAAG,KAAK;QAC3B,OAAOnF,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEQiL,qBAAqBA,CAACmD,IAAY;IACxC,IAAI,CAAC5I,SAAS,GAAG,IAAI,CAACE,aAAa,CAACqF,IAAI,CACtC3K,SAAS,CAAC,EAAE,CAAC,EACbE,YAAY,CAAC,GAAG,CAAC,EACjBL,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACsF,cAAc,GAAG,IAAK,CAAC,EACvCvF,SAAS,CAAE+N,IAAY,IAAI;MACzB,MAAMC,MAAM,GAAQ;QAClB,6BAA6B,EAAEE,IAAI;QACnC,kEAAkE,EAChE;OACH;MAED,IAAIH,IAAI,EAAE;QACRC,MAAM,CACJ,6DAA6D,CAC9D,GAAGD,IAAI;QACRC,MAAM,CACJ,oEAAoE,CACrE,GAAGD,IAAI;MACV;MAEA,OAAO,IAAI,CAAC5F,iBAAiB,CAACgG,kBAAkB,CAACH,MAAM,CAAC,CAACnD,IAAI,CAC3DhL,GAAG,CAAEyL,QAAe,IAAKA,QAAQ,IAAI,EAAE,CAAC,EACxCrL,GAAG,CAAEmO,QAAe,IAAI;QACtB,IAAI,CAAC7I,cAAc,GAAG,KAAK;MAC7B,CAAC,CAAC,EACFpF,UAAU,CAAE+K,KAAK,IAAI;QACnBD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC3F,cAAc,GAAG,KAAK;QAC3B,OAAOzF,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEA0D,eAAeA,CAACmF,EAAU;IACxB,MAAM0F,OAAO,GAAG,IAAI,CAACvJ,qBAAqB,CAAC6F,GAAG,CAAC,uBAAuB,CAAC;IACvE,IAAI2D,YAAY,GAAGD,OAAO,EAAEvB,KAAK,IAAI,EAAE;IAEvC,IAAIwB,YAAY,CAAC1C,QAAQ,CAACjD,EAAE,CAAC,EAAE;MAC7B2F,YAAY,GAAGA,YAAY,CAAC5C,MAAM,CAAE6C,CAAM,IAAKA,CAAC,KAAK5F,EAAE,CAAC;IAC1D,CAAC,MAAM;MACL2F,YAAY,GAAG,CAAC,GAAGA,YAAY,EAAE3F,EAAE,CAAC;IACtC;IAEA0F,OAAO,EAAEG,QAAQ,CAACF,YAAY,CAAC;EACjC;EAEA3K,UAAUA,CAACgF,EAAU;IACnB,OAAO,IAAI,CAAC7D,qBAAqB,CAAC6F,GAAG,CACnC,uBAAuB,CACxB,EAAEmC,KAAK,EAAElB,QAAQ,CAACjD,EAAE,CAAC;EACxB;EAEM8F,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChBD,KAAI,CAAC1F,aAAa,GAAG,IAAI;MACzB0F,KAAI,CAAC5F,WAAW,GAAG,IAAI;MAEvB,IAAI4F,KAAI,CAAC9E,QAAQ,CAACgF,OAAO,EAAE;QACzBF,KAAI,CAAC5F,WAAW,GAAG,IAAI;QACvB;MACF;MAEA4F,KAAI,CAACzF,UAAU,GAAG,IAAI;MACtB,MAAM6D,KAAK,GAAG;QAAE,GAAG4B,KAAI,CAAC9E,QAAQ,CAACkD;MAAK,CAAE;MAExC,MAAMX,IAAI,GAAG;QACXtL,WAAW,EAAE6N,KAAI,CAAC/F,EAAE;QACpBf,IAAI,EAAEkF,KAAK,EAAElF;OACd;MAED,IAAI8G,KAAI,CAACxF,UAAU,EAAE;QACnBwF,KAAI,CAACvG,iBAAiB,CACnB0G,UAAU,CAACH,KAAI,CAACxF,UAAU,EAAEiD,IAAI,CAAC,CACjCtB,IAAI,CAAClL,SAAS,CAAC+O,KAAI,CAACpG,aAAa,CAAC,CAAC,CACnC6C,SAAS,CAAC;UACT2D,QAAQ,EAAEA,CAAA,KAAK;YACbJ,KAAI,CAACzF,UAAU,GAAG,KAAK;YACvByF,KAAI,CAAC5F,WAAW,GAAG,KAAK;YACxB4F,KAAI,CAAC9E,QAAQ,CAACmF,KAAK,EAAE;YACrBL,KAAI,CAACtG,cAAc,CAACkC,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACFkE,KAAI,CAACvG,iBAAiB,CACnB6G,eAAe,CAACN,KAAI,CAAC/F,EAAE,CAAC,CACxBkC,IAAI,CAAClL,SAAS,CAAC+O,KAAI,CAACpG,aAAa,CAAC,CAAC,CACnC6C,SAAS,EAAE;UAChB,CAAC;UACDD,KAAK,EAAGiC,GAAQ,IAAI;YAClBuB,KAAI,CAACzF,UAAU,GAAG,KAAK;YACvByF,KAAI,CAAC5F,WAAW,GAAG,IAAI;YACvB4F,KAAI,CAACtG,cAAc,CAACkC,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN,CAAC,MAAM;QACLkE,KAAI,CAACvG,iBAAiB,CACnB8G,UAAU,CAAC9C,IAAI,CAAC,CAChBtB,IAAI,CAAClL,SAAS,CAAC+O,KAAI,CAACpG,aAAa,CAAC,CAAC,CACnC6C,SAAS,CAAC;UACT2D,QAAQ,EAAEA,CAAA,KAAK;YACbJ,KAAI,CAACzF,UAAU,GAAG,KAAK;YACvByF,KAAI,CAAC5F,WAAW,GAAG,KAAK;YACxB4F,KAAI,CAAC9E,QAAQ,CAACmF,KAAK,EAAE;YACrBL,KAAI,CAACtG,cAAc,CAACkC,GAAG,CAAC;cACtBC,QAAQ,EAAE,SAAS;cACnBC,MAAM,EAAE;aACT,CAAC;YACFkE,KAAI,CAACvG,iBAAiB,CACnB6G,eAAe,CAACN,KAAI,CAAC/F,EAAE,CAAC,CACxBkC,IAAI,CAAClL,SAAS,CAAC+O,KAAI,CAACpG,aAAa,CAAC,CAAC,CACnC6C,SAAS,EAAE;UAChB,CAAC;UACDD,KAAK,EAAGiC,GAAQ,IAAI;YAClBuB,KAAI,CAACzF,UAAU,GAAG,KAAK;YACvByF,KAAI,CAAC5F,WAAW,GAAG,IAAI;YACvB4F,KAAI,CAACtG,cAAc,CAACkC,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;aACT,CAAC;UACJ;SACD,CAAC;MACN;IAAC;EACH;EAEM3F,QAAQA,CAAA;IAAA,IAAAqK,MAAA;IAAA,OAAAP,iBAAA;MACZO,MAAI,CAACvM,SAAS,GAAG,IAAI;MAErB,IAAIuM,MAAI,CAACpK,qBAAqB,CAAC8J,OAAO,EAAE;QACtC;MACF;MAEAM,MAAI,CAACxG,MAAM,GAAG,IAAI;MAClB,MAAMoE,KAAK,GAAG;QAAE,GAAGoC,MAAI,CAACpK,qBAAqB,CAACgI;MAAK,CAAE;MAErD,MAAMX,IAAI,GAAG;QACXrL,OAAO,EAAEgM,KAAK,EAAEhM,OAAO;QACvB2I,qBAAqB,EAAEqD,KAAK,EAAErD,qBAAqB;QACnDE,qBAAqB,EAAEmD,KAAK,EAAEnD,qBAAqB;QACnDlI,mBAAmB,EAAEqL,KAAK,EAAErL,mBAAmB;QAC/CG,UAAU,EAAEkL,KAAK,EAAElL,UAAU,GAAGsN,MAAI,CAACC,UAAU,CAACrC,KAAK,CAAClL,UAAU,CAAC,GAAG,IAAI;QACxEE,QAAQ,EAAEgL,KAAK,EAAEhL,QAAQ,GAAGoN,MAAI,CAACC,UAAU,CAACrC,KAAK,CAAChL,QAAQ,CAAC,GAAG,IAAI;QAClEJ,gBAAgB,EAAEoL,KAAK,EAAEpL,gBAAgB;QACzCU,cAAc,EAAE0K,KAAK,EAAE1K,cAAc;QACrC;QACAD,eAAe,EAAE2K,KAAK,EAAE3K,eAAe;QACvCR,MAAM,EAAEmL,KAAK,EAAEnL,MAAM;QACrBK,KAAK,EAAE8K,KAAK,EAAE9K,KAAK;QACnBC,cAAc,EAAE6K,KAAK,EAAE7K,cAAc;QACrCC,OAAO,EAAE4K,KAAK,EAAE5K;OACjB;MAEDgN,MAAI,CAAC/G,iBAAiB,CACnBiH,cAAc,CAACF,MAAI,CAACtG,MAAM,EAAEuD,IAAI,CAAC,CACjCtB,IAAI,CAAClL,SAAS,CAACuP,MAAI,CAAC5G,aAAa,CAAC,CAAC,CACnC6C,SAAS,CAAC;QACTkE,IAAI,EAAG/D,QAAa,IAAI;UACtB4D,MAAI,CAAC9G,cAAc,CAACkC,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACF0E,MAAI,CAAC/G,iBAAiB,CACnB6G,eAAe,CAACE,MAAI,CAACvG,EAAE,CAAC,CACxBkC,IAAI,CAAClL,SAAS,CAACuP,MAAI,CAAC5G,aAAa,CAAC,CAAC,CACnC6C,SAAS,EAAE;UACd+D,MAAI,CAACrG,UAAU,GAAG,KAAK;QACzB,CAAC;QACDqC,KAAK,EAAGiC,GAAQ,IAAI;UAClB+B,MAAI,CAACxG,MAAM,GAAG,KAAK;UACnBwG,MAAI,CAACrG,UAAU,GAAG,IAAI;UACtBqG,MAAI,CAAC9G,cAAc,CAACkC,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEA7C,aAAaA,CAACtE,IAAS;IACrB,IAAI,CAACgF,mBAAmB,CAACiH,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChElJ,MAAM,EAAE,SAAS;MACjBmJ,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,MAAM,CAACrM,IAAI,CAAC;MACnB;KACD,CAAC;EACJ;EAEAqM,MAAMA,CAACrM,IAAS;IACd,IAAI,CAAC8E,iBAAiB,CACnBwH,UAAU,CAACtM,IAAI,CAAClC,UAAU,CAAC,CAC3B0J,IAAI,CAAClL,SAAS,CAAC,IAAI,CAAC2I,aAAa,CAAC,CAAC,CACnC6C,SAAS,CAAC;MACTkE,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACjH,cAAc,CAACkC,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACrC,iBAAiB,CACnB6G,eAAe,CAAC,IAAI,CAACrG,EAAE,CAAC,CACxBkC,IAAI,CAAClL,SAAS,CAAC,IAAI,CAAC2I,aAAa,CAAC,CAAC,CACnC6C,SAAS,EAAE;MAChB,CAAC;MACDD,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC9C,cAAc,CAACkC,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAoF,SAASA,CAACC,IAAY;IACpB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CF,IAAI,CAACG,SAAS,GAAGJ,IAAI;IACrB,OAAOC,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACK,SAAS,IAAI,EAAE;EACjD;EAEAhB,UAAUA,CAACiB,IAAU;IACnB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,EAAE,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvD,MAAMC,EAAE,GAAGH,MAAM,CAACJ,IAAI,CAACQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAClD,OAAO,GAAGL,IAAI,IAAIE,EAAE,IAAII,EAAE,EAAE;EAC9B;EAEA,IAAI/N,CAACA,CAAA;IACH,OAAO,IAAI,CAACkC,qBAAqB,CAAC+L,QAAQ;EAC5C;EAEA,IAAI9I,KAAKA,CAAA;IACP,OAAO,IAAI,CAAC6B,QAAQ,CAACiH,QAAQ;EAC/B;EAEAC,UAAUA,CAACC,QAAgB;IACzB,IAAI,CAAChI,YAAY,GAAGgI,QAAQ;IAC5B,IAAI,CAACjI,WAAW,GAAG,IAAI;IACvB,IAAI,CAACE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACY,QAAQ,CAACmF,KAAK,EAAE;EACvB;EAEAiC,UAAUA,CAAA;IACR,IAAI,CAACnI,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEAoI,OAAOA,CAAA;IACL,IAAI,CAACtO,SAAS,GAAG,KAAK;IACtB,IAAI,CAACmC,qBAAqB,CAACiK,KAAK,EAAE;EACpC;EAIAmC,WAAWA,CAAA;IACT,IAAI,CAAC5I,aAAa,CAAC+G,IAAI,EAAE;IACzB,IAAI,CAAC/G,aAAa,CAACwG,QAAQ,EAAE;EAC/B;;;uBAnhBW9G,qBAAqB,EAAA3H,EAAA,CAAA8Q,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhR,EAAA,CAAA8Q,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAlR,EAAA,CAAA8Q,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAApR,EAAA,CAAA8Q,iBAAA,CAAAK,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAArB1J,qBAAqB;MAAA2J,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtB1B5R,EAFR,CAAAC,cAAA,aAA2D,aAC4B,YAChC;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5DH,EAAA,CAAAC,cAAA,kBACyG;UAA1CD,EAAA,CAAA4C,UAAA,mBAAAkP,yDAAA;YAAA,OAASD,GAAA,CAAAlB,UAAA,EAAY;UAAA,EAAC;UACzF3Q,EAFI,CAAAG,YAAA,EACyG,EACvG;UAiLNH,EAhLA,CAAAkC,UAAA,IAAA6P,oCAAA,oBAA6D,IAAAC,qCAAA,qBAgLA;UAyNjEhS,EAAA,CAAAG,YAAA,EAAM;UAIEH,EAFR,CAAAC,cAAA,aAAgE,aACoC,YAC7C;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGrDH,EADJ,CAAAC,cAAA,cAA2C,mBAEoB;UADrCD,EAAA,CAAA4C,UAAA,mBAAAqP,0DAAA;YAAA,OAASJ,GAAA,CAAApB,UAAA,CAAW,OAAO,CAAC;UAAA,EAAC;UAAnDzQ,EAAA,CAAAG,YAAA,EAC2D;UAE3DH,EAAA,CAAAC,cAAA,yBAE+I;UAF1GD,EAAA,CAAAkS,gBAAA,2BAAAC,uEAAA/K,MAAA;YAAApH,EAAA,CAAAoS,kBAAA,CAAAP,GAAA,CAAAtL,oBAAA,EAAAa,MAAA,MAAAyK,GAAA,CAAAtL,oBAAA,GAAAa,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAkC;UAK/EpH,EAFQ,CAAAG,YAAA,EAAgB,EACd,EACJ;UAIFH,EAFJ,CAAAC,cAAA,eAAuB,mBAIoC;UADHD,EAAA,CAAA4C,UAAA,0BAAAyP,gEAAAjL,MAAA;YAAA,OAAgByK,GAAA,CAAArG,oBAAA,CAAApE,MAAA,CAA4B;UAAA,EAAC;UA6D7FpH,EA1DA,CAAAkC,UAAA,KAAAoQ,6CAAA,0BAAgC,KAAAC,6CAAA,0BA0BQ,KAAAC,6CAAA,0BA2BF,KAAAC,6CAAA,0BAKD;UAOjDzS,EAFQ,CAAAG,YAAA,EAAU,EACR,EACJ;UACNH,EAAA,CAAAC,cAAA,oBACuB;UADED,EAAA,CAAAkS,gBAAA,2BAAAQ,kEAAAtL,MAAA;YAAApH,EAAA,CAAAoS,kBAAA,CAAAP,GAAA,CAAApJ,WAAA,EAAArB,MAAA,MAAAyK,GAAA,CAAApJ,WAAA,GAAArB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAE9CpH,EAAA,CAAAkC,UAAA,KAAAyQ,6CAAA,0BAAgC;UAMxB3S,EAFR,CAAAC,cAAA,gBAAqE,eACZ,eACT;UACpCD,EAAA,CAAAiE,SAAA,oBAC0E;UAC1EjE,EAAA,CAAAkC,UAAA,KAAA0Q,qCAAA,kBACmE;UAI3E5S,EADI,CAAAG,YAAA,EAAM,EACJ;UAEFH,EADJ,CAAAC,cAAA,eAAgD,kBAGV;UAA9BD,EAAA,CAAA4C,UAAA,mBAAAiQ,wDAAA;YAAA,OAAAhB,GAAA,CAAApJ,WAAA,GAAuB,KAAK;UAAA,EAAC;UAACzI,EAAA,CAAAG,YAAA,EAAS;UAC3CH,EAAA,CAAAC,cAAA,kBAC6B;UAAzBD,EAAA,CAAA4C,UAAA,mBAAAkQ,wDAAA;YAAA,OAASjB,GAAA,CAAAzD,YAAA,EAAc;UAAA,EAAC;UAIxCpO,EAJyC,CAAAG,YAAA,EAAS,EACpC,EACH,EAEA;;;UA/fOH,EAAA,CAAAI,SAAA,GAAuC;UACqCJ,EAD5E,CAAAY,UAAA,UAAAiR,GAAA,CAAArJ,UAAA,oBAAuC,UAAAqJ,GAAA,CAAArJ,UAAA,uBAAyC,2CAC5B,iBAAwC;UAEpGxI,EAAA,CAAAI,SAAA,EAAiB;UAAjBJ,EAAA,CAAAY,UAAA,UAAAiR,GAAA,CAAArJ,UAAA,CAAiB;UAgLhBxI,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAY,UAAA,SAAAiR,GAAA,CAAArJ,UAAA,CAAgB;UAiOXxI,EAAA,CAAAI,SAAA,GAAmC;UAACJ,EAApC,CAAAY,UAAA,oCAAmC,iBAAiB;UAEzCZ,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAY,UAAA,YAAAiR,GAAA,CAAApI,SAAA,CAAqB;UAACzJ,EAAA,CAAA+S,gBAAA,YAAAlB,GAAA,CAAAtL,oBAAA,CAAkC;UAEnEvG,EAAA,CAAAY,UAAA,2IAA0I;UAOzIZ,EAAA,CAAAI,SAAA,GAAqB;UACNJ,EADf,CAAAY,UAAA,UAAAiR,GAAA,CAAAhM,WAAA,CAAqB,YAA4B,mBAAuC,oBAC1E,4BAA4B;UAqER7F,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAgT,UAAA,CAAAhT,EAAA,CAAAiT,eAAA,KAAAC,GAAA,EAA4B;UAArElT,EAAA,CAAAY,UAAA,eAAc;UAACZ,EAAA,CAAA+S,gBAAA,YAAAlB,GAAA,CAAApJ,WAAA,CAAyB;UAAmDzI,EAArB,CAAAY,UAAA,qBAAoB,oBAAoB;UAM9GZ,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAY,UAAA,cAAAiR,GAAA,CAAAtI,QAAA,CAAsB;UAGkDvJ,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAgT,UAAA,CAAAhT,EAAA,CAAAiT,eAAA,KAAAE,GAAA,EAA6B;UAC3FnT,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA+E,eAAA,KAAAC,GAAA,EAAA6M,GAAA,CAAAlJ,aAAA,IAAAkJ,GAAA,CAAAnK,KAAA,SAAAlF,MAAA,EAAmE;UACjExC,EAAA,CAAAI,SAAA,EAA2C;UAA3CJ,EAAA,CAAAY,UAAA,SAAAiR,GAAA,CAAAlJ,aAAA,IAAAkJ,GAAA,CAAAnK,KAAA,SAAAlF,MAAA,CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}