{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ProfileRoutingModule } from './profile-routing.module';\nimport { ProfileComponent } from './profile.component';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nexport class ProfileModule {\n  static {\n    this.ɵfac = function ProfileModule_Factory(t) {\n      return new (t || ProfileModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProfileModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ProfileRoutingModule, FormsModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProfileModule, {\n    declarations: [ProfileComponent],\n    imports: [CommonModule, ProfileRoutingModule, FormsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ProfileRoutingModule", "ProfileComponent", "FormsModule", "ProfileModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\profile\\profile.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { ProfileRoutingModule } from './profile-routing.module';\r\nimport { ProfileComponent } from './profile.component';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ProfileComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ProfileRoutingModule,\r\n    FormsModule\r\n  ]\r\n})\r\nexport class ProfileModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,WAAW,QAAQ,gBAAgB;;AAY5C,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBALtBJ,YAAY,EACZC,oBAAoB,EACpBE,WAAW;IAAA;EAAA;;;2EAGFC,aAAa;IAAAC,YAAA,GARtBH,gBAAgB;IAAAI,OAAA,GAGhBN,YAAY,EACZC,oBAAoB,EACpBE,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}