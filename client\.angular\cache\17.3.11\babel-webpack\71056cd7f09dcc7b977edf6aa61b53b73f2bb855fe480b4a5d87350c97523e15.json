{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/button\";\nimport * as i2 from \"primeng/inputtext\";\nexport class OpportunitiesOverviewComponent {\n  static {\n    this.ɵfac = function OpportunitiesOverviewComponent_Factory(t) {\n      return new (t || OpportunitiesOverviewComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OpportunitiesOverviewComponent,\n      selectors: [[\"app-opportunities-overview\"]],\n      decls: 171,\n      vars: 3,\n      consts: [[1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\"], [1, \"mt-6\", \"mb-1\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-3\"], [\"type\", \"button\", \"icon\", \"pi pi-check\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"p-button-icon-only\", \"surface-0\", \"h-3rem\", \"w-3rem\"], [1, \"material-symbols-rounded\", \"text-primary\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [\"icon\", \"pi pi-angle-left\", 1, \"-ml-5\", 3, \"rounded\", \"outlined\", \"styleClass\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"account-view\", \"mt-3\", \"p-3\", \"border-round\", \"surface-b\"], [1, \"grid\", \"mt-0\", \"align-items-center\"], [1, \"col-12\", \"lg:col-3\", \"md:col-3\"], [1, \"a-view-form-g\", \"flex\", \"align-items-center\", \"relative\", \"border-round\", \"overflow-hidden\"], [1, \"absolute\", \"top-0\", \"left-0\", \"h-3rem\", \"w-3rem\", \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-orange-600\", \"text-white\", \"font-semibold\"], [\"type\", \"text\", \"pInputText\", \"\", \"value\", \"98765.00\", 1, \"surface-0\", \"pl-7\", \"border-1\", \"border-100\", \"h-3rem\", \"w-full\", \"font-semibold\"], [1, \"grid\", \"m-0\"], [1, \"col-12\", \"p-0\", \"border-bottom-1\", \"border-100\"], [1, \"flex\", \"align-items-center\", \"h-3rem\", \"w-full\", \"justify-content-end\", \"font-semibold\"], [\"type\", \"text\", \"pInputText\", \"\", \"value\", \"98765.00\", 1, \"surface-0\", \"border-1\", \"border-100\", \"h-3rem\", \"w-full\", \"font-semibold\"], [1, \"grid\", \"mt-4\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\"], [1, \"flex\", \"flex-column\", \"gap-2\"], [\"for\", \"username\", 1, \"text-500\", \"font-medium\"], [\"pInputText\", \"\", \"id\", \"username\", \"value\", \"Activity Id\", 1, \"surface-0\", \"border-1\", \"border-100\", \"h-3rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"value\", \"475625.00\", 1, \"surface-0\", \"border-1\", \"border-100\", \"h-3rem\", \"w-full\", \"font-semibold\", \"text-500\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"pt-0\"], [\"pInputText\", \"\", \"id\", \"username\", \"value\", \"MM/DD/YYYY\", 1, \"surface-0\", \"border-1\", \"border-100\", \"h-3rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"value\", \"Customer ID\", 1, \"surface-0\", \"border-1\", \"border-100\", \"h-3rem\", \"w-full\", \"font-semibold\", \"text-500\"]],\n      template: function OpportunitiesOverviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"h5\", 6);\n          i0.ɵɵtext(7, \"JS\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"h5\", 8);\n          i0.ɵɵtext(10, \"SNJYA Customer Sprint 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"ul\", 9)(12, \"li\", 10)(13, \"span\", 11);\n          i0.ɵɵtext(14, \"CRM ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15, \" : 05545SD585\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"li\", 10)(17, \"span\", 11);\n          i0.ɵɵtext(18, \"S4/HANA ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(19, \" : 152ASD5585\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"li\", 10)(21, \"span\", 11);\n          i0.ɵɵtext(22, \"Account Owner \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(23, \" : Adam Smith\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"li\", 10)(25, \"span\", 11);\n          i0.ɵɵtext(26, \"Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27, \" : Ben Jacobs\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(28, \"div\", 12)(29, \"button\", 13)(30, \"i\", 14);\n          i0.ɵɵtext(31, \"call\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"button\", 13)(33, \"i\", 14);\n          i0.ɵɵtext(34, \"location_on\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"button\", 13)(36, \"i\", 14);\n          i0.ɵɵtext(37, \"email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"button\", 13)(39, \"i\", 14);\n          i0.ɵɵtext(40, \"language\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"button\", 13)(42, \"i\", 14);\n          i0.ɵɵtext(43, \"edit_square\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(44, \"div\", 15)(45, \"ul\", 16)(46, \"li\", 17)(47, \"span\", 18)(48, \"i\", 19);\n          i0.ɵɵtext(49, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(50, \" Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"span\", 20);\n          i0.ɵɵtext(52, \"3030 Warrenville Rd, Suite #610, Lisle, IL, United States of America, USA 60532.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"li\", 17)(54, \"span\", 18)(55, \"i\", 19);\n          i0.ɵɵtext(56, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(57, \" Phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"span\", 20);\n          i0.ɵɵtext(59, \"******-423-5926\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"li\", 17)(61, \"span\", 18)(62, \"i\", 19);\n          i0.ɵɵtext(63, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(64, \" Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"span\", 20);\n          i0.ɵɵtext(66, \"******-423-5926\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"li\", 17)(68, \"span\", 18)(69, \"i\", 19);\n          i0.ɵɵtext(70, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(71, \" Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"span\", 20);\n          i0.ɵɵtext(73, \"<EMAIL>\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"li\", 17)(75, \"span\", 18)(76, \"i\", 19);\n          i0.ɵɵtext(77, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(78, \" Website\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"span\", 20);\n          i0.ɵɵtext(80, \"www.asardigital.com\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(81, \"div\", 21)(82, \"div\", 22)(83, \"div\", 23);\n          i0.ɵɵelement(84, \"p-button\", 24);\n          i0.ɵɵelementStart(85, \"h4\", 25);\n          i0.ɵɵtext(86, \"Account 360\\u00B0 View\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"div\", 26)(88, \"div\", 27)(89, \"div\", 28)(90, \"div\", 29)(91, \"span\", 30);\n          i0.ɵɵtext(92, \"Q1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(93, \"input\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(94, \"div\", 28)(95, \"div\", 29)(96, \"span\", 30);\n          i0.ɵɵtext(97, \"Q2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(98, \"input\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(99, \"div\", 28)(100, \"div\", 29)(101, \"span\", 30);\n          i0.ɵɵtext(102, \"Q3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(103, \"input\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(104, \"div\", 28)(105, \"div\", 29)(106, \"span\", 30);\n          i0.ɵɵtext(107, \"Q4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(108, \"input\", 31);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(109, \"div\", 32);\n          i0.ɵɵelement(110, \"div\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"div\", 27);\n          i0.ɵɵelement(112, \"div\", 28)(113, \"div\", 28);\n          i0.ɵɵelementStart(114, \"div\", 28)(115, \"span\", 34);\n          i0.ɵɵtext(116, \"Total Sales\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(117, \"div\", 28)(118, \"div\", 29);\n          i0.ɵɵelement(119, \"input\", 35);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(120, \"div\", 36)(121, \"div\", 37)(122, \"div\", 38)(123, \"label\", 39);\n          i0.ɵɵtext(124, \"Last Interaction\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(125, \"input\", 40);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(126, \"div\", 37)(127, \"div\", 38)(128, \"label\", 39);\n          i0.ɵɵtext(129, \"Amount Invoiced\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(130, \"input\", 41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(131, \"div\", 37)(132, \"div\", 38)(133, \"label\", 39);\n          i0.ɵɵtext(134, \"Amount Due\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(135, \"input\", 41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(136, \"div\", 42)(137, \"div\", 38)(138, \"label\", 39);\n          i0.ɵɵtext(139, \"Last Order Amount\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(140, \"input\", 41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(141, \"div\", 42)(142, \"div\", 38)(143, \"label\", 39);\n          i0.ɵɵtext(144, \"Last Order Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(145, \"input\", 43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(146, \"div\", 42)(147, \"div\", 38)(148, \"label\", 39);\n          i0.ɵɵtext(149, \"Total Credit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(150, \"input\", 41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(151, \"div\", 42)(152, \"div\", 38)(153, \"label\", 39);\n          i0.ɵɵtext(154, \"Available Credit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(155, \"input\", 41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(156, \"div\", 42)(157, \"div\", 38)(158, \"label\", 39);\n          i0.ɵɵtext(159, \"Last Quotation Amount\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(160, \"input\", 41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(161, \"div\", 42)(162, \"div\", 38)(163, \"label\", 39);\n          i0.ɵɵtext(164, \"Last Quotation Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(165, \"input\", 43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(166, \"div\", 42)(167, \"div\", 38)(168, \"label\", 39);\n          i0.ɵɵtext(169, \"AI Insights\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(170, \"input\", 44);\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(84);\n          i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n        }\n      },\n      dependencies: [i1.Button, i2.InputText],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["OpportunitiesOverviewComponent", "selectors", "decls", "vars", "consts", "template", "OpportunitiesOverviewComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-overview\\opportunities-overview.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities-details\\opportunities-overview\\opportunities-overview.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-opportunities-overview',\r\n  templateUrl: './opportunities-overview.component.html',\r\n  styleUrl: './opportunities-overview.component.scss'\r\n})\r\nexport class OpportunitiesOverviewComponent {\r\n\r\n}\r\n", "<div class=\"grid mt-0 relative\">\r\n    <div class=\"col-12 lg:w-28rem md:w-28rem sm:w-full\">\r\n        <div class=\"w-full bg-white border-round shadow-1 overflow-hidden\">\r\n            <div class=\"left-side-bar-top p-4 bg-primary overflow-hidden\">\r\n                <div class=\"flex align-items-start gap-4\">\r\n                    <div class=\"flex align-items-center justify-content-center w-3rem h-3rem surface-0 border-circle\">\r\n                        <h5 class=\"m-0 p-0 text-primary font-bold\">JS</h5>\r\n                    </div>\r\n                    <div class=\"flex flex-column gap-4 flex-1\">\r\n                        <h5 class=\"mt-3 mb-1 font-semibold\">SNJYA Customer Sprint 2</h5>\r\n                        <ul class=\"flex flex-column gap-3 p-0 m-0 list-none\">\r\n                            <li class=\"flex align-items-center gap-2\"><span class=\"flex w-9rem\">CRM ID</span> :\r\n                                05545SD585</li>\r\n                            <li class=\"flex align-items-center gap-2\"><span class=\"flex w-9rem\">S4/HANA ID</span> :\r\n                                152ASD5585</li>\r\n                            <li class=\"flex align-items-center gap-2\"><span class=\"flex w-9rem\">Account Owner </span> :\r\n                                Adam Smith</li>\r\n                            <li class=\"flex align-items-center gap-2\"><span class=\"flex w-9rem\">Main Contact</span> :\r\n                                Ben Jacobs</li>\r\n                        </ul>\r\n                    </div>\r\n                </div>\r\n                <div class=\"mt-6 mb-1 flex align-items-center justify-content-between gap-3\">\r\n                    <button type=\"button\" icon=\"pi pi-check\"\r\n                        class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                        <i class=\"material-symbols-rounded text-primary\">call</i>\r\n                    </button>\r\n                    <button type=\"button\" icon=\"pi pi-check\"\r\n                        class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                        <i class=\"material-symbols-rounded text-primary\">location_on</i>\r\n                    </button>\r\n                    <button type=\"button\" icon=\"pi pi-check\"\r\n                        class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                        <i class=\"material-symbols-rounded text-primary\">email</i>\r\n                    </button>\r\n                    <button type=\"button\" icon=\"pi pi-check\"\r\n                        class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                        <i class=\"material-symbols-rounded text-primary\">language</i>\r\n                    </button>\r\n                    <button type=\"button\" icon=\"pi pi-check\"\r\n                        class=\"p-element p-ripple p-button-rounded p-button p-component p-button-icon-only surface-0 h-3rem w-3rem\">\r\n                        <i class=\"material-symbols-rounded text-primary\">edit_square</i>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n            <div class=\"left-side-bar-bottom px-3 py-4\">\r\n                <ul class=\"flex flex-column gap-5 p-0 m-0 list-none\">\r\n                    <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                        <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                class=\"material-symbols-rounded\">location_on</i> Address</span>\r\n                        <span class=\"flex-1\">3030 Warrenville Rd, Suite #610, Lisle, IL, United States of America, USA\r\n                            60532.</span>\r\n                    </li>\r\n                    <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                        <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                class=\"material-symbols-rounded\">phone_in_talk</i> Phone</span>\r\n                        <span class=\"flex-1\">******-423-5926</span>\r\n                    </li>\r\n                    <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                        <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                class=\"material-symbols-rounded\">phone_in_talk</i> Main Contact</span>\r\n                        <span class=\"flex-1\">******-423-5926</span>\r\n                    </li>\r\n                    <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                        <span class=\"flex w-10rem align-items-center gap-2\"><i class=\"material-symbols-rounded\">mail</i>\r\n                            Email</span>\r\n                        <span class=\"flex-1\">info&#64;asardigital.com</span>\r\n                    </li>\r\n                    <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                        <span class=\"flex w-10rem align-items-center gap-2\"><i\r\n                                class=\"material-symbols-rounded\">language</i> Website</span>\r\n                        <span class=\"flex-1\">www.asardigital.com</span>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"col-12 lg:flex-1 md:flex-1\">\r\n        <div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n            <div class=\"card-heading flex align-items-center justify-content-start gap-2\">\r\n                <p-button icon=\"pi pi-angle-left\" [rounded]=\"true\" [outlined]=\"true\"\r\n                    [styleClass]=\"'p-0 w-2rem h-2rem border-2 surface-0'\" class=\"-ml-5\" />\r\n                <h4 class=\"m-0 pl-3 left-border relative flex\">Account 360° View</h4>\r\n            </div>\r\n            <div class=\"account-view mt-3 p-3 border-round surface-b\">\r\n                <div class=\"grid mt-0 align-items-center\">\r\n                    <div class=\"col-12 lg:col-3 md:col-3\">\r\n                        <div class=\"a-view-form-g flex align-items-center relative border-round overflow-hidden\">\r\n                            <span\r\n                                class=\"absolute top-0 left-0 h-3rem w-3rem flex align-items-center justify-content-center bg-orange-600 text-white font-semibold\">Q1</span>\r\n                            <input type=\"text\" pInputText value=\"98765.00\"\r\n                                class=\"surface-0 pl-7 border-1 border-100 h-3rem w-full font-semibold\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-3 md:col-3\">\r\n                        <div class=\"a-view-form-g flex align-items-center relative border-round overflow-hidden\">\r\n                            <span\r\n                                class=\"absolute top-0 left-0 h-3rem w-3rem flex align-items-center justify-content-center bg-orange-600 text-white font-semibold\">Q2</span>\r\n                            <input type=\"text\" pInputText value=\"98765.00\"\r\n                                class=\"surface-0 pl-7 border-1 border-100 h-3rem w-full font-semibold\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-3 md:col-3\">\r\n                        <div class=\"a-view-form-g flex align-items-center relative border-round overflow-hidden\">\r\n                            <span\r\n                                class=\"absolute top-0 left-0 h-3rem w-3rem flex align-items-center justify-content-center bg-orange-600 text-white font-semibold\">Q3</span>\r\n                            <input type=\"text\" pInputText value=\"98765.00\"\r\n                                class=\"surface-0 pl-7 border-1 border-100 h-3rem w-full font-semibold\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-3 md:col-3\">\r\n                        <div class=\"a-view-form-g flex align-items-center relative border-round overflow-hidden\">\r\n                            <span\r\n                                class=\"absolute top-0 left-0 h-3rem w-3rem flex align-items-center justify-content-center bg-orange-600 text-white font-semibold\">Q4</span>\r\n                            <input type=\"text\" pInputText value=\"98765.00\"\r\n                                class=\"surface-0 pl-7 border-1 border-100 h-3rem w-full font-semibold\" />\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"grid m-0\">\r\n                    <div class=\"col-12 p-0 border-bottom-1 border-100\">\r\n                    </div>\r\n                </div>\r\n                <div class=\"grid mt-0 align-items-center\">\r\n                    <div class=\"col-12 lg:col-3 md:col-3\"></div>\r\n                    <div class=\"col-12 lg:col-3 md:col-3\"></div>\r\n                    <div class=\"col-12 lg:col-3 md:col-3\">\r\n                        <span class=\"flex align-items-center h-3rem w-full justify-content-end font-semibold\">Total\r\n                            Sales</span>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-3 md:col-3\">\r\n                        <div class=\"a-view-form-g flex align-items-center relative border-round overflow-hidden\">\r\n                            <input type=\"text\" pInputText value=\"98765.00\"\r\n                                class=\"surface-0 border-1 border-100 h-3rem w-full font-semibold\" />\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"grid mt-4\">\r\n                <div class=\"col-12 lg:col-4 md:col-4\">\r\n                    <div class=\"flex flex-column gap-2\">\r\n                        <label for=\"username\" class=\"text-500 font-medium\">Last Interaction</label>\r\n                        <input pInputText id=\"username\" value=\"Activity Id\"\r\n                            class=\"surface-0 border-1 border-100 h-3rem w-full font-semibold text-500\" />\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4 md:col-4\">\r\n                    <div class=\"flex flex-column gap-2\">\r\n                        <label for=\"username\" class=\"text-500 font-medium\">Amount Invoiced</label>\r\n                        <input pInputText id=\"username\" value=\"475625.00\"\r\n                            class=\"surface-0 border-1 border-100 h-3rem w-full font-semibold text-500\" />\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4 md:col-4\">\r\n                    <div class=\"flex flex-column gap-2\">\r\n                        <label for=\"username\" class=\"text-500 font-medium\">Amount Due</label>\r\n                        <input pInputText id=\"username\" value=\"475625.00\"\r\n                            class=\"surface-0 border-1 border-100 h-3rem w-full font-semibold text-500\" />\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4 md:col-4 pt-0\">\r\n                    <div class=\"flex flex-column gap-2\">\r\n                        <label for=\"username\" class=\"text-500 font-medium\">Last Order Amount</label>\r\n                        <input pInputText id=\"username\" value=\"475625.00\"\r\n                            class=\"surface-0 border-1 border-100 h-3rem w-full font-semibold text-500\" />\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4 md:col-4 pt-0\">\r\n                    <div class=\"flex flex-column gap-2\">\r\n                        <label for=\"username\" class=\"text-500 font-medium\">Last Order Date</label>\r\n                        <input pInputText id=\"username\" value=\"MM/DD/YYYY\"\r\n                            class=\"surface-0 border-1 border-100 h-3rem w-full font-semibold text-500\" />\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4 md:col-4 pt-0\">\r\n                    <div class=\"flex flex-column gap-2\">\r\n                        <label for=\"username\" class=\"text-500 font-medium\">Total Credit</label>\r\n                        <input pInputText id=\"username\" value=\"475625.00\"\r\n                            class=\"surface-0 border-1 border-100 h-3rem w-full font-semibold text-500\" />\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4 md:col-4 pt-0\">\r\n                    <div class=\"flex flex-column gap-2\">\r\n                        <label for=\"username\" class=\"text-500 font-medium\">Available Credit</label>\r\n                        <input pInputText id=\"username\" value=\"475625.00\"\r\n                            class=\"surface-0 border-1 border-100 h-3rem w-full font-semibold text-500\" />\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4 md:col-4 pt-0\">\r\n                    <div class=\"flex flex-column gap-2\">\r\n                        <label for=\"username\" class=\"text-500 font-medium\">Last Quotation Amount</label>\r\n                        <input pInputText id=\"username\" value=\"475625.00\"\r\n                            class=\"surface-0 border-1 border-100 h-3rem w-full font-semibold text-500\" />\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4 md:col-4 pt-0\">\r\n                    <div class=\"flex flex-column gap-2\">\r\n                        <label for=\"username\" class=\"text-500 font-medium\">Last Quotation Date</label>\r\n                        <input pInputText id=\"username\" value=\"MM/DD/YYYY\"\r\n                            class=\"surface-0 border-1 border-100 h-3rem w-full font-semibold text-500\" />\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4 md:col-4 pt-0\">\r\n                    <div class=\"flex flex-column gap-2\">\r\n                        <label for=\"username\" class=\"text-500 font-medium\">AI Insights</label>\r\n                        <input pInputText id=\"username\" value=\"Customer ID\"\r\n                            class=\"surface-0 border-1 border-100 h-3rem w-full font-semibold text-500\" />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": ";;;AAOA,OAAM,MAAOA,8BAA8B;;;uBAA9BA,8BAA8B;IAAA;EAAA;;;YAA9BA,8BAA8B;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCDnBE,EANxB,CAAAC,cAAA,aAAgC,aACwB,aACmB,aACD,aAChB,aAC4D,YACnD;UAAAD,EAAA,CAAAE,MAAA,SAAE;UACjDF,EADiD,CAAAG,YAAA,EAAK,EAChD;UAEFH,EADJ,CAAAC,cAAA,aAA2C,YACH;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAElBH,EAD9C,CAAAC,cAAA,aAAqD,cACP,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,qBACpE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACuBH,EAA1C,CAAAC,cAAA,cAA0C,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,qBACxE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACuBH,EAA1C,CAAAC,cAAA,cAA0C,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,qBAC5E;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACuBH,EAA1C,CAAAC,cAAA,cAA0C,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,qBAC1E;UAG1BF,EAH0B,CAAAG,YAAA,EAAK,EAClB,EACH,EACJ;UAIEH,EAHR,CAAAC,cAAA,eAA6E,kBAEuC,aAC3D;UAAAD,EAAA,CAAAE,MAAA,YAAI;UACzDF,EADyD,CAAAG,YAAA,EAAI,EACpD;UAGLH,EAFJ,CAAAC,cAAA,kBACgH,aAC3D;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAChEF,EADgE,CAAAG,YAAA,EAAI,EAC3D;UAGLH,EAFJ,CAAAC,cAAA,kBACgH,aAC3D;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAC1DF,EAD0D,CAAAG,YAAA,EAAI,EACrD;UAGLH,EAFJ,CAAAC,cAAA,kBACgH,aAC3D;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAC7DF,EAD6D,CAAAG,YAAA,EAAI,EACxD;UAGLH,EAFJ,CAAAC,cAAA,kBACgH,aAC3D;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAGxEF,EAHwE,CAAAG,YAAA,EAAI,EAC3D,EACP,EACJ;UAI0DH,EAHhE,CAAAC,cAAA,eAA4C,cACa,cACyB,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,wFACX;UACdF,EADc,CAAAG,YAAA,EAAO,EAChB;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UACxCF,EADwC,CAAAG,YAAA,EAAO,EAC1C;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9EH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UACxCF,EADwC,CAAAG,YAAA,EAAO,EAC1C;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aAAoC;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5FH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,4BAAwB;UACjDF,EADiD,CAAAG,YAAA,EAAO,EACnD;UAEmDH,EADxD,CAAAC,cAAA,cAA0E,gBAClB,aACX;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAK5DF,EAL4D,CAAAG,YAAA,EAAO,EAC9C,EACJ,EACH,EACJ,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAwC,eACmB,eAC2B;UAC1ED,EAAA,CAAAI,SAAA,oBAC0E;UAC1EJ,EAAA,CAAAC,cAAA,cAA+C;UAAAD,EAAA,CAAAE,MAAA,8BAAiB;UACpEF,EADoE,CAAAG,YAAA,EAAK,EACnE;UAKUH,EAJhB,CAAAC,cAAA,eAA0D,eACZ,eACA,eACuD,gBAEiD;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/IH,EAAA,CAAAI,SAAA,iBAC6E;UAErFJ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAsC,eACuD,gBAEiD;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/IH,EAAA,CAAAI,SAAA,iBAC6E;UAErFJ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAsC,gBACuD,iBAEiD;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/IH,EAAA,CAAAI,SAAA,kBAC6E;UAErFJ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAAsC,gBACuD,iBAEiD;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/IH,EAAA,CAAAI,SAAA,kBAC6E;UAGzFJ,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;UACNH,EAAA,CAAAC,cAAA,gBAAsB;UAClBD,EAAA,CAAAI,SAAA,gBACM;UACVJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAA0C;UAEtCD,EADA,CAAAI,SAAA,gBAA4C,gBACA;UAExCJ,EADJ,CAAAC,cAAA,gBAAsC,iBACoD;UAAAD,EAAA,CAAAE,MAAA,oBAC7E;UACbF,EADa,CAAAG,YAAA,EAAO,EACd;UAEFH,EADJ,CAAAC,cAAA,gBAAsC,gBACuD;UACrFD,EAAA,CAAAI,SAAA,kBACwE;UAIxFJ,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;UAIMH,EAHZ,CAAAC,cAAA,gBAAuB,gBACmB,gBACE,kBACmB;UAAAD,EAAA,CAAAE,MAAA,yBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3EH,EAAA,CAAAI,SAAA,kBACiF;UAEzFJ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAAsC,gBACE,kBACmB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1EH,EAAA,CAAAI,SAAA,kBACiF;UAEzFJ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAAsC,gBACE,kBACmB;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrEH,EAAA,CAAAI,SAAA,kBACiF;UAEzFJ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAA2C,gBACH,kBACmB;UAAAD,EAAA,CAAAE,MAAA,0BAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5EH,EAAA,CAAAI,SAAA,kBACiF;UAEzFJ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAA2C,gBACH,kBACmB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1EH,EAAA,CAAAI,SAAA,kBACiF;UAEzFJ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAA2C,gBACH,kBACmB;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvEH,EAAA,CAAAI,SAAA,kBACiF;UAEzFJ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAA2C,gBACH,kBACmB;UAAAD,EAAA,CAAAE,MAAA,yBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3EH,EAAA,CAAAI,SAAA,kBACiF;UAEzFJ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAA2C,gBACH,kBACmB;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChFH,EAAA,CAAAI,SAAA,kBACiF;UAEzFJ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAA2C,gBACH,kBACmB;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9EH,EAAA,CAAAI,SAAA,kBACiF;UAEzFJ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAA2C,gBACH,kBACmB;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtEH,EAAA,CAAAI,SAAA,kBACiF;UAMzGJ,EALoB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ,EACJ;;;UApI4CH,EAAA,CAAAK,SAAA,IAAgB;UAC9CL,EAD8B,CAAAM,UAAA,iBAAgB,kBAAkB,sDACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}