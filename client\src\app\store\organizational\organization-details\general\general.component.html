<div class="p-3 w-full surface-card border-round shadow-1 mb-5">
    <div class="card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2">
        <h4 class="m-0 pl-3 left-border relative flex">Parent Unit</h4>
        <div class="flex gap-3 ml-auto align-items-center">
            <!-- <p-button label="Add New" (click)="showNewDialog('right','parent')" icon="pi pi-plus-circle" iconPos="right"
                class="ml-auto" [styleClass]="'font-semibold px-3'" [rounded]="true" /> -->

            <p-multiSelect [options]="cols" [ngModel]="getSelectedColumns('parent')"
                (ngModelChange)="setSelectedColumns('parent', $event)" optionLabel="header"
                class="table-multiselect-dropdown" [styleClass]="
          'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'
        ">
            </p-multiSelect>
        </div>
    </div>

    <div class="table-sec">
        <p-table [value]="parentunitDetails" dataKey="id" [rows]="14" [paginator]="true" [lazy]="true"
            responsiveLayout="scroll" [scrollable]="true" class="scrollable-table" [reorderableColumns]="true"
            (onColReorder)="onColumnReorder($event,'parent')" (onSort)="customSort($event.field,'parent')">
            <ng-template pTemplate="header">
                <tr>
                    <th pFrozenColumn class="border-round-left-lg pl-3 w-2rem table-checkbox text-center">
                        <p-tableHeaderCheckbox />
                    </th>
                    <th pFrozenColumn (click)="customSort('start_date','parent')">
                        <div class="flex align-items-center cursor-pointer">
                            Valid From
                            <i *ngIf="sortFieldMap['parent'] === 'start_date'" class="ml-2 pi" [ngClass]="
                  sortOrderMap['parent'] === 1
                    ? 'pi-sort-amount-up-alt'
                    : 'pi-sort-amount-down'
                ">
                            </i>
                            <i *ngIf="sortFieldMap['parent'] !== 'start_date'" class="ml-2 pi pi-sort"></i>
                        </div>
                    </th>
                    <ng-container *ngFor="let col of getSelectedColumns('parent')">
                        <th [pSortableColumn]="col.field" pReorderableColumn (click)="customSort(col.field,'parent')">
                            <div class="flex align-items-center cursor-pointer">
                                {{ col.header }}
                                <i *ngIf="sortFieldMap['parent'] === col.field" class="ml-2 pi" [ngClass]="
                    sortOrderMap['parent'] === 1
                      ? 'pi-sort-amount-up-alt'
                      : 'pi-sort-amount-down'
                  ">
                                </i>
                                <i *ngIf="sortFieldMap['parent'] !== col.field" class="ml-2 pi pi-sort"></i>
                            </div>
                        </th>
                    </ng-container>
                    <th>
                        <div class="flex align-items-center">Actions</div>
                    </th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-unit let-columns="columns">
                <tr class="cursor-pointer">
                    <td pFrozenColumn class="border-round-left-lg pl-3 w-2rem text-center">
                        <p-tableCheckbox [value]="unit" />
                    </td>
                    <td pFrozenColumn class="font-medium">
                        {{ unit?.start_date ? (unit.start_date | date: 'dd/MM/yyyy') : '-' }}
                    </td>

                    <ng-container *ngFor="let col of getSelectedColumns('parent')">
                        <td>
                            <ng-container [ngSwitch]="col.field">
                                <ng-container *ngSwitchCase="'end_date'">
                                    {{ unit?.end_date ? (unit.end_date | date: 'dd/MM/yyyy') : '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'parent_organisational_unit_id'">
                                    {{ unit?.parent_organisational_unit_id || '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'parent_organisational_unit.name'">
                                    {{ unit?.parent_organisational_unit?.name || '-' }}
                                </ng-container>
                            </ng-container>
                        </td>
                    </ng-container>

                    <td>
                        <div class="flex align-items-center">
                            <button pButton type="button" class="mr-2" icon="pi pi-pencil" pTooltip="Edit"
                                (click)="editUnit(unit)"></button>
                            <button pButton type="button" icon="pi pi-trash" pTooltip="Delete"
                                (click)="$event.stopPropagation(); confirmRemove(unit,'parent')"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td class="border-round-left-lg" colspan="6">No parent units found.</td>
                </tr>
            </ng-template>
            <ng-template pTemplate="loadingbody">
                <tr>
                    <td colspan="6" class="border-round-left-lg">
                        Loading parent units data. Please wait...
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>
<p-dialog [modal]="true" [(visible)]="addParentDialogVisible" [style]="{ width: '45rem' }" [position]="'right'"
    [draggable]="false" class="opportunity-contact-popup">
    <ng-template pTemplate="header">
        <h4>Parent Unit</h4>
    </ng-template>

    <form [formGroup]="ParentUnitForm" class="relative flex flex-column gap-1">
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Valid From">
                <span class="material-symbols-rounded">badge</span>Valid From
                <span class="text-red-500">*</span>
            </label>
            <div class="form-input flex-1 relative">
                <p-calendar formControlName="start_date" inputId="calendar-12h" hourFormat="12" [showIcon]="true"
                    styleClass="h-3rem w-full" placeholder="Valid From" appendTo="body"
                    [ngClass]="{ 'is-invalid': submitted && f['start_date'].errors }" />
                <div *ngIf="submitted && f['start_date'].errors" class="p-error">
                    <div *ngIf="
              submitted &&
              f['start_date'].errors &&
              f['start_date'].errors['required']
            ">
                        Valid From is required.
                    </div>
                </div>
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Valid To">
                <span class="material-symbols-rounded">badge</span>Valid To
                <span class="text-red-500">*</span>
            </label>
            <div class="form-input flex-1 relative">
                <p-calendar formControlName="end_date" inputId="calendar-12h" hourFormat="12" [showIcon]="true"
                    styleClass="h-3rem w-full" placeholder="Valid To" appendTo="body"
                    [ngClass]="{ 'is-invalid': submitted && f['end_date'].errors }" />
                <div *ngIf="submitted && f['end_date'].errors" class="p-error">
                    <div *ngIf="
              submitted &&
              f['end_date'].errors &&
              f['end_date'].errors['required']
            ">
                        Valid To is required.
                    </div>
                </div>
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Parent Unit ID">
                <span class="material-symbols-rounded">account_tree</span>Parent Unit ID
            </label>
            <div class="form-input flex-1 relative">
                <ng-select pInputText [items]="units$ | async" bindLabel="name" bindValue="organisational_unit_id"
                    [hideSelected]="true" [loading]="unitLoading" [minTermLength]="0"
                    formControlName="parent_organisational_unit_id" [typeahead]="unitInput$" [maxSelectedItems]="10"
                    appendTo="body" [class]="'multiselect-dropdown p-inputtext p-component p-element'">
                    <ng-template ng-option-tmp let-item="item">
                        <span>{{ item.organisational_unit_id }}</span>
                        <span *ngIf="item.name"> : {{ item.name }}</span>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="flex align-items-center p-3 gap-3 mt-1">
            <button pButton type="button" label="Cancel"
                class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem"
                (click)="addParentDialogVisible = false"></button>
            <button pButton type="submit" label="Save" class="p-button-rounded justify-content-center w-9rem h-3rem"
                (click)="onSubmit()"></button>
        </div>
    </form>
</p-dialog>

<div class="p-3 w-full surface-card border-round shadow-1">
    <div class="card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2">
        <h4 class="m-0 pl-3 left-border relative flex">Address</h4>
        <div class="flex gap-3 ml-auto align-items-center">
            <p-button label="Add New" (click)="showNewDialog('right','address')" icon="pi pi-plus-circle"
                iconPos="right" class="ml-auto" [styleClass]="'font-semibold px-3'" [rounded]="true" />

            <p-multiSelect [options]="colsaddress" [ngModel]="getSelectedColumns('address')"
                (ngModelChange)="setSelectedColumns('address', $event)" optionLabel="header"
                class="table-multiselect-dropdown" [styleClass]="
          'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'
        ">
            </p-multiSelect>
        </div>
    </div>

    <div class="table-sec">
        <p-table [value]="addressDetails" dataKey="id" [rows]="14" [paginator]="true" [lazy]="true"
            responsiveLayout="scroll" [scrollable]="true" class="scrollable-table" [reorderableColumns]="true"
            (onColReorder)="onColumnReorder($event,'address')">
            <ng-template pTemplate="header">
                <tr>
                    <th pFrozenColumn class="border-round-left-lg pl-3 w-2rem table-checkbox text-center">
                        <p-tableHeaderCheckbox />
                    </th>
                    <th pFrozenColumn (click)="customSort('start_date','address')">
                        <div class="flex align-items-center cursor-pointer">
                            Valid From
                            <i *ngIf="sortFieldMap['address'] === 'start_date'" class="ml-2 pi" [ngClass]="
                  sortOrderMap['address'] === 1
                    ? 'pi-sort-amount-up-alt'
                    : 'pi-sort-amount-down'
                ">
                            </i>
                            <i *ngIf="sortFieldMap['address'] !== 'start_date'" class="ml-2 pi pi-sort"></i>
                        </div>
                    </th>
                    <ng-container *ngFor="let col of getSelectedColumns('address')">
                        <th [pSortableColumn]="col.field" pReorderableColumn (click)="customSort(col.field,'address')">
                            <div class="flex align-items-center cursor-pointer">
                                {{ col.header }}
                                <i *ngIf="sortFieldMap['address'] === col.field" class="ml-2 pi" [ngClass]="
                    sortOrderMap['address'] === 1
                      ? 'pi-sort-amount-up-alt'
                      : 'pi-sort-amount-down'
                  ">
                                </i>
                                <i *ngIf="sortFieldMap['address'] !== col.field" class="ml-2 pi pi-sort"></i>
                            </div>
                        </th>
                    </ng-container>
                    <th>
                        <div class="flex align-items-center">Actions</div>
                    </th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-address let-columns="columns">
                <tr class="cursor-pointer">
                    <td pFrozenColumn class="border-round-left-lg pl-3 w-2rem text-center">
                        <p-tableCheckbox [value]="address" />
                    </td>
                    <td pFrozenColumn class="font-medium">
                        {{ address?.start_date ? (address.start_date | date: 'dd/MM/yyyy') : '-' }}
                    </td>

                    <ng-container *ngFor="let col of getSelectedColumns('address')">
                        <td>
                            <ng-container [ngSwitch]="col.field">
                                <ng-container *ngSwitchCase="'end_date'">
                                    {{ address?.end_date ? (address.end_date | date: 'dd/MM/yyyy') : '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'name'">
                                    {{ address?.name || '-' }}
                                </ng-container>
                                <ng-container *ngSwitchCase="'street_name'">
                                    {{
                                    (address?.street_name || '-') + ', ' +
                                    (address?.city_name || '-') + ', ' +
                                    (address?.region_code || '-') + ', ' +
                                    (address?.country_code || '-') + ' - ' +
                                    (address?.street_postal_code || '-')
                                    }}
                                </ng-container>
                                <ng-container *ngSwitchCase="'conventional_phone_formatted_number_description'">
                                    {{ address?.conventional_phone_formatted_number_description || '-' }}
                                </ng-container>
                                <ng-container *ngSwitchCase="'mobile_formatted_number_description'">
                                    {{ address?.mobile_formatted_number_description || '-' }}
                                </ng-container>
                                <ng-container *ngSwitchCase="'email_uri'">
                                    {{ address?.email_uri || '-' }}
                                </ng-container>
                                <ng-container *ngSwitchCase="'web_uri'">
                                    {{ address?.web_uri || '-' }}
                                </ng-container>
                            </ng-container>
                        </td>
                    </ng-container>

                    <td>
                        <div class="flex align-items-center">
                            <button pButton type="button" class="mr-2" icon="pi pi-pencil" pTooltip="Edit"
                                (click)="editAddress(address)"></button>
                            <button pButton type="button" icon="pi pi-trash" pTooltip="Delete"
                                (click)="$event.stopPropagation(); confirmRemove(address,'address')"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td class="border-round-left-lg" colspan="12">No addresses found.</td>
                </tr>
            </ng-template>
            <ng-template pTemplate="loadingbody">
                <tr>
                    <td colspan="12" class="border-round-left-lg">
                        Loading addresses data. Please wait...
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>

<p-dialog [modal]="true" [(visible)]="addAddressDialogVisible" [style]="{ width: '45rem' }" [position]="'right'"
    [draggable]="false" class="opportunity-contact-popup">
    <ng-template pTemplate="header">
        <h4>Address</h4>
    </ng-template>

    <form [formGroup]="AddressForm" class="relative flex flex-column gap-1">
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Name">
                <span class="material-symbols-rounded">badge</span>Name
                <span class="text-red-500">*</span>
            </label>
            <div class="form-input flex-1 relative">
                <input pInputText type="text" id="name" formControlName="name" class="h-3rem w-full" autocomplete="off"
                    placeholder="Name" [ngClass]="{ 'is-invalid': submitted && faddress['name'].errors }" />
                <div *ngIf="submitted && faddress['name'].errors" class="p-error">
                    <div *ngIf="
                submitted &&
                faddress['name'].errors &&
                faddress['name'].errors['required']
              ">
                        Name is required.
                    </div>
                </div>
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Email">
                <span class="material-symbols-rounded">badge</span>Email
            </label>
            <div class="form-input flex-1 relative">
                <input pInputText type="text" id="email_uri" formControlName="email_uri" class="h-3rem w-full"
                    autocomplete="off" placeholder="Email" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="WebSite">
                <span class="material-symbols-rounded">globe</span>WebSite
            </label>
            <div class="form-input flex-1 relative">
                <input pInputText type="text" id="web_uri" formControlName="web_uri" class="h-3rem w-full"
                    autocomplete="off" placeholder="WebSite" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Street">
                <span class="material-symbols-rounded">badge</span>Street
            </label>
            <div class="form-input flex-1 relative">
                <input pInputText type="text" id="street_name" formControlName="street_name" class="h-3rem w-full"
                    autocomplete="off" placeholder="Street" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Country">
                <span class="material-symbols-rounded">map</span>Country
                <span class="text-red-500">*</span>
            </label>
            <div class="form-input flex-1 relative">
                <p-dropdown [options]="countries" optionLabel="name" optionValue="isoCode" [(ngModel)]="selectedCountry"
                    (onChange)="onCountryChange()" [filter]="true" formControlName="country_code"
                    [styleClass]="'h-3rem w-full'" placeholder="Select Country"
                    [ngClass]="{ 'is-invalid': submitted && faddress['country_code'].errors }">
                </p-dropdown>
                <div *ngIf="submitted && faddress['country_code'].errors" class="p-error">
                    <div *ngIf="
                submitted &&
                faddress['country_code'].errors &&
                faddress['country_code'].errors['required']
              ">
                        Country is required.
                    </div>
                </div>
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="State">
                <span class="material-symbols-rounded">location_on</span>State
                <span class="text-red-500">*</span>
            </label>
            <div class="form-input flex-1 relative">
                <p-dropdown [options]="states" optionLabel="name" optionValue="isoCode" [(ngModel)]="selectedState"
                    formControlName="region_code" placeholder="Select State" [disabled]="!selectedCountry"
                    [styleClass]="'h-3rem w-full'"
                    [ngClass]="{ 'is-invalid': submitted && faddress['region_code'].errors }">
                </p-dropdown>
                <div *ngIf="submitted && faddress['region_code'].errors" class="p-error">
                    <div *ngIf="
                submitted &&
                faddress['region_code'].errors &&
                faddress['region_code'].errors['required']
              ">
                        State is required.
                    </div>
                </div>
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="City">
                <span class="material-symbols-rounded">home_pin</span>City
            </label>
            <div class="form-input flex-1 relative">
                <input pInputText type="text" id="city_name" formControlName="city_name" class="h-3rem w-full"
                    autocomplete="off" placeholder="City" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Zip Code">
                <span class="material-symbols-rounded">code_blocks</span>Zip Code
            </label>
            <div class="form-input flex-1 relative">
                <input pInputText type="text" id="street_postal_code" formControlName="street_postal_code"
                    class="h-3rem w-full" autocomplete="off" placeholder="Zip Code" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Mobile">
                <span class="material-symbols-rounded">phone_iphone</span>Mobile
            </label>
            <div class="form-input flex-1 relative">
                <input pInputText type="text" id="mobile_formatted_number_description"
                    formControlName="mobile_formatted_number_description" class="h-3rem w-full" autocomplete="off"
                    placeholder="Mobile" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Phone">
                <span class="material-symbols-rounded">code_blocks</span>Phone
            </label>
            <div class="form-input flex-1 relative">
                <input pInputText type="text" id="conventional_phone_formatted_number_description"
                    formControlName="conventional_phone_formatted_number_description" class="h-3rem w-full"
                    autocomplete="off" placeholder="Phone" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Valid From">
                <span class="material-symbols-rounded">badge</span>Valid From
                <span class="text-red-500">*</span>
            </label>
            <div class="form-input flex-1 relative">
                <p-calendar formControlName="start_date" inputId="calendar-12h" hourFormat="12" [showIcon]="true"
                    styleClass="h-3rem w-full" placeholder="Valid From" appendTo="body"
                    [ngClass]="{ 'is-invalid': submitted && faddress['start_date'].errors }" />
                <div *ngIf="submitted && faddress['start_date'].errors" class="p-error">
                    <div *ngIf="
              submitted &&
              faddress['start_date'].errors &&
              faddress['start_date'].errors['required']
            ">
                        Valid From is required.
                    </div>
                </div>
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Valid To">
                <span class="material-symbols-rounded">badge</span>Valid To
                <span class="text-red-500">*</span>
            </label>
            <div class="form-input flex-1 relative">
                <p-calendar formControlName="end_date" inputId="calendar-12h" hourFormat="12" [showIcon]="true"
                    styleClass="h-3rem w-full" placeholder="Valid To" appendTo="body"
                    [ngClass]="{ 'is-invalid': submitted && faddress['end_date'].errors }" />
                <div *ngIf="submitted && faddress['end_date'].errors" class="p-error">
                    <div *ngIf="
              submitted &&
              faddress['end_date'].errors &&
              faddress['end_date'].errors['required']
            ">
                        Valid To is required.
                    </div>
                </div>
            </div>
        </div>
        <div class="flex align-items-center p-3 gap-3 mt-1">
            <button pButton type="button" label="Cancel"
                class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem"
                (click)="addParentDialogVisible = false"></button>
            <button pButton type="submit" label="Save" class="p-button-rounded justify-content-center w-9rem h-3rem"
                (click)="onSubmitAddress()"></button>
        </div>
    </form>
</p-dialog>