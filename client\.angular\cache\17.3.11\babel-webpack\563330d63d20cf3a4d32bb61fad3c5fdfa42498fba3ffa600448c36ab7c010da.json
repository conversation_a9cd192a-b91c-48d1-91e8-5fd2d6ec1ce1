{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class InitialsPipe {\n  transform(value) {\n    const words = value?.trim().split(' ');\n    const firstInitial = words[0]?.charAt(0).toUpperCase() || '';\n    const secondInitial = words[1]?.charAt(0).toUpperCase() || '';\n    return firstInitial + secondInitial;\n  }\n  static {\n    this.ɵfac = function InitialsPipe_Factory(t) {\n      return new (t || InitialsPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"initials\",\n      type: InitialsPipe,\n      pure: true\n    });\n  }\n}", "map": {"version": 3, "names": ["InitialsPipe", "transform", "value", "words", "trim", "split", "firstInitial", "char<PERSON>t", "toUpperCase", "secondInitial", "pure"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\shared\\initials.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n    name: 'initials'\r\n})\r\nexport class InitialsPipe implements PipeTransform {\r\n\r\n    transform(value: string): string {\r\n        const words = value?.trim().split(' ');\r\n        const firstInitial = words[0]?.charAt(0).toUpperCase() || '';\r\n        const secondInitial = words[1]?.charAt(0).toUpperCase() || '';\r\n        return firstInitial + secondInitial;\r\n    }\r\n    \r\n}"], "mappings": ";AAKA,OAAM,MAAOA,YAAY;EAErBC,SAASA,CAACC,KAAa;IACnB,MAAMC,KAAK,GAAGD,KAAK,EAAEE,IAAI,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACtC,MAAMC,YAAY,GAAGH,KAAK,CAAC,CAAC,CAAC,EAAEI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,IAAI,EAAE;IAC5D,MAAMC,aAAa,GAAGN,KAAK,CAAC,CAAC,CAAC,EAAEI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,IAAI,EAAE;IAC7D,OAAOF,YAAY,GAAGG,aAAa;EACvC;;;uBAPST,YAAY;IAAA;EAAA;;;;YAAZA,YAAY;MAAAU,IAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}