{"ast": null, "code": "import { effect, signal } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let LayoutService = /*#__PURE__*/(() => {\n  class LayoutService {\n    constructor() {\n      this._config = {\n        ripple: false,\n        menuMode: 'static',\n        colorScheme: 'dark',\n        theme: 'magenta',\n        scale: 14\n      };\n      this.config = signal(this._config);\n      this.state = {\n        staticMenuDesktopInactive: false,\n        overlayMenuActive: false,\n        rightMenuActive: false,\n        configSidebarVisible: false,\n        staticMenuMobileActive: false,\n        menuHoverActive: false,\n        sidebarActive: false,\n        anchored: false\n      };\n      this.configUpdate = new Subject();\n      this.overlayOpen = new Subject();\n      this.configUpdate$ = this.configUpdate.asObservable();\n      this.overlayOpen$ = this.overlayOpen.asObservable();\n      effect(() => {\n        const config = this.config();\n        if (this.updateStyle(config)) {\n          this.changeTheme();\n        }\n        this.changeScale(config.scale);\n        this.onConfigUpdate();\n      });\n    }\n    updateStyle(config) {\n      return config.theme !== this._config.theme || config.colorScheme !== this._config.colorScheme;\n    }\n    changeTheme() {\n      const config = this.config();\n      const themeLink = document.getElementById('theme-link');\n      const themeLinkHref = themeLink.getAttribute('href');\n      const newHref = themeLinkHref.split('/').map(el => el == this._config.theme ? el = config.theme : el == `theme-${this._config.colorScheme}` ? el = `theme-${config.colorScheme}` : el).join('/');\n      this.replaceThemeLink(newHref);\n    }\n    replaceThemeLink(href) {\n      const id = 'theme-link';\n      let themeLink = document.getElementById(id);\n      const cloneLinkElement = themeLink.cloneNode(true);\n      cloneLinkElement.setAttribute('href', href);\n      cloneLinkElement.setAttribute('id', id + '-clone');\n      themeLink.parentNode.insertBefore(cloneLinkElement, themeLink.nextSibling);\n      cloneLinkElement.addEventListener('load', () => {\n        themeLink.remove();\n        cloneLinkElement.setAttribute('id', id);\n      });\n    }\n    changeScale(value) {\n      document.documentElement.style.fontSize = `${value}px`;\n    }\n    onMenuToggle() {\n      if (this.isOverlay()) {\n        this.state.overlayMenuActive = !this.state.overlayMenuActive;\n        if (this.state.overlayMenuActive) {\n          this.overlayOpen.next(null);\n        }\n      }\n      if (this.isDesktop()) {\n        this.state.staticMenuDesktopInactive = !this.state.staticMenuDesktopInactive;\n      } else {\n        this.state.staticMenuMobileActive = !this.state.staticMenuMobileActive;\n        if (this.state.staticMenuMobileActive) {\n          this.overlayOpen.next(null);\n        }\n      }\n    }\n    onOverlaySubmenuOpen() {\n      this.overlayOpen.next(null);\n    }\n    showConfigSidebar() {\n      this.state.configSidebarVisible = true;\n    }\n    showSidebar() {\n      this.state.rightMenuActive = true;\n    }\n    isOverlay() {\n      return this.config().menuMode === 'overlay';\n    }\n    isDesktop() {\n      return window.innerWidth > 991;\n    }\n    isSlim() {\n      return this.config().menuMode === 'slim';\n    }\n    isSlimPlus() {\n      return this.config().menuMode === 'slim-plus';\n    }\n    isHorizontal() {\n      return this.config().menuMode === 'horizontal';\n    }\n    isMobile() {\n      return !this.isDesktop();\n    }\n    onConfigUpdate() {\n      this._config = {\n        ...this.config()\n      };\n      this.configUpdate.next(this.config());\n    }\n    static {\n      this.ɵfac = function LayoutService_Factory(t) {\n        return new (t || LayoutService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: LayoutService,\n        factory: LayoutService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return LayoutService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}