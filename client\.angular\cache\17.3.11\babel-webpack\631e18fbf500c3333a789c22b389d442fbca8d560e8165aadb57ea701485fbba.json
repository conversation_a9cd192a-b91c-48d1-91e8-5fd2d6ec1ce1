{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { OrganizationalComponent } from './organizational.component';\nimport { AddOrgUnitComponent } from './add-org-unit/add-org-unit.component';\nimport { EmployeesComponent } from './organization-details/employees/employees.component';\nimport { FunctionsComponent } from './organization-details/functions/functions.component';\nimport { GeneralComponent } from './organization-details/general/general.component';\nimport { OrganizationDetailsComponent } from './organization-details/organization-details.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: OrganizationalComponent\n}, {\n  path: 'create',\n  component: AddOrgUnitComponent\n}, {\n  path: 'createsub',\n  component: AddOrgUnitComponent\n}, {\n  path: ':id',\n  component: OrganizationDetailsComponent,\n  children: [{\n    path: 'general',\n    component: GeneralComponent\n  }, {\n    path: 'functions',\n    component: FunctionsComponent\n  }, {\n    path: 'employees',\n    component: EmployeesComponent\n  }, {\n    path: '',\n    redirectTo: 'general',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'general',\n    pathMatch: 'full'\n  }]\n}];\nexport class OrganizationalRoutingModule {\n  static {\n    this.ɵfac = function OrganizationalRoutingModule_Factory(t) {\n      return new (t || OrganizationalRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: OrganizationalRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(OrganizationalRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "OrganizationalComponent", "AddOrgUnitComponent", "EmployeesComponent", "FunctionsComponent", "GeneralComponent", "OrganizationDetailsComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "OrganizationalRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\organizational\\organizational-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { OrganizationalComponent } from './organizational.component';\r\nimport { AddOrgUnitComponent } from './add-org-unit/add-org-unit.component';\r\nimport { EmployeesComponent } from './organization-details/employees/employees.component';\r\nimport { FunctionsComponent } from './organization-details/functions/functions.component';\r\nimport { GeneralComponent } from './organization-details/general/general.component';\r\nimport { OrganizationDetailsComponent } from './organization-details/organization-details.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: OrganizationalComponent },\r\n  { path: 'create', component: AddOrgUnitComponent },\r\n  { path: 'createsub', component: AddOrgUnitComponent },\r\n  {\r\n    path: ':id',\r\n    component: OrganizationDetailsComponent,\r\n    children: [\r\n      { path: 'general', component: GeneralComponent },\r\n      { path: 'functions', component: FunctionsComponent },\r\n      { path: 'employees', component: EmployeesComponent },\r\n      { path: '', redirectTo: 'general', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'general', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class OrganizationalRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,SAASC,mBAAmB,QAAQ,uCAAuC;AAC3E,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,gBAAgB,QAAQ,kDAAkD;AACnF,SAASC,4BAA4B,QAAQ,uDAAuD;;;AAEpG,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAER;AAAuB,CAAE,EAChD;EAAEO,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEP;AAAmB,CAAE,EAClD;EAAEM,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEP;AAAmB,CAAE,EACrD;EACEM,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEH,4BAA4B;EACvCI,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEJ;EAAgB,CAAE,EAChD;IAAEG,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEL;EAAkB,CAAE,EACpD;IAAEI,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEN;EAAkB,CAAE,EACpD;IAAEK,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAM,CAAE,EACtD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE3D,CACF;AAMD,OAAM,MAAOC,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH5Bb,YAAY,CAACc,QAAQ,CAACP,MAAM,CAAC,EAC7BP,YAAY;IAAA;EAAA;;;2EAEXa,2BAA2B;IAAAE,OAAA,GAAAC,EAAA,CAAAhB,YAAA;IAAAiB,OAAA,GAF5BjB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}