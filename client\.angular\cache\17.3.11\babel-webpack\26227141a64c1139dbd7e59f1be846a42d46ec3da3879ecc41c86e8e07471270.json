{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class OpportunitiesService {\n  constructor(http) {\n    this.http = http;\n    this.opportunitySubject = new BehaviorSubject(null);\n    this.opportunity = this.opportunitySubject.asObservable();\n  }\n  createOpportunity(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_REGISTRATION}`, {\n      data\n    });\n  }\n  createNote(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\n      data\n    });\n  }\n  updateOpportunity(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_OPPORTUNITY}/${Id}`, {\n      data\n    });\n  }\n  updateNote(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\n      data\n    });\n  }\n  deleteNote(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_NOTE}/${id}`);\n  }\n  getActivityDropdownOptions(type) {\n    const params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', type);\n    return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n      params\n    });\n  }\n  getPartners(params) {\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    }).pipe(map(response => (response?.data || []).map(item => {\n      return {\n        bp_id: item?.bp_id || '',\n        bp_full_name: item?.bp_full_name || ''\n      };\n    })));\n  }\n  getOpportunities(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'opportunity_id,name,probability_percent,createdAt,updatedAt');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][opportunity_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][name][$containsi]', searchTerm);\n      params = params.set('filters[$or][2][opportunity_id][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {\n      params\n    });\n  }\n  getOpportunityByID(opportunityId) {\n    const params = new HttpParams().set('filters[opportunity_id][$eq]', opportunityId).set('populate[notes][populate]', '*');\n    return this.http.get(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {\n      params\n    }).pipe(map(response => {\n      const opportunityDetails = response?.data[0] || null;\n      this.opportunitySubject.next(opportunityDetails);\n      return response;\n    }));\n  }\n  static {\n    this.ɵfac = function OpportunitiesService_Factory(t) {\n      return new (t || OpportunitiesService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: OpportunitiesService,\n      factory: OpportunitiesService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "map", "CMS_APIContstant", "OpportunitiesService", "constructor", "http", "opportunitySubject", "opportunity", "asObservable", "createOpportunity", "data", "post", "CRM_OPPORTUNITY_REGISTRATION", "createNote", "CRM_NOTE", "updateOpportunity", "Id", "put", "CRM_OPPORTUNITY", "updateNote", "deleteNote", "id", "delete", "getActivityDropdownOptions", "type", "params", "set", "get", "CONFIG_DATA", "getPartners", "PARTNERS", "pipe", "response", "item", "bp_id", "bp_full_name", "getOpportunities", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "toString", "undefined", "order", "getOpportunityByID", "opportunityId", "opportunityDetails", "next", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class OpportunitiesService {\r\n  public opportunitySubject = new BehaviorSubject<any>(null);\r\n  public opportunity = this.opportunitySubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  createOpportunity(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_REGISTRATION}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  createNote(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateOpportunity(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_OPPORTUNITY}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateNote(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  deleteNote(id: string) {\r\n    return this.http.delete<any>(`${CMS_APIContstant.CRM_NOTE}/${id}`);\r\n  }\r\n\r\n  getActivityDropdownOptions(type: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', type);\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });\r\n  }\r\n\r\n  getPartners(params: any) {\r\n    return this.http.get<any>(`${CMS_APIContstant.PARTNERS}`, { params }).pipe(\r\n      map((response) =>\r\n        (response?.data || []).map((item: any) => {\r\n          return {\r\n            bp_id: item?.bp_id || '',\r\n            bp_full_name: item?.bp_full_name || '',\r\n          };\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  getOpportunities(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set('fields', 'opportunity_id,name,probability_percent,createdAt,updatedAt');\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set(\r\n        'filters[$or][0][opportunity_id][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set('filters[$or][1][name][$containsi]', searchTerm);\r\n      params = params.set(\r\n        'filters[$or][2][opportunity_id][$containsi]',\r\n        searchTerm\r\n      );\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getOpportunityByID(opportunityId: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[opportunity_id][$eq]', opportunityId)\r\n      .set('populate[notes][populate]', '*');\r\n\r\n    return this.http\r\n      .get<any[]>(`${CMS_APIContstant.CRM_OPPORTUNITY}`, { params })\r\n      .pipe(\r\n        map((response: any) => {\r\n          const opportunityDetails = response?.data[0] || null;\r\n          this.opportunitySubject.next(opportunityDetails);\r\n          return response;\r\n        })\r\n      );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,oBAAoB;EAI/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,kBAAkB,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IACnD,KAAAO,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;EAEpB;EAEvCC,iBAAiBA,CAACC,IAAS;IACzB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACU,4BAA4B,EAAE,EAAE;MACxEF;KACD,CAAC;EACJ;EAEAG,UAAUA,CAACH,IAAS;IAClB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACY,QAAQ,EAAE,EAAE;MACpDJ;KACD,CAAC;EACJ;EAEAK,iBAAiBA,CAACC,EAAU,EAAEN,IAAS;IACrC,OAAO,IAAI,CAACL,IAAI,CAACY,GAAG,CAAC,GAAGf,gBAAgB,CAACgB,eAAe,IAAIF,EAAE,EAAE,EAAE;MAChEN;KACD,CAAC;EACJ;EAEAS,UAAUA,CAACH,EAAU,EAAEN,IAAS;IAC9B,OAAO,IAAI,CAACL,IAAI,CAACY,GAAG,CAAC,GAAGf,gBAAgB,CAACY,QAAQ,IAAIE,EAAE,EAAE,EAAE;MACzDN;KACD,CAAC;EACJ;EAEAU,UAAUA,CAACC,EAAU;IACnB,OAAO,IAAI,CAAChB,IAAI,CAACiB,MAAM,CAAM,GAAGpB,gBAAgB,CAACY,QAAQ,IAAIO,EAAE,EAAE,CAAC;EACpE;EAEAE,0BAA0BA,CAACC,IAAY;IACrC,MAAMC,MAAM,GAAG,IAAI1B,UAAU,EAAE,CAC5B2B,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAEF,IAAI,CAAC;IAElC,OAAO,IAAI,CAACnB,IAAI,CAACsB,GAAG,CAAM,GAAGzB,gBAAgB,CAAC0B,WAAW,EAAE,EAAE;MAAEH;IAAM,CAAE,CAAC;EAC1E;EAEAI,WAAWA,CAACJ,MAAW;IACrB,OAAO,IAAI,CAACpB,IAAI,CAACsB,GAAG,CAAM,GAAGzB,gBAAgB,CAAC4B,QAAQ,EAAE,EAAE;MAAEL;IAAM,CAAE,CAAC,CAACM,IAAI,CACxE9B,GAAG,CAAE+B,QAAQ,IACX,CAACA,QAAQ,EAAEtB,IAAI,IAAI,EAAE,EAAET,GAAG,CAAEgC,IAAS,IAAI;MACvC,OAAO;QACLC,KAAK,EAAED,IAAI,EAAEC,KAAK,IAAI,EAAE;QACxBC,YAAY,EAAEF,IAAI,EAAEE,YAAY,IAAI;OACrC;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEAC,gBAAgBA,CACdC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIhB,MAAM,GAAG,IAAI1B,UAAU,EAAE,CAC1B2B,GAAG,CAAC,kBAAkB,EAAEW,IAAI,CAACK,QAAQ,EAAE,CAAC,CACxChB,GAAG,CAAC,sBAAsB,EAAEY,QAAQ,CAACI,QAAQ,EAAE,CAAC,CAChDhB,GAAG,CAAC,QAAQ,EAAE,6DAA6D,CAAC;IAE/E,IAAIa,SAAS,IAAIC,SAAS,KAAKG,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGJ,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9Cf,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGa,SAAS,IAAIK,KAAK,EAAE,CAAC;IACtD;IAEA,IAAIH,UAAU,EAAE;MACdhB,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,6CAA6C,EAC7Ce,UAAU,CACX;MACDhB,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,mCAAmC,EAAEe,UAAU,CAAC;MACpEhB,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,6CAA6C,EAC7Ce,UAAU,CACX;IACH;IAEA,OAAO,IAAI,CAACpC,IAAI,CAACsB,GAAG,CAAQ,GAAGzB,gBAAgB,CAACgB,eAAe,EAAE,EAAE;MACjEO;KACD,CAAC;EACJ;EAEAoB,kBAAkBA,CAACC,aAAqB;IACtC,MAAMrB,MAAM,GAAG,IAAI1B,UAAU,EAAE,CAC5B2B,GAAG,CAAC,8BAA8B,EAAEoB,aAAa,CAAC,CAClDpB,GAAG,CAAC,2BAA2B,EAAE,GAAG,CAAC;IAExC,OAAO,IAAI,CAACrB,IAAI,CACbsB,GAAG,CAAQ,GAAGzB,gBAAgB,CAACgB,eAAe,EAAE,EAAE;MAAEO;IAAM,CAAE,CAAC,CAC7DM,IAAI,CACH9B,GAAG,CAAE+B,QAAa,IAAI;MACpB,MAAMe,kBAAkB,GAAGf,QAAQ,EAAEtB,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;MACpD,IAAI,CAACJ,kBAAkB,CAAC0C,IAAI,CAACD,kBAAkB,CAAC;MAChD,OAAOf,QAAQ;IACjB,CAAC,CAAC,CACH;EACL;;;uBAvGW7B,oBAAoB,EAAA8C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBjD,oBAAoB;MAAAkD,OAAA,EAApBlD,oBAAoB,CAAAmD,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}