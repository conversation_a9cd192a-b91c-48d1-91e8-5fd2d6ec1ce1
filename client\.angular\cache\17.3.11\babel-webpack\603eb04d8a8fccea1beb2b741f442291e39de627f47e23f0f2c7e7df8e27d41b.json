{"ast": null, "code": "import stateList from './assets/state.json';\nimport { findEntryByCode, findStateByCodeAndCountryCode, compare } from './utils';\n// Get a list of all states.\nexport function getAllStates() {\n  return stateList;\n}\n// Get a list of states belonging to a specific country.\nexport function getStatesOfCountry(countryCode = '') {\n  if (!countryCode) return [];\n  // get data from file or cache\n  const states = stateList.filter(value => {\n    return value.countryCode === countryCode;\n  });\n  return states.sort(compare);\n}\nexport function getStateByCodeAndCountry(stateCode, countryCode) {\n  if (!stateCode) return undefined;\n  if (!countryCode) return undefined;\n  return findStateByCodeAndCountryCode(stateList, stateCode, countryCode);\n}\n// to be deprecate\nexport function getStateByCode(isoCode) {\n  // eslint-disable-next-line no-console\n  console.warn(`WARNING! 'getStateByCode' has been deprecated, please use the new 'getStateByCodeAndCountry' function instead!`);\n  if (!isoCode) return undefined;\n  return findEntryByCode(stateList, isoCode);\n}\nfunction sortByIsoCode(countries) {\n  return countries.sort((a, b) => {\n    return compare(a, b, entity => {\n      return `${entity.countryCode}-${entity.isoCode}`;\n    });\n  });\n}\nexport default {\n  getAllStates,\n  getStatesOfCountry,\n  getStateByCodeAndCountry,\n  getStateByCode,\n  sortByIsoCode\n};", "map": {"version": 3, "names": ["stateList", "findEntryByCode", "findStateByCodeAndCountryCode", "compare", "getAllStates", "getStatesOfCountry", "countryCode", "states", "filter", "value", "sort", "getStateByCodeAndCountry", "stateCode", "undefined", "getStateByCode", "isoCode", "console", "warn", "sortByIsoCode", "countries", "a", "b", "entity"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/country-state-city/lib/state.js"], "sourcesContent": ["import stateList from './assets/state.json';\nimport { findEntryByCode, findStateByCodeAndCountryCode, compare } from './utils';\n// Get a list of all states.\nexport function getAllStates() {\n    return stateList;\n}\n// Get a list of states belonging to a specific country.\nexport function getStatesOfCountry(countryCode = '') {\n    if (!countryCode)\n        return [];\n    // get data from file or cache\n    const states = stateList.filter((value) => {\n        return value.countryCode === countryCode;\n    });\n    return states.sort(compare);\n}\nexport function getStateByCodeAndCountry(stateCode, countryCode) {\n    if (!stateCode)\n        return undefined;\n    if (!countryCode)\n        return undefined;\n    return findStateByCodeAndCountryCode(stateList, stateCode, countryCode);\n}\n// to be deprecate\nexport function getStateByCode(isoCode) {\n    // eslint-disable-next-line no-console\n    console.warn(`WARNING! 'getStateByCode' has been deprecated, please use the new 'getStateByCodeAndCountry' function instead!`);\n    if (!isoCode)\n        return undefined;\n    return findEntryByCode(stateList, isoCode);\n}\nfunction sortByIsoCode(countries) {\n    return countries.sort((a, b) => {\n        return compare(a, b, (entity) => {\n            return `${entity.countryCode}-${entity.isoCode}`;\n        });\n    });\n}\nexport default {\n    getAllStates,\n    getStatesOfCountry,\n    getStateByCodeAndCountry,\n    getStateByCode,\n    sortByIsoCode,\n};\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,qBAAqB;AAC3C,SAASC,eAAe,EAAEC,6BAA6B,EAAEC,OAAO,QAAQ,SAAS;AACjF;AACA,OAAO,SAASC,YAAYA,CAAA,EAAG;EAC3B,OAAOJ,SAAS;AACpB;AACA;AACA,OAAO,SAASK,kBAAkBA,CAACC,WAAW,GAAG,EAAE,EAAE;EACjD,IAAI,CAACA,WAAW,EACZ,OAAO,EAAE;EACb;EACA,MAAMC,MAAM,GAAGP,SAAS,CAACQ,MAAM,CAAEC,KAAK,IAAK;IACvC,OAAOA,KAAK,CAACH,WAAW,KAAKA,WAAW;EAC5C,CAAC,CAAC;EACF,OAAOC,MAAM,CAACG,IAAI,CAACP,OAAO,CAAC;AAC/B;AACA,OAAO,SAASQ,wBAAwBA,CAACC,SAAS,EAAEN,WAAW,EAAE;EAC7D,IAAI,CAACM,SAAS,EACV,OAAOC,SAAS;EACpB,IAAI,CAACP,WAAW,EACZ,OAAOO,SAAS;EACpB,OAAOX,6BAA6B,CAACF,SAAS,EAAEY,SAAS,EAAEN,WAAW,CAAC;AAC3E;AACA;AACA,OAAO,SAASQ,cAAcA,CAACC,OAAO,EAAE;EACpC;EACAC,OAAO,CAACC,IAAI,CAAE,gHAA+G,CAAC;EAC9H,IAAI,CAACF,OAAO,EACR,OAAOF,SAAS;EACpB,OAAOZ,eAAe,CAACD,SAAS,EAAEe,OAAO,CAAC;AAC9C;AACA,SAASG,aAAaA,CAACC,SAAS,EAAE;EAC9B,OAAOA,SAAS,CAACT,IAAI,CAAC,CAACU,CAAC,EAAEC,CAAC,KAAK;IAC5B,OAAOlB,OAAO,CAACiB,CAAC,EAAEC,CAAC,EAAGC,MAAM,IAAK;MAC7B,OAAQ,GAAEA,MAAM,CAAChB,WAAY,IAAGgB,MAAM,CAACP,OAAQ,EAAC;IACpD,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,eAAe;EACXX,YAAY;EACZC,kBAAkB;EAClBM,wBAAwB;EACxBG,cAAc;EACdI;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}