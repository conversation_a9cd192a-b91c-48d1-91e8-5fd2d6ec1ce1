{"ast": null, "code": "import { Subject, forkJoin } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./organizational.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/breadcrumb\";\nimport * as i9 from \"primeng/table\";\nimport * as i10 from \"primeng/checkbox\";\nimport * as i11 from \"primeng/multiselect\";\nconst _c0 = [\"dt1\"];\nconst _c1 = (a0, a1) => ({\n  \"pi-chevron-down\": a0,\n  \"pi-chevron-right\": a1\n});\nconst _c2 = (a0, a1) => ({\n  $implicit: a0,\n  indent: a1\n});\nconst _c3 = a0 => ({\n  $implicit: a0,\n  indent: 12\n});\nfunction OrganizationalComponent_ng_template_15_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.sortOrderOrg === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction OrganizationalComponent_ng_template_15_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n}\nfunction OrganizationalComponent_ng_template_15_ng_container_8_th_1_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.sortOrderOrg === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction OrganizationalComponent_ng_template_15_ng_container_8_th_1_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n}\nfunction OrganizationalComponent_ng_template_15_ng_container_8_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 30);\n    i0.ɵɵlistener(\"click\", function OrganizationalComponent_ng_template_15_ng_container_8_th_1_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const col_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.customSort(col_r6.field, ctx_r3.organization, \"ORG\"));\n    });\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, OrganizationalComponent_ng_template_15_ng_container_8_th_1_i_3_Template, 1, 1, \"i\", 24)(4, OrganizationalComponent_ng_template_15_ng_container_8_th_1_i_4_Template, 1, 0, \"i\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const col_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"pSortableColumn\", col_r6.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r6.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortFieldOrg === col_r6.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortFieldOrg !== col_r6.field);\n  }\n}\nfunction OrganizationalComponent_ng_template_15_ng_container_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 31)(1, \"div\", 32);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const col_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r6.header, \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_15_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, OrganizationalComponent_ng_template_15_ng_container_8_th_1_Template, 5, 4, \"th\", 29)(2, OrganizationalComponent_ng_template_15_ng_container_8_ng_template_2_Template, 3, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    const nonSortableColumn_r7 = i0.ɵɵreference(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", col_r6.field !== \"sales_organisation_indicator\" && col_r6.field !== \"sales_indicator\" && col_r6.field !== \"reporting_line_indicator\")(\"ngIfElse\", nonSortableColumn_r7);\n  }\n}\nfunction OrganizationalComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 21);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 22);\n    i0.ɵɵlistener(\"click\", function OrganizationalComponent_ng_template_15_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.customSort(\"name\", ctx_r3.organization, \"ORG\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 23);\n    i0.ɵɵtext(5, \" Name \");\n    i0.ɵɵtemplate(6, OrganizationalComponent_ng_template_15_i_6_Template, 1, 1, \"i\", 24)(7, OrganizationalComponent_ng_template_15_i_7_Template, 1, 0, \"i\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, OrganizationalComponent_ng_template_15_ng_container_8_Template, 4, 2, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortFieldOrg === \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortFieldOrg !== \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedOrgColumns);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function OrganizationalComponent_ng_template_16_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const organization_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggle(organization_r9));\n    });\n    i0.ɵɵelement(1, \"span\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const organization_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c1, organization_r9.expanded, !organization_r9.expanded));\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (organization_r9 == null ? null : organization_r9.crm_org_unit_managers == null ? null : organization_r9.crm_org_unit_managers[0] == null ? null : organization_r9.crm_org_unit_managers[0].business_partner == null ? null : organization_r9.crm_org_unit_managers[0].business_partner.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (organization_r9 == null ? null : organization_r9.parent_organisational_unit == null ? null : organization_r9.parent_organisational_unit.name) || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (organization_r9 == null ? null : organization_r9.organisational_unit_id) || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (organization_r9 == null ? null : organization_r9.parent_organisational_unit_id) || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 45);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", true)(\"ngModel\", organization_r9 == null ? null : organization_r9.crm_org_unit_functions == null ? null : organization_r9.crm_org_unit_functions[0] == null ? null : organization_r9.crm_org_unit_functions[0].sales_organisation_indicator);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 45);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", true)(\"ngModel\", organization_r9 == null ? null : organization_r9.crm_org_unit_functions == null ? null : organization_r9.crm_org_unit_functions[0] == null ? null : organization_r9.crm_org_unit_functions[0].sales_indicator);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 45);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", true)(\"ngModel\", organization_r9 == null ? null : organization_r9.crm_org_unit_functions == null ? null : organization_r9.crm_org_unit_functions[0] == null ? null : organization_r9.crm_org_unit_functions[0].reporting_line_indicator);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 43);\n    i0.ɵɵtemplate(3, OrganizationalComponent_ng_template_16_ng_container_8_ng_container_3_Template, 2, 1, \"ng-container\", 44)(4, OrganizationalComponent_ng_template_16_ng_container_8_ng_container_4_Template, 2, 1, \"ng-container\", 44)(5, OrganizationalComponent_ng_template_16_ng_container_8_ng_container_5_Template, 2, 1, \"ng-container\", 44)(6, OrganizationalComponent_ng_template_16_ng_container_8_ng_container_6_Template, 2, 1, \"ng-container\", 44)(7, OrganizationalComponent_ng_template_16_ng_container_8_ng_container_7_Template, 2, 3, \"ng-container\", 44)(8, OrganizationalComponent_ng_template_16_ng_container_8_ng_container_8_Template, 2, 3, \"ng-container\", 44)(9, OrganizationalComponent_ng_template_16_ng_container_8_ng_container_9_Template, 2, 3, \"ng-container\", 44);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r10.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"crm_org_unit_managers.business_partner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"parent_organisational_unit.name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"organisational_unit_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"parent_organisational_unit_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"sales_organisation_indicator\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"sales_indicator\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"reporting_line_indicator\");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function OrganizationalComponent_ng_template_16_ng_template_9_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const node_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggle(node_r12));\n    });\n    i0.ɵɵelement(1, \"span\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const node_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c1, node_r12.expanded, !node_r12.expanded));\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (node_r12.crm_org_unit_managers == null ? null : node_r12.crm_org_unit_managers[0] == null ? null : node_r12.crm_org_unit_managers[0].business_partner == null ? null : node_r12.crm_org_unit_managers[0].business_partner.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (node_r12.parent_organisational_unit == null ? null : node_r12.parent_organisational_unit.name) || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", node_r12.organisational_unit_id || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", node_r12.parent_organisational_unit_id || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 45);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", true)(\"ngModel\", node_r12.crm_org_unit_functions == null ? null : node_r12.crm_org_unit_functions[0] == null ? null : node_r12.crm_org_unit_functions[0].sales_organisation_indicator);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 45);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", true)(\"ngModel\", node_r12.crm_org_unit_functions == null ? null : node_r12.crm_org_unit_functions[0] == null ? null : node_r12.crm_org_unit_functions[0].sales_indicator);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 45);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", true)(\"ngModel\", node_r12.crm_org_unit_functions == null ? null : node_r12.crm_org_unit_functions[0] == null ? null : node_r12.crm_org_unit_functions[0].reporting_line_indicator);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r13 = i0.ɵɵnextContext().$implicit;\n    const node_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", node_r12[col_r13.field] || \"-\", \" \");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 43);\n    i0.ɵɵtemplate(3, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_3_Template, 2, 1, \"ng-container\", 44)(4, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_4_Template, 2, 1, \"ng-container\", 44)(5, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_5_Template, 2, 1, \"ng-container\", 44)(6, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_6_Template, 2, 1, \"ng-container\", 44)(7, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_7_Template, 2, 3, \"ng-container\", 44)(8, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_8_Template, 2, 3, \"ng-container\", 44)(9, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_9_Template, 2, 3, \"ng-container\", 44)(10, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_10_Template, 2, 1, \"ng-container\", 49);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r13.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"crm_org_unit_managers.business_partner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"parent_organisational_unit.name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"organisational_unit_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"parent_organisational_unit_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"sales_organisation_indicator\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"sales_indicator\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"reporting_line_indicator\");\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 50);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const child_r14 = ctx.$implicit;\n    const indent_r15 = i0.ɵɵnextContext(3).indent;\n    i0.ɵɵnextContext();\n    const rowTpl_r16 = i0.ɵɵreference(10);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", rowTpl_r16)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c2, child_r14, indent_r15 + 16));\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_ng_container_1_Template, 2, 5, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const wrap_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", wrap_r17.data);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_Template, 2, 1, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", node_r12.details);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 46)(1, \"td\", 47);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 36)(4, \"div\", 48);\n    i0.ɵɵtemplate(5, OrganizationalComponent_ng_template_16_ng_template_9_button_5_Template, 2, 4, \"button\", 38);\n    i0.ɵɵelementStart(6, \"a\", 39);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_Template, 11, 8, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_Template, 2, 1, \"ng-container\", 40);\n  }\n  if (rf & 2) {\n    const node_r12 = ctx.$implicit;\n    const indent_r15 = ctx.indent;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", node_r12);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"margin-left\", indent_r15, \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", node_r12.child_organisational_units == null ? null : node_r12.child_organisational_units.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/organization/\" + node_r12.organisational_unit_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", node_r12.name || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedOrgColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", node_r12.expanded);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 50);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const child_r18 = ctx.$implicit;\n    i0.ɵɵnextContext(3);\n    const rowTpl_r16 = i0.ɵɵreference(10);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", rowTpl_r16)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, child_r18));\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_ng_container_1_Template, 2, 4, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const wrap_r19 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", wrap_r19.data);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_Template, 2, 1, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const organization_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", organization_r9.details);\n  }\n}\nfunction OrganizationalComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 33)(1, \"td\", 34);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 36)(4, \"div\", 37);\n    i0.ɵɵtemplate(5, OrganizationalComponent_ng_template_16_button_5_Template, 2, 4, \"button\", 38);\n    i0.ɵɵelementStart(6, \"a\", 39);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, OrganizationalComponent_ng_template_16_ng_container_8_Template, 10, 8, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, OrganizationalComponent_ng_template_16_ng_template_9_Template, 10, 8, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(11, OrganizationalComponent_ng_template_16_ng_container_11_Template, 2, 1, \"ng-container\", 40);\n  }\n  if (rf & 2) {\n    const organization_r9 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", organization_r9);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (organization_r9 == null ? null : organization_r9.child_organisational_units == null ? null : organization_r9.child_organisational_units.length) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/organization/\" + organization_r9.organisational_unit_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", organization_r9.name || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedOrgColumns);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", organization_r9.expanded);\n  }\n}\nfunction OrganizationalComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 51);\n    i0.ɵɵtext(2, \"No organization found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OrganizationalComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 51);\n    i0.ɵɵtext(2, \"Loading organization data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class OrganizationalComponent {\n  constructor(router, organizationalservice) {\n    this.router = router;\n    this.organizationalservice = organizationalservice;\n    this.unsubscribe$ = new Subject();\n    this.organization = [];\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n    this.selectedOrganizations = [];\n    this.createOptions = [];\n    this._selectedOrgColumns = [];\n    this.OrgCols = [{\n      field: 'crm_org_unit_managers.business_partner.bp_full_name',\n      header: 'Manager'\n    }, {\n      field: 'parent_organisational_unit.name',\n      header: 'Parent Unit Name'\n    }, {\n      field: 'organisational_unit_id',\n      header: 'ID'\n    }, {\n      field: 'parent_organisational_unit_id',\n      header: 'Parent Unit ID'\n    }, {\n      field: 'sales_organisation_indicator',\n      header: 'Sales Organization'\n    }, {\n      field: 'sales_indicator',\n      header: 'Sales'\n    }, {\n      field: 'reporting_line_indicator',\n      header: 'Reporting Line'\n    }];\n    this.sortFieldOrg = '';\n    this.sortOrderOrg = 1;\n    this.trackByUnit = (_, u) => u?.organisational_unit_id ?? _;\n  }\n  ngOnInit() {\n    this.breadcrumbitems = [{\n      label: 'Organization',\n      routerLink: ['/store/organization']\n    }];\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this._selectedOrgColumns = this.OrgCols;\n    this.updateCreateOptions();\n  }\n  get selectedOrgColumns() {\n    return this._selectedOrgColumns;\n  }\n  set selectedOrgColumns(val) {\n    this._selectedOrgColumns = this.OrgCols.filter(col => val.includes(col));\n  }\n  onOrgColumnReorder(event) {\n    const draggedCol = this.OrgCols[event.dragIndex];\n    this.OrgCols.splice(event.dragIndex, 1);\n    this.OrgCols.splice(event.dropIndex, 0, draggedCol);\n  }\n  customSort(field, data, type) {\n    if (type === 'ORG') {\n      if (this.sortFieldOrg === field) {\n        // Toggle sort order if same column is clicked\n        this.sortOrderOrg = this.sortOrderOrg === 1 ? -1 : 1;\n      } else {\n        // Reset to ascending when changing columns\n        this.sortFieldOrg = field;\n        this.sortOrderOrg = 1;\n      }\n    }\n    data.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrderOrg * result;\n    });\n  }\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      let fields = field.split('.');\n      let value = data;\n      for (let i = 0; i < fields.length; i++) {\n        if (value == null) return null;\n        value = value[fields[i]];\n      }\n      return value;\n    }\n  }\n  updateCreateOptions() {\n    this.createOptions = [{\n      label: 'Org Unit',\n      value: 'org-unit',\n      disabled: false\n    }, {\n      label: 'SubOrg Unit',\n      value: 'sub-unit',\n      disabled: !(this.selectedOrganizations?.length > 0)\n    }];\n  }\n  loadOrganization(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    const sortField = event.sortField;\n    const sortOrder = event.sortOrder;\n    this.organizationalservice.getOrganization(page, pageSize, sortField, sortOrder, this.globalSearchTerm).subscribe({\n      next: response => {\n        this.organization = response?.data || [];\n        this.totalRecords = response?.meta?.pagination.total;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching organization', error);\n        this.loading = false;\n      }\n    });\n  }\n  create(event) {\n    const selectedType = event?.value?.value;\n    if (selectedType === 'sub-unit' && this.selectedOrganizations?.length) {\n      const selected = this.selectedOrganizations[0];\n      const parentUnitId = selected?.organisational_unit_id;\n      this.router.navigate(['/store/organization/createsub'], {\n        state: {\n          parentUnitId\n        }\n      });\n    } else {\n      this.router.navigate(['/store/organization/create']);\n    }\n  }\n  toggle(organization) {\n    organization.expanded = !organization.expanded;\n    if (!organization.expanded) {\n      return;\n    }\n    const childIds = organization?.child_organisational_units?.map(c => c?.organisational_unit_id)?.filter(id => !!id) || [];\n    if (childIds.length === 0) {\n      organization.expanded = false;\n      return;\n    }\n    forkJoin(childIds.map(id => this.organizationalservice.getOrganizationByChildID(id))).pipe(map(resArray => resArray.flatMap(res => res ?? []))).subscribe({\n      next: units => {\n        organization.details = units;\n      },\n      error: err => {\n        console.error('Error loading expanded data', err);\n        organization.expanded = false;\n      }\n    });\n  }\n  onGlobalFilter(table, event) {\n    this.loadOrganization({\n      first: 0,\n      rows: 10\n    });\n  }\n  signup() {\n    this.router.navigate(['/store/organization/create']);\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function OrganizationalComponent_Factory(t) {\n      return new (t || OrganizationalComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.OrganizationalService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OrganizationalComponent,\n      selectors: [[\"app-organizational\"]],\n      viewQuery: function OrganizationalComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dt1 = _t.first);\n        }\n      },\n      decls: 19,\n      vars: 19,\n      consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [\"nonSortableColumn\", \"\"], [\"rowTpl\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-start\", \"justify-content-between\", \"flex-column\", \"gap-2\", \"md:flex-row\", \"md:align-items-center\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-start\", \"gap-3\", \"md:flex-row\", \"md:align-items-center\", \"flex-wrap\", \"w-full\", \"md:w-auto\"], [1, \"h-search-box\", \"w-full\", \"sm:w-24rem\"], [1, \"p-input-icon-right\", \"w-full\", \"md:w-24rem\"], [\"type\", \"text\", \"placeholder\", \"Search Organization\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"sm:w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"placeholder\", \"Create\", \"optionLabel\", \"label\", 1, \"w-full\", \"sm:w-13rem\", 3, \"onChange\", \"options\", \"styleClass\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"w-full\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onLazyLoad\", \"onColReorder\", \"selectionChange\", \"value\", \"rows\", \"paginator\", \"loading\", \"lazy\", \"totalRecords\", \"scrollable\", \"reorderableColumns\", \"selection\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"pSortableColumn\", \"click\", 4, \"ngIf\", \"ngIfElse\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [\"pReorderableColumn\", \"\"], [1, \"flex\", \"align-items-center\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"pButton\", \"\", \"type\", \"button\", \"class\", \"p-button p-button-text p-button-rounded p-button-plain p-button-icon-only p-button-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"text-orange-600\", \"font-medium\", \"underline\", 3, \"routerLink\"], [4, \"ngIf\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button\", \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", \"p-button-icon-only\", \"p-button-sm\", 3, \"click\"], [1, \"pi\", 2, \"font-size\", \"0.75rem\", 3, \"ngClass\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [3, \"binary\", \"disabled\", \"ngModel\"], [1, \"bg-surface-50\", \"text-sm\", \"org-child-row\"], [\"pFrozenColumn\", \"\", 1, \"pl-3\", \"w-2rem\", \"text-center\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"ml-5\"], [4, \"ngSwitchDefault\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"colspan\", \"10\", 1, \"border-round-left-lg\"]],\n      template: function OrganizationalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 8)(5, \"div\", 9)(6, \"span\", 10)(7, \"input\", 11, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function OrganizationalComponent_Template_input_ngModelChange_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function OrganizationalComponent_Template_input_input_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const dt1_r2 = i0.ɵɵreference(14);\n            return i0.ɵɵresetView(ctx.onGlobalFilter(dt1_r2, $event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"i\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"p-dropdown\", 13);\n          i0.ɵɵlistener(\"onChange\", function OrganizationalComponent_Template_p_dropdown_onChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.create($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p-multiSelect\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function OrganizationalComponent_Template_p_multiSelect_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedOrgColumns, $event) || (ctx.selectedOrgColumns = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 15)(13, \"p-table\", 16, 1);\n          i0.ɵɵlistener(\"onLazyLoad\", function OrganizationalComponent_Template_p_table_onLazyLoad_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadOrganization($event));\n          })(\"onColReorder\", function OrganizationalComponent_Template_p_table_onColReorder_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onOrgColumnReorder($event));\n          });\n          i0.ɵɵtwoWayListener(\"selectionChange\", function OrganizationalComponent_Template_p_table_selectionChange_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedOrganizations, $event) || (ctx.selectedOrganizations = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectionChange\", function OrganizationalComponent_Template_p_table_selectionChange_13_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.updateCreateOptions());\n          });\n          i0.ɵɵtemplate(15, OrganizationalComponent_ng_template_15_Template, 9, 3, \"ng-template\", 17)(16, OrganizationalComponent_ng_template_16_Template, 12, 6, \"ng-template\", 18)(17, OrganizationalComponent_ng_template_17_Template, 3, 0, \"ng-template\", 19)(18, OrganizationalComponent_ng_template_18_Template, 3, 0, \"ng-template\", 20);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"options\", ctx.createOptions)(\"styleClass\", \"w-full sm:w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.OrgCols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedOrgColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.organization)(\"rows\", 14)(\"paginator\", true)(\"loading\", ctx.loading)(\"paginator\", true)(\"lazy\", true)(\"totalRecords\", ctx.totalRecords)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵtwoWayProperty(\"selection\", ctx.selectedOrganizations);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgTemplateOutlet, i3.NgSwitch, i3.NgSwitchCase, i3.NgSwitchDefault, i1.RouterLink, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.ButtonDirective, i6.PrimeTemplate, i7.Dropdown, i8.Breadcrumb, i9.Table, i9.SortableColumn, i9.FrozenColumn, i9.ReorderableColumn, i9.TableCheckbox, i9.TableHeaderCheckbox, i10.Checkbox, i11.MultiSelect],\n      styles: [\".org-child-row {\\n  font-size: 14px !important; \\n\\n}\\n\\n  .org-child-row a {\\n  font-size: 14px !important;\\n}\\n\\n  .org-child-row td {\\n  padding-top: 6px !important;\\n  padding-bottom: 6px !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvb3JnYW5pemF0aW9uYWwvb3JnYW5pemF0aW9uYWwuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSwwQkFBQSxFQUFBLGlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSwwQkFBQTtBQUNGOztBQUVBO0VBQ0UsMkJBQUE7RUFDQSw4QkFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwIC5vcmctY2hpbGQtcm93IHtcclxuICBmb250LXNpemU6IDE0cHggIWltcG9ydGFudDsgLyogc21hbGxlciBmb250ICovXHJcbn1cclxuXHJcbjo6bmctZGVlcCAub3JnLWNoaWxkLXJvdyBhIHtcclxuICBmb250LXNpemU6IDE0cHggIWltcG9ydGFudDtcclxufVxyXG5cclxuOjpuZy1kZWVwIC5vcmctY2hpbGQtcm93IHRkIHtcclxuICBwYWRkaW5nLXRvcDogNnB4ICFpbXBvcnRhbnQ7XHJcbiAgcGFkZGluZy1ib3R0b206IDZweCAhaW1wb3J0YW50O1xyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "fork<PERSON><PERSON>n", "map", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r3", "sortOrderOrg", "ɵɵelementStart", "ɵɵlistener", "OrganizationalComponent_ng_template_15_ng_container_8_th_1_Template_th_click_0_listener", "ɵɵrestoreView", "_r5", "col_r6", "ɵɵnextContext", "$implicit", "ɵɵresetView", "customSort", "field", "organization", "ɵɵtext", "ɵɵtemplate", "OrganizationalComponent_ng_template_15_ng_container_8_th_1_i_3_Template", "OrganizationalComponent_ng_template_15_ng_container_8_th_1_i_4_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortFieldOrg", "ɵɵelementContainerStart", "OrganizationalComponent_ng_template_15_ng_container_8_th_1_Template", "OrganizationalComponent_ng_template_15_ng_container_8_ng_template_2_Template", "ɵɵtemplateRefExtractor", "nonSortableColumn_r7", "OrganizationalComponent_ng_template_15_Template_th_click_3_listener", "_r3", "OrganizationalComponent_ng_template_15_i_6_Template", "OrganizationalComponent_ng_template_15_i_7_Template", "OrganizationalComponent_ng_template_15_ng_container_8_Template", "selectedOrgColumns", "OrganizationalComponent_ng_template_16_button_5_Template_button_click_0_listener", "_r8", "organization_r9", "toggle", "ɵɵpureFunction2", "_c1", "expanded", "crm_org_unit_managers", "business_partner", "bp_full_name", "parent_organisational_unit", "name", "organisational_unit_id", "parent_organisational_unit_id", "crm_org_unit_functions", "sales_organisation_indicator", "sales_indicator", "reporting_line_indicator", "OrganizationalComponent_ng_template_16_ng_container_8_ng_container_3_Template", "OrganizationalComponent_ng_template_16_ng_container_8_ng_container_4_Template", "OrganizationalComponent_ng_template_16_ng_container_8_ng_container_5_Template", "OrganizationalComponent_ng_template_16_ng_container_8_ng_container_6_Template", "OrganizationalComponent_ng_template_16_ng_container_8_ng_container_7_Template", "OrganizationalComponent_ng_template_16_ng_container_8_ng_container_8_Template", "OrganizationalComponent_ng_template_16_ng_container_8_ng_container_9_Template", "col_r10", "OrganizationalComponent_ng_template_16_ng_template_9_button_5_Template_button_click_0_listener", "_r11", "node_r12", "col_r13", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_3_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_4_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_5_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_6_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_7_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_8_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_9_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_ng_container_10_Template", "ɵɵelementContainer", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_ng_container_1_ng_container_1_Template", "rowTpl_r16", "_c2", "child_r14", "indent_r15", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_ng_container_1_Template", "wrap_r17", "data", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_ng_container_1_Template", "details", "OrganizationalComponent_ng_template_16_ng_template_9_button_5_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_8_Template", "OrganizationalComponent_ng_template_16_ng_template_9_ng_container_9_Template", "ɵɵstyleProp", "child_organisational_units", "length", "OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_ng_container_1_ng_container_1_Template", "ɵɵpureFunction1", "_c3", "child_r18", "OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_ng_container_1_Template", "wrap_r19", "OrganizationalComponent_ng_template_16_ng_container_11_ng_container_1_Template", "OrganizationalComponent_ng_template_16_button_5_Template", "OrganizationalComponent_ng_template_16_ng_container_8_Template", "OrganizationalComponent_ng_template_16_ng_template_9_Template", "OrganizationalComponent_ng_template_16_ng_container_11_Template", "OrganizationalComponent", "constructor", "router", "organizationalservice", "unsubscribe$", "totalRecords", "loading", "globalSearchTerm", "selectedOrganizations", "createOptions", "_selectedOrgColumns", "OrgCols", "trackByUnit", "_", "u", "ngOnInit", "breadcrumbitems", "label", "routerLink", "home", "icon", "updateCreateOptions", "val", "filter", "col", "includes", "onOrgColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "type", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "indexOf", "fields", "split", "value", "i", "disabled", "loadOrganization", "page", "first", "rows", "pageSize", "sortField", "sortOrder", "getOrganization", "subscribe", "next", "response", "meta", "pagination", "total", "error", "console", "create", "selectedType", "selected", "parentUnitId", "navigate", "state", "childIds", "c", "id", "getOrganizationByChildID", "pipe", "resArray", "flatMap", "res", "units", "err", "onGlobalFilter", "table", "signup", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "Router", "i2", "OrganizationalService", "selectors", "viewQuery", "OrganizationalComponent_Query", "rf", "ctx", "ɵɵtwoWayListener", "OrganizationalComponent_Template_input_ngModelChange_7_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "OrganizationalComponent_Template_input_input_7_listener", "dt1_r2", "ɵɵreference", "OrganizationalComponent_Template_p_dropdown_onChange_10_listener", "OrganizationalComponent_Template_p_multiSelect_ngModelChange_11_listener", "OrganizationalComponent_Template_p_table_onLazyLoad_13_listener", "OrganizationalComponent_Template_p_table_onColReorder_13_listener", "OrganizationalComponent_Template_p_table_selectionChange_13_listener", "OrganizationalComponent_ng_template_15_Template", "OrganizationalComponent_ng_template_16_Template", "OrganizationalComponent_ng_template_17_Template", "OrganizationalComponent_ng_template_18_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\organizational\\organizational.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\organizational\\organizational.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Subject, forkJoin } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { Table } from 'primeng/table';\r\nimport { Router } from '@angular/router';\r\nimport { OrganizationalService } from './organizational.service';\r\nimport { DropdownChangeEvent } from 'primeng/dropdown';\r\n\r\ninterface OrgColumn {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\ninterface CreateOption {\r\n  label: string;\r\n  value: 'sub-unit' | 'org-unit';\r\n  disabled: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-organizational',\r\n  templateUrl: './organizational.component.html',\r\n  styleUrl: './organizational.component.scss',\r\n})\r\nexport class OrganizationalComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  @ViewChild('dt1') dt1!: Table;\r\n  public breadcrumbitems: MenuItem[] | any;\r\n  public home: MenuItem | any;\r\n  public organization: any[] = [];\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n  public selectedOrganizations: any[] = [];\r\n  public createOptions: CreateOption[] = [];\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private organizationalservice: OrganizationalService\r\n  ) {}\r\n\r\n  private _selectedOrgColumns: OrgColumn[] = [];\r\n\r\n  public OrgCols: OrgColumn[] = [\r\n    {\r\n      field: 'crm_org_unit_managers.business_partner.bp_full_name',\r\n      header: 'Manager',\r\n    },\r\n    { field: 'parent_organisational_unit.name', header: 'Parent Unit Name' },\r\n    { field: 'organisational_unit_id', header: 'ID' },\r\n    { field: 'parent_organisational_unit_id', header: 'Parent Unit ID' },\r\n    { field: 'sales_organisation_indicator', header: 'Sales Organization' },\r\n    { field: 'sales_indicator', header: 'Sales' },\r\n    { field: 'reporting_line_indicator', header: 'Reporting Line' },\r\n  ];\r\n\r\n  sortFieldOrg: string = '';\r\n  sortOrderOrg: number = 1;\r\n\r\n  ngOnInit() {\r\n    this.breadcrumbitems = [\r\n      { label: 'Organization', routerLink: ['/store/organization'] },\r\n    ];\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n    this._selectedOrgColumns = this.OrgCols;\r\n    this.updateCreateOptions();\r\n  }\r\n\r\n  get selectedOrgColumns(): any[] {\r\n    return this._selectedOrgColumns;\r\n  }\r\n\r\n  set selectedOrgColumns(val: any[]) {\r\n    this._selectedOrgColumns = this.OrgCols.filter((col) => val.includes(col));\r\n  }\r\n\r\n  onOrgColumnReorder(event: any) {\r\n    const draggedCol = this.OrgCols[event.dragIndex];\r\n    this.OrgCols.splice(event.dragIndex, 1);\r\n    this.OrgCols.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  customSort(field: string, data: any[], type: 'ORG') {\r\n    if (type === 'ORG') {\r\n      if (this.sortFieldOrg === field) {\r\n        // Toggle sort order if same column is clicked\r\n        this.sortOrderOrg = this.sortOrderOrg === 1 ? -1 : 1;\r\n      } else {\r\n        // Reset to ascending when changing columns\r\n        this.sortFieldOrg = field;\r\n        this.sortOrderOrg = 1;\r\n      }\r\n    }\r\n\r\n    data.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\r\n\r\n      return this.sortOrderOrg * result;\r\n    });\r\n  }\r\n\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      let fields = field.split('.');\r\n      let value = data;\r\n      for (let i = 0; i < fields.length; i++) {\r\n        if (value == null) return null;\r\n        value = value[fields[i]];\r\n      }\r\n      return value;\r\n    }\r\n  }\r\n\r\n  updateCreateOptions(): void {\r\n    this.createOptions = [\r\n      { label: 'Org Unit', value: 'org-unit', disabled: false },\r\n      {\r\n        label: 'SubOrg Unit',\r\n        value: 'sub-unit',\r\n        disabled: !(this.selectedOrganizations?.length > 0),\r\n      },\r\n    ];\r\n  }\r\n\r\n  loadOrganization(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n\r\n    this.organizationalservice\r\n      .getOrganization(\r\n        page,\r\n        pageSize,\r\n        sortField,\r\n        sortOrder,\r\n        this.globalSearchTerm\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.organization = response?.data || [];\r\n          this.totalRecords = response?.meta?.pagination.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching organization', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  create(event: DropdownChangeEvent): void {\r\n    const selectedType = event?.value?.value;\r\n\r\n    if (selectedType === 'sub-unit' && this.selectedOrganizations?.length) {\r\n      const selected = this.selectedOrganizations[0];\r\n      const parentUnitId = selected?.organisational_unit_id;\r\n\r\n      this.router.navigate(['/store/organization/createsub'], {\r\n        state: { parentUnitId },\r\n      });\r\n    } else {\r\n      this.router.navigate(['/store/organization/create']);\r\n    }\r\n  }\r\n\r\n  toggle(organization: any): void {\r\n    organization.expanded = !organization.expanded;\r\n    if (!organization.expanded) {\r\n      return;\r\n    }\r\n\r\n    const childIds: string[] =\r\n      organization?.child_organisational_units\r\n        ?.map((c: any) => c?.organisational_unit_id)\r\n        ?.filter((id: string | undefined): id is string => !!id) || [];\r\n\r\n    if (childIds.length === 0) {\r\n      organization.expanded = false;\r\n      return;\r\n    }\r\n\r\n    forkJoin(\r\n      childIds.map((id) =>\r\n        this.organizationalservice.getOrganizationByChildID(id)\r\n      )\r\n    )\r\n      .pipe(map((resArray) => resArray.flatMap((res) => res ?? [])))\r\n      .subscribe({\r\n        next: (units: any[]) => {\r\n          organization.details = units;\r\n        },\r\n        error: (err) => {\r\n          console.error('Error loading expanded data', err);\r\n          organization.expanded = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  trackByUnit = (_: number, u: any) => u?.organisational_unit_id ?? _;\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadOrganization({ first: 0, rows: 10 });\r\n  }\r\n\r\n  signup() {\r\n    this.router.navigate(['/store/organization/create']);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div\r\n        class=\"filter-sec my-4 flex align-items-start justify-content-between flex-column gap-2 md:flex-row md:align-items-center\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-start gap-3 md:flex-row md:align-items-center flex-wrap w-full md:w-auto\">\r\n            <!-- Search Box -->\r\n            <div class=\"h-search-box w-full sm:w-24rem\">\r\n                <span class=\"p-input-icon-right w-full md:w-24rem\">\r\n                    <input type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\" (input)=\"onGlobalFilter(dt1, $event)\"\r\n                        placeholder=\"Search Organization\"\r\n                        class=\"p-inputtext p-component p-element w-full sm:w-24rem h-3rem px-3 border-round-3xl border-1 surface-border\">\r\n                    <i class=\"pi pi-search\" style=\"right: 16px;\"></i>\r\n                </span>\r\n            </div>\r\n\r\n            <!-- <button type=\"button\" (click)=\"create()\"\r\n                class=\"h-3rem p-element p-ripple p-button p-component border-round-3xl w-8rem justify-content-center gap-2 font-semibold border-none\">\r\n                <span class=\"material-symbols-rounded text-2xl\">box_edit</span> Create\r\n            </button> -->\r\n            <p-dropdown [options]=\"createOptions\" placeholder=\"Create\" optionLabel=\"label\" (onChange)=\"create($event)\"\r\n                class=\"w-full sm:w-13rem\"\r\n                [styleClass]=\"'w-full sm:w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" />\r\n\r\n            <p-multiSelect [options]=\"OrgCols\" [(ngModel)]=\"selectedOrgColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table #dt1 [value]=\"organization\" dataKey=\"id\" [rows]=\"14\" (onLazyLoad)=\"loadOrganization($event)\"\r\n            styleClass=\"w-full\" [paginator]=\"true\" [loading]=\"loading\" [paginator]=\"true\" [lazy]=\"true\"\r\n            [totalRecords]=\"totalRecords\" [scrollable]=\"true\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onOrgColumnReorder($event)\" [(selection)]=\"selectedOrganizations\"\r\n            (selectionChange)=\"updateCreateOptions()\" responsiveLayout=\"scroll\" class=\"scrollable-table\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center table-checkbox\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pFrozenColumn (click)=\"customSort('name', organization, 'ORG')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Name\r\n                            <i *ngIf=\"sortFieldOrg === 'name'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrderOrg === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                            <i *ngIf=\"sortFieldOrg !== 'name'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of selectedOrgColumns\">\r\n                        <th *ngIf=\"col.field !== 'sales_organisation_indicator' && col.field !== 'sales_indicator' && col.field !== 'reporting_line_indicator'; else nonSortableColumn\"\r\n                            [pSortableColumn]=\"col.field\" pReorderableColumn\r\n                            (click)=\"customSort(col.field,organization, 'ORG')\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortFieldOrg === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrderOrg === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\"></i>\r\n                                <i *ngIf=\"sortFieldOrg !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n\r\n                        <ng-template #nonSortableColumn>\r\n                            <th pReorderableColumn>\r\n                                <div class=\"flex align-items-center\">\r\n                                    {{ col.header }}\r\n                                </div>\r\n                            </th>\r\n                        </ng-template>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-organization>\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"organization\" />\r\n                    </td>\r\n\r\n                    <td pFrozenColumn class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            <button *ngIf=\"organization?.child_organisational_units?.length > 0\" pButton type=\"button\"\r\n                                class=\"p-button p-button-text p-button-rounded p-button-plain p-button-icon-only p-button-sm\"\r\n                                (click)=\"toggle(organization)\">\r\n                                <span class=\"pi\" [ngClass]=\"{\r\n      'pi-chevron-down': organization.expanded,\r\n      'pi-chevron-right': !organization.expanded\r\n    }\" style=\"font-size: 0.75rem;\"></span>\r\n                            </button>\r\n\r\n\r\n                            <a [routerLink]=\"'/store/organization/' + organization.organisational_unit_id\"\r\n                                class=\"text-orange-600 font-medium underline\">\r\n                                {{ organization.name || '-' }}\r\n                            </a>\r\n                        </div>\r\n                    </td>\r\n\r\n\r\n                    <ng-container *ngFor=\"let col of selectedOrgColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'crm_org_unit_managers.business_partner.bp_full_name'\">\r\n                                    {{ organization?.crm_org_unit_managers?.[0]?.business_partner?.bp_full_name || '-'\r\n                                    }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'parent_organisational_unit.name'\">\r\n                                    {{ organization?.parent_organisational_unit?.name || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'organisational_unit_id'\">\r\n                                    {{ organization?.organisational_unit_id || '-'}}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'parent_organisational_unit_id'\">\r\n                                    {{ organization?.parent_organisational_unit_id || '-'}}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'sales_organisation_indicator'\">\r\n                                    <p-checkbox [binary]=\"true\" [disabled]=\"true\"\r\n                                        [ngModel]=\"organization?.crm_org_unit_functions?.[0]?.sales_organisation_indicator\"></p-checkbox>\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'sales_indicator'\">\r\n                                    <p-checkbox [binary]=\"true\" [disabled]=\"true\"\r\n                                        [ngModel]=\"organization?.crm_org_unit_functions?.[0]?.sales_indicator\"></p-checkbox>\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'reporting_line_indicator'\">\r\n                                    <p-checkbox [binary]=\"true\" [disabled]=\"true\"\r\n                                        [ngModel]=\"organization?.crm_org_unit_functions?.[0]?.reporting_line_indicator\"></p-checkbox>\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                </tr>\r\n                <!-- ───── reusable row template ───── -->\r\n                <ng-template #rowTpl let-node let-indent=\"indent\">\r\n                    <tr class=\"bg-surface-50 text-sm org-child-row\">\r\n\r\n                        <!-- checkbox -->\r\n                        <td pFrozenColumn class=\"pl-3 w-2rem text-center\">\r\n                            <p-tableCheckbox [value]=\"node\"></p-tableCheckbox>\r\n                        </td>\r\n\r\n                        <!-- name + expander -->\r\n                        <td pFrozenColumn class=\"border-round-left-lg\">\r\n                            <div class=\"flex align-items-center gap-2 ml-5\" [style.margin-left.px]=\"indent\">\r\n                                <button *ngIf=\"node.child_organisational_units?.length\" pButton type=\"button\"\r\n                                    class=\"p-button p-button-text p-button-rounded p-button-plain p-button-icon-only p-button-sm\"\r\n                                    (click)=\"toggle(node)\">\r\n                                    <span class=\"pi\" [ngClass]=\"{\r\n                  'pi-chevron-down': node.expanded,\r\n                  'pi-chevron-right': !node.expanded\r\n                }\" style=\"font-size:0.75rem;\"></span>\r\n                                </button>\r\n\r\n                                <a [routerLink]=\"'/store/organization/' + node.organisational_unit_id\"\r\n                                    class=\"text-orange-600 font-medium underline\">\r\n                                    {{ node.name || '-' }}\r\n                                </a>\r\n                            </div>\r\n                        </td>\r\n\r\n                        <!-- dynamic columns (unchanged, but now use 'node') -->\r\n                        <ng-container *ngFor=\"let col of selectedOrgColumns\">\r\n                            <td>\r\n                                <ng-container [ngSwitch]=\"col.field\">\r\n\r\n                                    <ng-container *ngSwitchCase=\"'crm_org_unit_managers.business_partner.bp_full_name'\">\r\n                                        {{ node.crm_org_unit_managers?.[0]?.business_partner?.bp_full_name || '-' }}\r\n                                    </ng-container>\r\n\r\n                                    <ng-container *ngSwitchCase=\"'parent_organisational_unit.name'\">\r\n                                        {{ node.parent_organisational_unit?.name || '-' }}\r\n                                    </ng-container>\r\n\r\n                                    <ng-container *ngSwitchCase=\"'organisational_unit_id'\">\r\n                                        {{ node.organisational_unit_id || '-' }}\r\n                                    </ng-container>\r\n\r\n                                    <ng-container *ngSwitchCase=\"'parent_organisational_unit_id'\">\r\n                                        {{ node.parent_organisational_unit_id || '-' }}\r\n                                    </ng-container>\r\n\r\n                                    <ng-container *ngSwitchCase=\"'sales_organisation_indicator'\">\r\n                                        <p-checkbox [binary]=\"true\" [disabled]=\"true\"\r\n                                            [ngModel]=\"node.crm_org_unit_functions?.[0]?.sales_organisation_indicator\">\r\n                                        </p-checkbox>\r\n                                    </ng-container>\r\n\r\n                                    <ng-container *ngSwitchCase=\"'sales_indicator'\">\r\n                                        <p-checkbox [binary]=\"true\" [disabled]=\"true\"\r\n                                            [ngModel]=\"node.crm_org_unit_functions?.[0]?.sales_indicator\">\r\n                                        </p-checkbox>\r\n                                    </ng-container>\r\n\r\n                                    <ng-container *ngSwitchCase=\"'reporting_line_indicator'\">\r\n                                        <p-checkbox [binary]=\"true\" [disabled]=\"true\"\r\n                                            [ngModel]=\"node.crm_org_unit_functions?.[0]?.reporting_line_indicator\">\r\n                                        </p-checkbox>\r\n                                    </ng-container>\r\n\r\n                                    <ng-container *ngSwitchDefault>\r\n                                        {{ node[col.field] || '-' }}\r\n                                    </ng-container>\r\n\r\n                                </ng-container>\r\n                            </td>\r\n                        </ng-container>\r\n                    </tr>\r\n\r\n                    <!-- ───── recurse to children, grandchildren, … ───── -->\r\n                    <ng-container *ngIf=\"node.expanded\">\r\n                        <ng-container *ngFor=\"let wrap of node.details\">\r\n                            <ng-container *ngFor=\"let child of wrap.data\">\r\n                                <ng-container\r\n                                    *ngTemplateOutlet=\"rowTpl; context:{ $implicit: child, indent: indent + 16 }\"></ng-container>\r\n                            </ng-container>\r\n                        </ng-container>\r\n                    </ng-container>\r\n                </ng-template>\r\n\r\n                <!-- ───── render first‑level children of current \"organization\" ───── -->\r\n                <ng-container *ngIf=\"organization.expanded\">\r\n                    <ng-container *ngFor=\"let wrap of organization.details\">\r\n                        <ng-container *ngFor=\"let child of wrap.data\">\r\n                            <ng-container\r\n                                *ngTemplateOutlet=\"rowTpl; context:{ $implicit: child, indent: 12 }\"></ng-container>\r\n                        </ng-container>\r\n                    </ng-container>\r\n                </ng-container>\r\n\r\n\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"10\" class=\"border-round-left-lg\">No organization found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"10\" class=\"border-round-left-lg\">Loading organization data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,OAAO,EAAEC,QAAQ,QAAQ,MAAM;AACxC,SAASC,GAAG,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC4CRC,EAAA,CAAAC,SAAA,YACyF;;;;IAArFD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,YAAA,yDAAgF;;;;;IACpFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;IAS3DD,EAAA,CAAAC,SAAA,YACyF;;;;IAArFD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,YAAA,yDAAgF;;;;;IACpFJ,EAAA,CAAAC,SAAA,YAAkE;;;;;;IAP1ED,EAAA,CAAAK,cAAA,aAEwD;IAApDL,EAAA,CAAAM,UAAA,mBAAAC,wFAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAa,WAAA,CAASV,MAAA,CAAAW,UAAA,CAAAJ,MAAA,CAAAK,KAAA,EAAAZ,MAAA,CAAAa,YAAA,EAAmC,KAAK,CAAC;IAAA,EAAC;IACnDhB,EAAA,CAAAK,cAAA,cAAoD;IAChDL,EAAA,CAAAiB,MAAA,GACA;IAEAjB,EAFA,CAAAkB,UAAA,IAAAC,uEAAA,gBACqF,IAAAC,uEAAA,gBACvB;IAEtEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;IARDrB,EAAA,CAAAE,UAAA,oBAAAQ,MAAA,CAAAK,KAAA,CAA6B;IAGzBf,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAb,MAAA,CAAAc,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAAgC;IAAhCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,YAAA,KAAAf,MAAA,CAAAK,KAAA,CAAgC;IAEhCf,EAAA,CAAAsB,SAAA,EAAgC;IAAhCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,YAAA,KAAAf,MAAA,CAAAK,KAAA,CAAgC;;;;;IAMpCf,EADJ,CAAAK,cAAA,aAAuB,cACkB;IACjCL,EAAA,CAAAiB,MAAA,GACJ;IACJjB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;IAFGrB,EAAA,CAAAsB,SAAA,GACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAb,MAAA,CAAAc,MAAA,MACJ;;;;;IAhBZxB,EAAA,CAAA0B,uBAAA,GAAqD;IAYjD1B,EAXA,CAAAkB,UAAA,IAAAS,mEAAA,iBAEwD,IAAAC,4EAAA,gCAAA5B,EAAA,CAAA6B,sBAAA,CASxB;;;;;;IAX3B7B,EAAA,CAAAsB,SAAA,EAAmI;IAAAtB,EAAnI,CAAAE,UAAA,SAAAQ,MAAA,CAAAK,KAAA,uCAAAL,MAAA,CAAAK,KAAA,0BAAAL,MAAA,CAAAK,KAAA,gCAAmI,aAAAe,oBAAA,CAAsB;;;;;;IAZlK9B,EADJ,CAAAK,cAAA,SAAI,aACsF;IAClFL,EAAA,CAAAC,SAAA,4BAAyB;IAC7BD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAK,cAAA,aAAoE;IAAlDL,EAAA,CAAAM,UAAA,mBAAAyB,oEAAA;MAAA/B,EAAA,CAAAQ,aAAA,CAAAwB,GAAA;MAAA,MAAA7B,MAAA,GAAAH,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAa,WAAA,CAASV,MAAA,CAAAW,UAAA,CAAW,MAAM,EAAAX,MAAA,CAAAa,YAAA,EAAgB,KAAK,CAAC;IAAA,EAAC;IAC/DhB,EAAA,CAAAK,cAAA,cAAoD;IAChDL,EAAA,CAAAiB,MAAA,aACA;IAEAjB,EAFA,CAAAkB,UAAA,IAAAe,mDAAA,gBACqF,IAAAC,mDAAA,gBAC1B;IAEnElC,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAiB,8DAAA,2BAAqD;IAoBzDnC,EAAA,CAAAqB,YAAA,EAAK;;;;IAzBWrB,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,YAAA,YAA6B;IAE7BzB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,YAAA,YAA6B;IAGXzB,EAAA,CAAAsB,SAAA,EAAqB;IAArBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAiC,kBAAA,CAAqB;;;;;;IA+B3CpC,EAAA,CAAAK,cAAA,iBAEmC;IAA/BL,EAAA,CAAAM,UAAA,mBAAA+B,iFAAA;MAAArC,EAAA,CAAAQ,aAAA,CAAA8B,GAAA;MAAA,MAAAC,eAAA,GAAAvC,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAa,WAAA,CAASV,MAAA,CAAAqC,MAAA,CAAAD,eAAA,CAAoB;IAAA,EAAC;IAC9BvC,EAAA,CAAAC,SAAA,eAGU;IACdD,EAAA,CAAAqB,YAAA,EAAS;;;;IAJYrB,EAAA,CAAAsB,SAAA,EAG3C;IAH2CtB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAyC,eAAA,IAAAC,GAAA,EAAAH,eAAA,CAAAI,QAAA,GAAAJ,eAAA,CAAAI,QAAA,EAG3C;;;;;IAe0B3C,EAAA,CAAA0B,uBAAA,GAAoF;IAChF1B,EAAA,CAAAiB,MAAA,GAEJ;;;;;IAFIjB,EAAA,CAAAsB,SAAA,EAEJ;IAFItB,EAAA,CAAAuB,kBAAA,OAAAgB,eAAA,kBAAAA,eAAA,CAAAK,qBAAA,kBAAAL,eAAA,CAAAK,qBAAA,qBAAAL,eAAA,CAAAK,qBAAA,IAAAC,gBAAA,kBAAAN,eAAA,CAAAK,qBAAA,IAAAC,gBAAA,CAAAC,YAAA,cAEJ;;;;;IAEA9C,EAAA,CAAA0B,uBAAA,GAAgE;IAC5D1B,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAgB,eAAA,kBAAAA,eAAA,CAAAQ,0BAAA,kBAAAR,eAAA,CAAAQ,0BAAA,CAAAC,IAAA,cACJ;;;;;IAEAhD,EAAA,CAAA0B,uBAAA,GAAuD;IACnD1B,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAgB,eAAA,kBAAAA,eAAA,CAAAU,sBAAA,cACJ;;;;;IAEAjD,EAAA,CAAA0B,uBAAA,GAA8D;IAC1D1B,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAgB,eAAA,kBAAAA,eAAA,CAAAW,6BAAA,cACJ;;;;;IAEAlD,EAAA,CAAA0B,uBAAA,GAA6D;IACzD1B,EAAA,CAAAC,SAAA,qBACqG;;;;;IADzFD,EAAA,CAAAsB,SAAA,EAAe;IACvBtB,EADQ,CAAAE,UAAA,gBAAe,kBAAkB,YAAAqC,eAAA,kBAAAA,eAAA,CAAAY,sBAAA,kBAAAZ,eAAA,CAAAY,sBAAA,qBAAAZ,eAAA,CAAAY,sBAAA,IAAAC,4BAAA,CAC0C;;;;;IAE3FpD,EAAA,CAAA0B,uBAAA,GAAgD;IAC5C1B,EAAA,CAAAC,SAAA,qBACwF;;;;;IAD5ED,EAAA,CAAAsB,SAAA,EAAe;IACvBtB,EADQ,CAAAE,UAAA,gBAAe,kBAAkB,YAAAqC,eAAA,kBAAAA,eAAA,CAAAY,sBAAA,kBAAAZ,eAAA,CAAAY,sBAAA,qBAAAZ,eAAA,CAAAY,sBAAA,IAAAE,eAAA,CAC6B;;;;;IAG9ErD,EAAA,CAAA0B,uBAAA,GAAyD;IACrD1B,EAAA,CAAAC,SAAA,qBACiG;;;;;IADrFD,EAAA,CAAAsB,SAAA,EAAe;IACvBtB,EADQ,CAAAE,UAAA,gBAAe,kBAAkB,YAAAqC,eAAA,kBAAAA,eAAA,CAAAY,sBAAA,kBAAAZ,eAAA,CAAAY,sBAAA,qBAAAZ,eAAA,CAAAY,sBAAA,IAAAG,wBAAA,CACsC;;;;;IA/BnGtD,EAAA,CAAA0B,uBAAA,GAAqD;IACjD1B,EAAA,CAAAK,cAAA,SAAI;IACAL,EAAA,CAAA0B,uBAAA,OAAqC;IA2BjC1B,EA1BA,CAAAkB,UAAA,IAAAqC,6EAAA,2BAAoF,IAAAC,6EAAA,2BAKpB,IAAAC,6EAAA,2BAIT,IAAAC,6EAAA,2BAIO,IAAAC,6EAAA,2BAID,IAAAC,6EAAA,2BAIb,IAAAC,6EAAA,2BAKS;;IAMjE7D,EAAA,CAAAqB,YAAA,EAAK;;;;;IAjCarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAA4D,OAAA,CAAA/C,KAAA,CAAsB;IACjBf,EAAA,CAAAsB,SAAA,EAAmE;IAAnEtB,EAAA,CAAAE,UAAA,uEAAmE;IAKnEF,EAAA,CAAAsB,SAAA,EAA+C;IAA/CtB,EAAA,CAAAE,UAAA,mDAA+C;IAI/CF,EAAA,CAAAsB,SAAA,EAAsC;IAAtCtB,EAAA,CAAAE,UAAA,0CAAsC;IAItCF,EAAA,CAAAsB,SAAA,EAA6C;IAA7CtB,EAAA,CAAAE,UAAA,iDAA6C;IAI7CF,EAAA,CAAAsB,SAAA,EAA4C;IAA5CtB,EAAA,CAAAE,UAAA,gDAA4C;IAI5CF,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAAE,UAAA,mCAA+B;IAK/BF,EAAA,CAAAsB,SAAA,EAAwC;IAAxCtB,EAAA,CAAAE,UAAA,4CAAwC;;;;;;IAqBvDF,EAAA,CAAAK,cAAA,iBAE2B;IAAvBL,EAAA,CAAAM,UAAA,mBAAAyD,+FAAA;MAAA/D,EAAA,CAAAQ,aAAA,CAAAwD,IAAA;MAAA,MAAAC,QAAA,GAAAjE,EAAA,CAAAW,aAAA,GAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAa,WAAA,CAASV,MAAA,CAAAqC,MAAA,CAAAyB,QAAA,CAAY;IAAA,EAAC;IACtBjE,EAAA,CAAAC,SAAA,eAGiB;IACrBD,EAAA,CAAAqB,YAAA,EAAS;;;;IAJYrB,EAAA,CAAAsB,SAAA,EAGnC;IAHmCtB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAyC,eAAA,IAAAC,GAAA,EAAAuB,QAAA,CAAAtB,QAAA,GAAAsB,QAAA,CAAAtB,QAAA,EAGnC;;;;;IAekB3C,EAAA,CAAA0B,uBAAA,GAAoF;IAChF1B,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAA0C,QAAA,CAAArB,qBAAA,kBAAAqB,QAAA,CAAArB,qBAAA,qBAAAqB,QAAA,CAAArB,qBAAA,IAAAC,gBAAA,kBAAAoB,QAAA,CAAArB,qBAAA,IAAAC,gBAAA,CAAAC,YAAA,cACJ;;;;;IAEA9C,EAAA,CAAA0B,uBAAA,GAAgE;IAC5D1B,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAA0C,QAAA,CAAAlB,0BAAA,kBAAAkB,QAAA,CAAAlB,0BAAA,CAAAC,IAAA,cACJ;;;;;IAEAhD,EAAA,CAAA0B,uBAAA,GAAuD;IACnD1B,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAA0C,QAAA,CAAAhB,sBAAA,aACJ;;;;;IAEAjD,EAAA,CAAA0B,uBAAA,GAA8D;IAC1D1B,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAA0C,QAAA,CAAAf,6BAAA,aACJ;;;;;IAEAlD,EAAA,CAAA0B,uBAAA,GAA6D;IACzD1B,EAAA,CAAAC,SAAA,qBAEa;;;;;IAFDD,EAAA,CAAAsB,SAAA,EAAe;IACvBtB,EADQ,CAAAE,UAAA,gBAAe,kBAAkB,YAAA+D,QAAA,CAAAd,sBAAA,kBAAAc,QAAA,CAAAd,sBAAA,qBAAAc,QAAA,CAAAd,sBAAA,IAAAC,4BAAA,CACiC;;;;;IAIlFpD,EAAA,CAAA0B,uBAAA,GAAgD;IAC5C1B,EAAA,CAAAC,SAAA,qBAEa;;;;;IAFDD,EAAA,CAAAsB,SAAA,EAAe;IACvBtB,EADQ,CAAAE,UAAA,gBAAe,kBAAkB,YAAA+D,QAAA,CAAAd,sBAAA,kBAAAc,QAAA,CAAAd,sBAAA,qBAAAc,QAAA,CAAAd,sBAAA,IAAAE,eAAA,CACoB;;;;;IAIrErD,EAAA,CAAA0B,uBAAA,GAAyD;IACrD1B,EAAA,CAAAC,SAAA,qBAEa;;;;;IAFDD,EAAA,CAAAsB,SAAA,EAAe;IACvBtB,EADQ,CAAAE,UAAA,gBAAe,kBAAkB,YAAA+D,QAAA,CAAAd,sBAAA,kBAAAc,QAAA,CAAAd,sBAAA,qBAAAc,QAAA,CAAAd,sBAAA,IAAAG,wBAAA,CAC6B;;;;;IAI9EtD,EAAA,CAAA0B,uBAAA,GAA+B;IAC3B1B,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAA0C,QAAA,CAAAC,OAAA,CAAAnD,KAAA,cACJ;;;;;IAxCZf,EAAA,CAAA0B,uBAAA,GAAqD;IACjD1B,EAAA,CAAAK,cAAA,SAAI;IACAL,EAAA,CAAA0B,uBAAA,OAAqC;IAoCjC1B,EAlCA,CAAAkB,UAAA,IAAAiD,2FAAA,2BAAoF,IAAAC,2FAAA,2BAIpB,IAAAC,2FAAA,2BAIT,IAAAC,2FAAA,2BAIO,IAAAC,2FAAA,2BAID,IAAAC,2FAAA,2BAMb,IAAAC,2FAAA,2BAMS,KAAAC,4FAAA,2BAM1B;;IAKvC1E,EAAA,CAAAqB,YAAA,EAAK;;;;;IAzCarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAAgE,OAAA,CAAAnD,KAAA,CAAsB;IAEjBf,EAAA,CAAAsB,SAAA,EAAmE;IAAnEtB,EAAA,CAAAE,UAAA,uEAAmE;IAInEF,EAAA,CAAAsB,SAAA,EAA+C;IAA/CtB,EAAA,CAAAE,UAAA,mDAA+C;IAI/CF,EAAA,CAAAsB,SAAA,EAAsC;IAAtCtB,EAAA,CAAAE,UAAA,0CAAsC;IAItCF,EAAA,CAAAsB,SAAA,EAA6C;IAA7CtB,EAAA,CAAAE,UAAA,iDAA6C;IAI7CF,EAAA,CAAAsB,SAAA,EAA4C;IAA5CtB,EAAA,CAAAE,UAAA,gDAA4C;IAM5CF,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAAE,UAAA,mCAA+B;IAM/BF,EAAA,CAAAsB,SAAA,EAAwC;IAAxCtB,EAAA,CAAAE,UAAA,4CAAwC;;;;;IAmB3DF,EAAA,CAAA2E,kBAAA,GACiG;;;;;IAFrG3E,EAAA,CAAA0B,uBAAA,GAA8C;IAC1C1B,EAAA,CAAAkB,UAAA,IAAA0D,yHAAA,2BACkF;;;;;;;;IAA7E5E,EAAA,CAAAsB,SAAA,EAA0B;IAAAtB,EAA1B,CAAAE,UAAA,qBAAA2E,UAAA,CAA0B,4BAAA7E,EAAA,CAAAyC,eAAA,IAAAqC,GAAA,EAAAC,SAAA,EAAAC,UAAA,OAAiD;;;;;IAHxFhF,EAAA,CAAA0B,uBAAA,GAAgD;IAC5C1B,EAAA,CAAAkB,UAAA,IAAA+D,0GAAA,2BAA8C;;;;;IAAdjF,EAAA,CAAAsB,SAAA,EAAY;IAAZtB,EAAA,CAAAE,UAAA,YAAAgF,QAAA,CAAAC,IAAA,CAAY;;;;;IAFpDnF,EAAA,CAAA0B,uBAAA,GAAoC;IAChC1B,EAAA,CAAAkB,UAAA,IAAAkE,2FAAA,2BAAgD;;;;;IAAjBpF,EAAA,CAAAsB,SAAA,EAAe;IAAftB,EAAA,CAAAE,UAAA,YAAA+D,QAAA,CAAAoB,OAAA,CAAe;;;;;IAzE9CrF,EAHJ,CAAAK,cAAA,aAAgD,aAGM;IAC9CL,EAAA,CAAAC,SAAA,0BAAkD;IACtDD,EAAA,CAAAqB,YAAA,EAAK;IAIDrB,EADJ,CAAAK,cAAA,aAA+C,cACqC;IAC5EL,EAAA,CAAAkB,UAAA,IAAAoE,sEAAA,qBAE2B;IAO3BtF,EAAA,CAAAK,cAAA,YACkD;IAC9CL,EAAA,CAAAiB,MAAA,GACJ;IAERjB,EAFQ,CAAAqB,YAAA,EAAI,EACF,EACL;IAGLrB,EAAA,CAAAkB,UAAA,IAAAqE,4EAAA,4BAAqD;IA6CzDvF,EAAA,CAAAqB,YAAA,EAAK;IAGLrB,EAAA,CAAAkB,UAAA,IAAAsE,4EAAA,2BAAoC;;;;;;IAvEXxF,EAAA,CAAAsB,SAAA,GAAc;IAAdtB,EAAA,CAAAE,UAAA,UAAA+D,QAAA,CAAc;IAKiBjE,EAAA,CAAAsB,SAAA,GAA+B;IAA/BtB,EAAA,CAAAyF,WAAA,gBAAAT,UAAA,OAA+B;IAClEhF,EAAA,CAAAsB,SAAA,EAA6C;IAA7CtB,EAAA,CAAAE,UAAA,SAAA+D,QAAA,CAAAyB,0BAAA,kBAAAzB,QAAA,CAAAyB,0BAAA,CAAAC,MAAA,CAA6C;IASnD3F,EAAA,CAAAsB,SAAA,EAAmE;IAAnEtB,EAAA,CAAAE,UAAA,wCAAA+D,QAAA,CAAAhB,sBAAA,CAAmE;IAElEjD,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAA0C,QAAA,CAAAjB,IAAA,aACJ;IAKsBhD,EAAA,CAAAsB,SAAA,EAAqB;IAArBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAiC,kBAAA,CAAqB;IAgDxCpC,EAAA,CAAAsB,SAAA,EAAmB;IAAnBtB,EAAA,CAAAE,UAAA,SAAA+D,QAAA,CAAAtB,QAAA,CAAmB;;;;;IAc1B3C,EAAA,CAAA2E,kBAAA,GACwF;;;;;IAF5F3E,EAAA,CAAA0B,uBAAA,GAA8C;IAC1C1B,EAAA,CAAAkB,UAAA,IAAA0E,4GAAA,2BACyE;;;;;;;IAApE5F,EAAA,CAAAsB,SAAA,EAA0B;IAAAtB,EAA1B,CAAAE,UAAA,qBAAA2E,UAAA,CAA0B,4BAAA7E,EAAA,CAAA6F,eAAA,IAAAC,GAAA,EAAAC,SAAA,EAAwC;;;;;IAH/E/F,EAAA,CAAA0B,uBAAA,GAAwD;IACpD1B,EAAA,CAAAkB,UAAA,IAAA8E,6FAAA,2BAA8C;;;;;IAAdhG,EAAA,CAAAsB,SAAA,EAAY;IAAZtB,EAAA,CAAAE,UAAA,YAAA+F,QAAA,CAAAd,IAAA,CAAY;;;;;IAFpDnF,EAAA,CAAA0B,uBAAA,GAA4C;IACxC1B,EAAA,CAAAkB,UAAA,IAAAgF,8EAAA,2BAAwD;;;;;IAAzBlG,EAAA,CAAAsB,SAAA,EAAuB;IAAvBtB,EAAA,CAAAE,UAAA,YAAAqC,eAAA,CAAA8C,OAAA,CAAuB;;;;;IAvJtDrF,EADJ,CAAAK,cAAA,aAA2B,aACgD;IACnEL,EAAA,CAAAC,SAAA,0BAA0C;IAC9CD,EAAA,CAAAqB,YAAA,EAAK;IAGDrB,EADJ,CAAAK,cAAA,aAA+C,cACA;IACvCL,EAAA,CAAAkB,UAAA,IAAAiF,wDAAA,qBAEmC;IAQnCnG,EAAA,CAAAK,cAAA,YACkD;IAC9CL,EAAA,CAAAiB,MAAA,GACJ;IAERjB,EAFQ,CAAAqB,YAAA,EAAI,EACF,EACL;IAGLrB,EAAA,CAAAkB,UAAA,IAAAkF,8DAAA,4BAAqD;IAqCzDpG,EAAA,CAAAqB,YAAA,EAAK;IAyFLrB,EAvFA,CAAAkB,UAAA,IAAAmF,6DAAA,iCAAArG,EAAA,CAAA6B,sBAAA,CAAkD,KAAAyE,+DAAA,2BAuFN;;;;;IArJnBtG,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,UAAAqC,eAAA,CAAsB;IAK1BvC,EAAA,CAAAsB,SAAA,GAA0D;IAA1DtB,EAAA,CAAAE,UAAA,UAAAqC,eAAA,kBAAAA,eAAA,CAAAmD,0BAAA,kBAAAnD,eAAA,CAAAmD,0BAAA,CAAAC,MAAA,MAA0D;IAUhE3F,EAAA,CAAAsB,SAAA,EAA2E;IAA3EtB,EAAA,CAAAE,UAAA,wCAAAqC,eAAA,CAAAU,sBAAA,CAA2E;IAE1EjD,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAgB,eAAA,CAAAS,IAAA,aACJ;IAKsBhD,EAAA,CAAAsB,SAAA,EAAqB;IAArBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAiC,kBAAA,CAAqB;IA8HxCpC,EAAA,CAAAsB,SAAA,GAA2B;IAA3BtB,EAAA,CAAAE,UAAA,SAAAqC,eAAA,CAAAI,QAAA,CAA2B;;;;;IActC3C,EADJ,CAAAK,cAAA,SAAI,aAC8C;IAAAL,EAAA,CAAAiB,MAAA,6BAAsB;IACxEjB,EADwE,CAAAqB,YAAA,EAAK,EACxE;;;;;IAIDrB,EADJ,CAAAK,cAAA,SAAI,aAC8C;IAAAL,EAAA,CAAAiB,MAAA,8CAAuC;IACzFjB,EADyF,CAAAqB,YAAA,EAAK,EACzF;;;AD9NrB,OAAM,MAAOkF,uBAAuB;EAYlCC,YACUC,MAAc,EACdC,qBAA4C;IAD5C,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,qBAAqB,GAArBA,qBAAqB;IAbvB,KAAAC,YAAY,GAAG,IAAI9G,OAAO,EAAQ;IAInC,KAAAmB,YAAY,GAAU,EAAE;IACxB,KAAA4F,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,qBAAqB,GAAU,EAAE;IACjC,KAAAC,aAAa,GAAmB,EAAE;IAOjC,KAAAC,mBAAmB,GAAgB,EAAE;IAEtC,KAAAC,OAAO,GAAgB,CAC5B;MACEnG,KAAK,EAAE,qDAAqD;MAC5DS,MAAM,EAAE;KACT,EACD;MAAET,KAAK,EAAE,iCAAiC;MAAES,MAAM,EAAE;IAAkB,CAAE,EACxE;MAAET,KAAK,EAAE,wBAAwB;MAAES,MAAM,EAAE;IAAI,CAAE,EACjD;MAAET,KAAK,EAAE,+BAA+B;MAAES,MAAM,EAAE;IAAgB,CAAE,EACpE;MAAET,KAAK,EAAE,8BAA8B;MAAES,MAAM,EAAE;IAAoB,CAAE,EACvE;MAAET,KAAK,EAAE,iBAAiB;MAAES,MAAM,EAAE;IAAO,CAAE,EAC7C;MAAET,KAAK,EAAE,0BAA0B;MAAES,MAAM,EAAE;IAAgB,CAAE,CAChE;IAED,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAArB,YAAY,GAAW,CAAC;IA6JxB,KAAA+G,WAAW,GAAG,CAACC,CAAS,EAAEC,CAAM,KAAKA,CAAC,EAAEpE,sBAAsB,IAAImE,CAAC;EA/KhE;EAoBHE,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE,cAAc;MAAEC,UAAU,EAAE,CAAC,qBAAqB;IAAC,CAAE,CAC/D;IACD,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAC7D,IAAI,CAACR,mBAAmB,GAAG,IAAI,CAACC,OAAO;IACvC,IAAI,CAACU,mBAAmB,EAAE;EAC5B;EAEA,IAAIxF,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAAC6E,mBAAmB;EACjC;EAEA,IAAI7E,kBAAkBA,CAACyF,GAAU;IAC/B,IAAI,CAACZ,mBAAmB,GAAG,IAAI,CAACC,OAAO,CAACY,MAAM,CAAEC,GAAG,IAAKF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EAC5E;EAEAE,kBAAkBA,CAACC,KAAU;IAC3B,MAAMC,UAAU,GAAG,IAAI,CAACjB,OAAO,CAACgB,KAAK,CAACE,SAAS,CAAC;IAChD,IAAI,CAAClB,OAAO,CAACmB,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IACvC,IAAI,CAAClB,OAAO,CAACmB,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EACrD;EAEArH,UAAUA,CAACC,KAAa,EAAEoE,IAAW,EAAEoD,IAAW;IAChD,IAAIA,IAAI,KAAK,KAAK,EAAE;MAClB,IAAI,IAAI,CAAC9G,YAAY,KAAKV,KAAK,EAAE;QAC/B;QACA,IAAI,CAACX,YAAY,GAAG,IAAI,CAACA,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACtD,CAAC,MAAM;QACL;QACA,IAAI,CAACqB,YAAY,GAAGV,KAAK;QACzB,IAAI,CAACX,YAAY,GAAG,CAAC;MACvB;IACF;IAEA+E,IAAI,CAACqD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACjB,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAE1H,KAAK,CAAC;MAC9C,MAAM8H,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAE3H,KAAK,CAAC;MAE9C,IAAI+H,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAGH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MAE5D,OAAO,IAAI,CAACzI,YAAY,GAAG0I,MAAM;IACnC,CAAC,CAAC;EACJ;EAEAF,gBAAgBA,CAACzD,IAAS,EAAEpE,KAAa;IACvC,IAAI,CAACoE,IAAI,IAAI,CAACpE,KAAK,EAAE,OAAO,IAAI;IAEhC,IAAIA,KAAK,CAACiI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAO7D,IAAI,CAACpE,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,IAAIkI,MAAM,GAAGlI,KAAK,CAACmI,KAAK,CAAC,GAAG,CAAC;MAC7B,IAAIC,KAAK,GAAGhE,IAAI;MAChB,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACtD,MAAM,EAAEyD,CAAC,EAAE,EAAE;QACtC,IAAID,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI;QAC9BA,KAAK,GAAGA,KAAK,CAACF,MAAM,CAACG,CAAC,CAAC,CAAC;MAC1B;MACA,OAAOD,KAAK;IACd;EACF;EAEAvB,mBAAmBA,CAAA;IACjB,IAAI,CAACZ,aAAa,GAAG,CACnB;MAAEQ,KAAK,EAAE,UAAU;MAAE2B,KAAK,EAAE,UAAU;MAAEE,QAAQ,EAAE;IAAK,CAAE,EACzD;MACE7B,KAAK,EAAE,aAAa;MACpB2B,KAAK,EAAE,UAAU;MACjBE,QAAQ,EAAE,EAAE,IAAI,CAACtC,qBAAqB,EAAEpB,MAAM,GAAG,CAAC;KACnD,CACF;EACH;EAEA2D,gBAAgBA,CAACpB,KAAU;IACzB,IAAI,CAACrB,OAAO,GAAG,IAAI;IACnB,MAAM0C,IAAI,GAAGrB,KAAK,CAACsB,KAAK,GAAGtB,KAAK,CAACuB,IAAI,GAAG,CAAC;IACzC,MAAMC,QAAQ,GAAGxB,KAAK,CAACuB,IAAI;IAC3B,MAAME,SAAS,GAAGzB,KAAK,CAACyB,SAAS;IACjC,MAAMC,SAAS,GAAG1B,KAAK,CAAC0B,SAAS;IAEjC,IAAI,CAAClD,qBAAqB,CACvBmD,eAAe,CACdN,IAAI,EACJG,QAAQ,EACRC,SAAS,EACTC,SAAS,EACT,IAAI,CAAC9C,gBAAgB,CACtB,CACAgD,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAChJ,YAAY,GAAGgJ,QAAQ,EAAE7E,IAAI,IAAI,EAAE;QACxC,IAAI,CAACyB,YAAY,GAAGoD,QAAQ,EAAEC,IAAI,EAAEC,UAAU,CAACC,KAAK;QACpD,IAAI,CAACtD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDuD,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACvD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEAyD,MAAMA,CAACpC,KAA0B;IAC/B,MAAMqC,YAAY,GAAGrC,KAAK,EAAEiB,KAAK,EAAEA,KAAK;IAExC,IAAIoB,YAAY,KAAK,UAAU,IAAI,IAAI,CAACxD,qBAAqB,EAAEpB,MAAM,EAAE;MACrE,MAAM6E,QAAQ,GAAG,IAAI,CAACzD,qBAAqB,CAAC,CAAC,CAAC;MAC9C,MAAM0D,YAAY,GAAGD,QAAQ,EAAEvH,sBAAsB;MAErD,IAAI,CAACwD,MAAM,CAACiE,QAAQ,CAAC,CAAC,+BAA+B,CAAC,EAAE;QACtDC,KAAK,EAAE;UAAEF;QAAY;OACtB,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAChE,MAAM,CAACiE,QAAQ,CAAC,CAAC,4BAA4B,CAAC,CAAC;IACtD;EACF;EAEAlI,MAAMA,CAACxB,YAAiB;IACtBA,YAAY,CAAC2B,QAAQ,GAAG,CAAC3B,YAAY,CAAC2B,QAAQ;IAC9C,IAAI,CAAC3B,YAAY,CAAC2B,QAAQ,EAAE;MAC1B;IACF;IAEA,MAAMiI,QAAQ,GACZ5J,YAAY,EAAE0E,0BAA0B,EACpC3F,GAAG,CAAE8K,CAAM,IAAKA,CAAC,EAAE5H,sBAAsB,CAAC,EAC1C6E,MAAM,CAAEgD,EAAsB,IAAmB,CAAC,CAACA,EAAE,CAAC,IAAI,EAAE;IAElE,IAAIF,QAAQ,CAACjF,MAAM,KAAK,CAAC,EAAE;MACzB3E,YAAY,CAAC2B,QAAQ,GAAG,KAAK;MAC7B;IACF;IAEA7C,QAAQ,CACN8K,QAAQ,CAAC7K,GAAG,CAAE+K,EAAE,IACd,IAAI,CAACpE,qBAAqB,CAACqE,wBAAwB,CAACD,EAAE,CAAC,CACxD,CACF,CACEE,IAAI,CAACjL,GAAG,CAAEkL,QAAQ,IAAKA,QAAQ,CAACC,OAAO,CAAEC,GAAG,IAAKA,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAC7DrB,SAAS,CAAC;MACTC,IAAI,EAAGqB,KAAY,IAAI;QACrBpK,YAAY,CAACqE,OAAO,GAAG+F,KAAK;MAC9B,CAAC;MACDhB,KAAK,EAAGiB,GAAG,IAAI;QACbhB,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEiB,GAAG,CAAC;QACjDrK,YAAY,CAAC2B,QAAQ,GAAG,KAAK;MAC/B;KACD,CAAC;EACN;EAIA2I,cAAcA,CAACC,KAAY,EAAErD,KAAY;IACvC,IAAI,CAACoB,gBAAgB,CAAC;MAAEE,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC/C;EAEA+B,MAAMA,CAAA;IACJ,IAAI,CAAC/E,MAAM,CAACiE,QAAQ,CAAC,CAAC,4BAA4B,CAAC,CAAC;EACtD;EAEAe,WAAWA,CAAA;IACT,IAAI,CAAC9E,YAAY,CAACoD,IAAI,EAAE;IACxB,IAAI,CAACpD,YAAY,CAAC+E,QAAQ,EAAE;EAC9B;;;uBA3MWnF,uBAAuB,EAAAvG,EAAA,CAAA2L,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA7L,EAAA,CAAA2L,iBAAA,CAAAG,EAAA,CAAAC,qBAAA;IAAA;EAAA;;;YAAvBxF,uBAAuB;MAAAyF,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCtB5BnM,EAHR,CAAAK,cAAA,aAA8D,aAEqE,aAC/F;UACxBL,EAAA,CAAAC,SAAA,sBAA+F;UACnGD,EAAA,CAAAqB,YAAA,EAAM;UAKMrB,EAJZ,CAAAK,cAAA,aAAuG,aAEvD,eACW,mBAGsE;UAF1FL,EAAA,CAAAqM,gBAAA,2BAAAC,gEAAAC,MAAA;YAAAvM,EAAA,CAAAQ,aAAA,CAAAgM,GAAA;YAAAxM,EAAA,CAAAyM,kBAAA,CAAAL,GAAA,CAAAtF,gBAAA,EAAAyF,MAAA,MAAAH,GAAA,CAAAtF,gBAAA,GAAAyF,MAAA;YAAA,OAAAvM,EAAA,CAAAa,WAAA,CAAA0L,MAAA;UAAA,EAA8B;UAACvM,EAAA,CAAAM,UAAA,mBAAAoM,wDAAAH,MAAA;YAAAvM,EAAA,CAAAQ,aAAA,CAAAgM,GAAA;YAAA,MAAAG,MAAA,GAAA3M,EAAA,CAAA4M,WAAA;YAAA,OAAA5M,EAAA,CAAAa,WAAA,CAASuL,GAAA,CAAAd,cAAA,CAAAqB,MAAA,EAAAJ,MAAA,CAA2B;UAAA,EAAC;UAA/FvM,EAAA,CAAAqB,YAAA,EAEqH;UACrHrB,EAAA,CAAAC,SAAA,YAAiD;UAEzDD,EADI,CAAAqB,YAAA,EAAO,EACL;UAMNrB,EAAA,CAAAK,cAAA,sBAEmH;UAFpCL,EAAA,CAAAM,UAAA,sBAAAuM,iEAAAN,MAAA;YAAAvM,EAAA,CAAAQ,aAAA,CAAAgM,GAAA;YAAA,OAAAxM,EAAA,CAAAa,WAAA,CAAYuL,GAAA,CAAA9B,MAAA,CAAAiC,MAAA,CAAc;UAAA,EAAC;UAA1GvM,EAAA,CAAAqB,YAAA,EAEmH;UAEnHrB,EAAA,CAAAK,cAAA,yBAE+I;UAF5GL,EAAA,CAAAqM,gBAAA,2BAAAS,yEAAAP,MAAA;YAAAvM,EAAA,CAAAQ,aAAA,CAAAgM,GAAA;YAAAxM,EAAA,CAAAyM,kBAAA,CAAAL,GAAA,CAAAhK,kBAAA,EAAAmK,MAAA,MAAAH,GAAA,CAAAhK,kBAAA,GAAAmK,MAAA;YAAA,OAAAvM,EAAA,CAAAa,WAAA,CAAA0L,MAAA;UAAA,EAAgC;UAK3EvM,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAGFrB,EADJ,CAAAK,cAAA,eAAuB,sBAK8E;UAD7FL,EAH0D,CAAAM,UAAA,wBAAAyM,gEAAAR,MAAA;YAAAvM,EAAA,CAAAQ,aAAA,CAAAgM,GAAA;YAAA,OAAAxM,EAAA,CAAAa,WAAA,CAAcuL,GAAA,CAAA9C,gBAAA,CAAAiD,MAAA,CAAwB;UAAA,EAAC,0BAAAS,kEAAAT,MAAA;YAAAvM,EAAA,CAAAQ,aAAA,CAAAgM,GAAA;YAAA,OAAAxM,EAAA,CAAAa,WAAA,CAGjFuL,GAAA,CAAAnE,kBAAA,CAAAsE,MAAA,CAA0B;UAAA,EAAC;UAACvM,EAAA,CAAAqM,gBAAA,6BAAAY,qEAAAV,MAAA;YAAAvM,EAAA,CAAAQ,aAAA,CAAAgM,GAAA;YAAAxM,EAAA,CAAAyM,kBAAA,CAAAL,GAAA,CAAArF,qBAAA,EAAAwF,MAAA,MAAAH,GAAA,CAAArF,qBAAA,GAAAwF,MAAA;YAAA,OAAAvM,EAAA,CAAAa,WAAA,CAAA0L,MAAA;UAAA,EAAqC;UACjFvM,EAAA,CAAAM,UAAA,6BAAA2M,qEAAA;YAAAjN,EAAA,CAAAQ,aAAA,CAAAgM,GAAA;YAAA,OAAAxM,EAAA,CAAAa,WAAA,CAAmBuL,GAAA,CAAAxE,mBAAA,EAAqB;UAAA,EAAC;UA+MzC5H,EA7MA,CAAAkB,UAAA,KAAAgM,+CAAA,0BAAgC,KAAAC,+CAAA,2BAoCe,KAAAC,+CAAA,0BAoKT,KAAAC,+CAAA,0BAKD;UAOjDrN,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;;;UAvPoBrB,EAAA,CAAAsB,SAAA,GAAyB;UAAetB,EAAxC,CAAAE,UAAA,UAAAkM,GAAA,CAAA7E,eAAA,CAAyB,SAAA6E,GAAA,CAAA1E,IAAA,CAAc,uCAAuC;UAMzD1H,EAAA,CAAAsB,SAAA,GAA8B;UAA9BtB,EAAA,CAAAsN,gBAAA,YAAAlB,GAAA,CAAAtF,gBAAA,CAA8B;UAWrD9G,EAAA,CAAAsB,SAAA,GAAyB;UAEjCtB,EAFQ,CAAAE,UAAA,YAAAkM,GAAA,CAAApF,aAAA,CAAyB,6GAE2E;UAEjGhH,EAAA,CAAAsB,SAAA,EAAmB;UAAnBtB,EAAA,CAAAE,UAAA,YAAAkM,GAAA,CAAAlF,OAAA,CAAmB;UAAClH,EAAA,CAAAsN,gBAAA,YAAAlB,GAAA,CAAAhK,kBAAA,CAAgC;UAE/DpC,EAAA,CAAAE,UAAA,2IAA0I;UAMpIF,EAAA,CAAAsB,SAAA,GAAsB;UAEkBtB,EAFxC,CAAAE,UAAA,UAAAkM,GAAA,CAAApL,YAAA,CAAsB,YAAyB,mBACnB,YAAAoL,GAAA,CAAAvF,OAAA,CAAoB,mBAAmB,cAAc,iBAAAuF,GAAA,CAAAxF,YAAA,CAC9D,oBAAoB,4BAA4B;UACjC5G,EAAA,CAAAsN,gBAAA,cAAAlB,GAAA,CAAArF,qBAAA,CAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}