{"ast": null, "code": "'use strict';\n\nvar callBind = require('call-bind-apply-helpers');\nvar gOPD = require('gopd');\n\n// eslint-disable-next-line no-extra-parens, no-proto\nvar hasProtoAccessor = /** @type {{ __proto__?: typeof Array.prototype }} */[].__proto__ === Array.prototype;\n\n// eslint-disable-next-line no-extra-parens\nvar desc = hasProtoAccessor && gOPD && gOPD(Object.prototype, /** @type {keyof typeof Object.prototype} */'__proto__');\nvar $Object = Object;\nvar $getPrototypeOf = $Object.getPrototypeOf;\n\n/** @type {import('./get')} */\nmodule.exports = desc && typeof desc.get === 'function' ? callBind([desc.get]) : typeof $getPrototypeOf === 'function' ? /** @type {import('./get')} */function getDunder(value) {\n  // eslint-disable-next-line eqeqeq\n  return $getPrototypeOf(value == null ? value : $Object(value));\n} : false;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}