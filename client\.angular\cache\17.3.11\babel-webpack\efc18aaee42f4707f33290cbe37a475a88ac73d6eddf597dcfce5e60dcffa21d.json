{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, signal, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ViewChildren, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils } from 'primeng/utils';\n\n/**\n * TabMenu is a navigation component that displays items as tab headers.\n * @group Components\n */\nconst _c0 = [\"content\"];\nconst _c1 = [\"navbar\"];\nconst _c2 = [\"inkbar\"];\nconst _c3 = [\"prevBtn\"];\nconst _c4 = [\"nextBtn\"];\nconst _c5 = [\"tabLink\"];\nconst _c6 = [\"tab\"];\nconst _c7 = a0 => ({\n  \"p-tabmenu p-component\": true,\n  \"p-tabmenu-scrollable\": a0\n});\nconst _c8 = (a0, a1, a2) => ({\n  \"p-tabmenuitem\": true,\n  \"p-disabled\": a0,\n  \"p-highlight\": a1,\n  \"p-hidden\": a2\n});\nconst _c9 = (a0, a1) => ({\n  $implicit: a0,\n  index: a1\n});\nconst _c10 = () => ({\n  exact: false\n});\nfunction TabMenu_button_2_ChevronLeftIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction TabMenu_button_2_3_ng_template_0_Template(rf, ctx) {}\nfunction TabMenu_button_2_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TabMenu_button_2_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TabMenu_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17, 3);\n    i0.ɵɵlistener(\"click\", function TabMenu_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navBackward());\n    });\n    i0.ɵɵtemplate(2, TabMenu_button_2_ChevronLeftIcon_2_Template, 1, 1, \"ChevronLeftIcon\", 18)(3, TabMenu_button_2_3_Template, 1, 0, null, 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.previousIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.previousIconTemplate);\n  }\n}\nfunction TabMenu_li_7_a_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 28);\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r5.icon)(\"ngStyle\", item_r5.iconStyle);\n  }\n}\nfunction TabMenu_li_7_a_2_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(item_r5, \"label\"));\n  }\n}\nfunction TabMenu_li_7_a_2_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemProp(item_r5, \"label\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction TabMenu_li_7_a_2_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", item_r5.badgeStyleClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(item_r5, \"badge\"));\n  }\n}\nfunction TabMenu_li_7_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 24, 5);\n    i0.ɵɵelementContainerStart(2);\n    i0.ɵɵtemplate(3, TabMenu_li_7_a_2_span_3_Template, 1, 2, \"span\", 25)(4, TabMenu_li_7_a_2_span_4_Template, 2, 1, \"span\", 26)(5, TabMenu_li_7_a_2_ng_template_5_Template, 1, 1, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor)(7, TabMenu_li_7_a_2_span_7_Template, 2, 2, \"span\", 27);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r8 = i0.ɵɵreference(6);\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", ctx_r2.getItemProp(item_r5, \"target\"));\n    i0.ɵɵattribute(\"href\", ctx_r2.getItemProp(item_r5, \"url\"), i0.ɵɵsanitizeUrl)(\"id\", ctx_r2.getItemProp(item_r5, \"id\"))(\"aria-disabled\", ctx_r2.disabled(item_r5))(\"aria-label\", ctx_r2.getItemProp(item_r5, \"label\"))(\"tabindex\", ctx_r2.disabled(item_r5) ? -1 : 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r5.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.escape !== false)(\"ngIfElse\", htmlLabel_r8);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r5.badge);\n  }\n}\nfunction TabMenu_li_7_a_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 28);\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r5.icon)(\"ngStyle\", item_r5.iconStyle);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction TabMenu_li_7_a_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(item_r5, \"label\"));\n  }\n}\nfunction TabMenu_li_7_a_3_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemProp(item_r5, \"label\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction TabMenu_li_7_a_3_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", item_r5.badgeStyleClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(item_r5, \"badge\"));\n  }\n}\nfunction TabMenu_li_7_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 32, 5);\n    i0.ɵɵelementContainerStart(2);\n    i0.ɵɵtemplate(3, TabMenu_li_7_a_3_span_3_Template, 1, 3, \"span\", 25)(4, TabMenu_li_7_a_3_span_4_Template, 2, 1, \"span\", 26)(5, TabMenu_li_7_a_3_ng_template_5_Template, 1, 1, \"ng-template\", null, 7, i0.ɵɵtemplateRefExtractor)(7, TabMenu_li_7_a_3_span_7_Template, 2, 2, \"span\", 27);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlRouteLabel_r9 = i0.ɵɵreference(6);\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", item_r5.routerLink)(\"queryParams\", item_r5.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", item_r5.routerLinkActiveOptions || i0.ɵɵpureFunction0(19, _c10))(\"target\", item_r5.target)(\"fragment\", item_r5.fragment)(\"queryParamsHandling\", item_r5.queryParamsHandling)(\"preserveFragment\", item_r5.preserveFragment)(\"skipLocationChange\", item_r5.skipLocationChange)(\"replaceUrl\", item_r5.replaceUrl)(\"state\", item_r5.state);\n    i0.ɵɵattribute(\"id\", ctx_r2.getItemProp(item_r5, \"id\"))(\"aria-disabled\", ctx_r2.disabled(item_r5))(\"aria-label\", ctx_r2.getItemProp(item_r5, \"label\"))(\"tabindex\", ctx_r2.disabled(item_r5) ? -1 : 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r5.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.escape !== false)(\"ngIfElse\", htmlRouteLabel_r9);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r5.badge);\n  }\n}\nfunction TabMenu_li_7_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TabMenu_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 20, 4);\n    i0.ɵɵlistener(\"click\", function TabMenu_li_7_Template_li_click_0_listener($event) {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.itemClick($event, item_r5));\n    })(\"keydown\", function TabMenu_li_7_Template_li_keydown_0_listener($event) {\n      const ctx_r5 = i0.ɵɵrestoreView(_r4);\n      const item_r5 = ctx_r5.$implicit;\n      const i_r7 = ctx_r5.index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onKeydownItem($event, i_r7, item_r5));\n    })(\"focus\", function TabMenu_li_7_Template_li_focus_0_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onMenuItemFocus(item_r5));\n    });\n    i0.ɵɵtemplate(2, TabMenu_li_7_a_2_Template, 8, 10, \"a\", 21)(3, TabMenu_li_7_a_3_Template, 8, 20, \"a\", 22)(4, TabMenu_li_7_ng_container_4_Template, 1, 0, \"ng-container\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(item_r5.styleClass);\n    i0.ɵɵproperty(\"ngStyle\", item_r5.style)(\"ngClass\", i0.ɵɵpureFunction3(11, _c8, ctx_r2.getItemProp(item_r5, \"disabled\"), ctx_r2.isActive(item_r5), item_r5.visible === false))(\"tooltipOptions\", item_r5.tooltipOptions);\n    i0.ɵɵattribute(\"data-p-disabled\", ctx_r2.disabled(item_r5))(\"data-p-highlight\", ctx_r2.focusedItemInfo() === item_r5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !item_r5.routerLink && !ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.routerLink && !ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(15, _c9, item_r5, i_r7));\n  }\n}\nfunction TabMenu_button_10_ChevronRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction TabMenu_button_10_3_ng_template_0_Template(rf, ctx) {}\nfunction TabMenu_button_10_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TabMenu_button_10_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TabMenu_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33, 8);\n    i0.ɵɵlistener(\"click\", function TabMenu_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navForward());\n    });\n    i0.ɵɵtemplate(2, TabMenu_button_10_ChevronRightIcon_2_Template, 1, 1, \"ChevronRightIcon\", 18)(3, TabMenu_button_10_3_Template, 1, 0, null, 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.previousIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.nextIconTemplate);\n  }\n}\nlet TabMenu = /*#__PURE__*/(() => {\n  class TabMenu {\n    platformId;\n    router;\n    route;\n    cd;\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    set model(value) {\n      this._model = value;\n      this._focusableItems = (this._model || []).reduce((result, item) => {\n        result.push(item);\n        return result;\n      }, []);\n    }\n    get model() {\n      return this._model;\n    }\n    /**\n     * Defines the default active menuitem\n     * @group Props\n     */\n    activeItem;\n    /**\n     * When enabled displays buttons at each side of the tab headers to scroll the tab list.\n     * @group Props\n     */\n    scrollable;\n    /**\n     * Defines if popup mode enabled.\n     */\n    popup;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Event fired when a tab is selected.\n     * @param {MenuItem} item - Menu item.\n     * @group Emits\n     */\n    activeItemChange = new EventEmitter();\n    content;\n    navbar;\n    inkbar;\n    prevBtn;\n    nextBtn;\n    tabLink;\n    tab;\n    templates;\n    itemTemplate;\n    previousIconTemplate;\n    nextIconTemplate;\n    tabChanged;\n    backwardIsDisabled = true;\n    forwardIsDisabled = false;\n    timerIdForInitialAutoScroll = null;\n    _focusableItems;\n    _model;\n    focusedItemInfo = signal(null);\n    get focusableItems() {\n      if (!this._focusableItems || !this._focusableItems.length) {\n        this._focusableItems = (this.model || []).reduce((result, item) => {\n          result.push(item);\n          return result;\n        }, []);\n      }\n      return this._focusableItems;\n    }\n    constructor(platformId, router, route, cd) {\n      this.platformId = platformId;\n      this.router = router;\n      this.route = route;\n      this.cd = cd;\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'item':\n            this.itemTemplate = item.template;\n            break;\n          case 'nexticon':\n            this.nextIconTemplate = item.template;\n            break;\n          case 'previousicon':\n            this.previousIconTemplate = item.template;\n            break;\n          default:\n            this.itemTemplate = item.template;\n            break;\n        }\n      });\n    }\n    ngAfterViewInit() {\n      if (isPlatformBrowser(this.platformId)) {\n        this.updateInkBar();\n        this.initAutoScrollForActiveItem();\n        this.initButtonState();\n      }\n    }\n    ngAfterViewChecked() {\n      if (this.tabChanged) {\n        this.updateInkBar();\n        this.tabChanged = false;\n      }\n    }\n    ngOnDestroy() {\n      this.clearAutoScrollHandler();\n    }\n    isActive(item) {\n      if (item.routerLink) {\n        const routerLink = Array.isArray(item.routerLink) ? item.routerLink : [item.routerLink];\n        return this.router.isActive(this.router.createUrlTree(routerLink, {\n          relativeTo: this.route\n        }).toString(), item.routerLinkActiveOptions?.exact ?? item.routerLinkActiveOptions ?? false);\n      }\n      return item === this.activeItem;\n    }\n    getItemProp(item, name) {\n      return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n    }\n    visible(item) {\n      return typeof item.visible === 'function' ? item.visible() : item.visible !== false;\n    }\n    disabled(item) {\n      return typeof item.disabled === 'function' ? item.disabled() : item.disabled;\n    }\n    onMenuItemFocus(item) {\n      this.focusedItemInfo.set(item);\n    }\n    itemClick(event, item) {\n      if (item.disabled) {\n        event.preventDefault();\n        return;\n      }\n      if (!item.url && !item.routerLink) {\n        event.preventDefault();\n      }\n      if (item.command) {\n        item.command({\n          originalEvent: event,\n          item: item\n        });\n      }\n      this.activeItem = item;\n      this.activeItemChange.emit(item);\n      this.tabChanged = true;\n      this.cd.markForCheck();\n    }\n    onKeydownItem(event, index, item) {\n      let i = index;\n      let foundElement = {};\n      const tabLinks = this.tabLink.toArray();\n      const tabs = this.tab.toArray();\n      switch (event.code) {\n        case 'ArrowRight':\n          foundElement = this.findNextItem(tabs, i);\n          i = foundElement['i'];\n          break;\n        case 'ArrowLeft':\n          foundElement = this.findPrevItem(tabs, i);\n          i = foundElement['i'];\n          break;\n        case 'End':\n          foundElement = this.findPrevItem(tabs, this.model.length);\n          i = foundElement['i'];\n          event.preventDefault();\n          break;\n        case 'Home':\n          foundElement = this.findNextItem(tabs, -1);\n          i = foundElement['i'];\n          event.preventDefault();\n          break;\n        case 'Space':\n        case 'Enter':\n          this.itemClick(event, item);\n          break;\n        case 'Tab':\n          this.onTabKeyDown(tabLinks);\n          break;\n        default:\n          break;\n      }\n      if (tabLinks[i] && tabLinks[index]) {\n        tabLinks[index].nativeElement.tabIndex = '-1';\n        tabLinks[i].nativeElement.tabIndex = '0';\n        tabLinks[i].nativeElement.focus();\n      }\n      this.cd.markForCheck();\n    }\n    onTabKeyDown(tabLinks) {\n      tabLinks.forEach(item => {\n        item.nativeElement.tabIndex = DomHandler.getAttribute(item.nativeElement.parentElement, 'data-p-highlight') ? '0' : '-1';\n      });\n    }\n    findNextItem(items, index) {\n      let i = index + 1;\n      if (i >= items.length) {\n        return {\n          nextItem: items[items.length],\n          i: items.length\n        };\n      }\n      let nextItem = items[i];\n      if (nextItem) return DomHandler.getAttribute(nextItem.nativeElement, 'data-p-disabled') ? this.findNextItem(items, i) : {\n        nextItem: nextItem.nativeElement,\n        i\n      };else return null;\n    }\n    findPrevItem(items, index) {\n      let i = index - 1;\n      if (i < 0) {\n        return {\n          prevItem: items[0],\n          i: 0\n        };\n      }\n      let prevItem = items[i];\n      if (prevItem) return DomHandler.getAttribute(prevItem.nativeElement, 'data-p-disabled') ? this.findPrevItem(items, i) : {\n        prevItem: prevItem.nativeElement,\n        i\n      };else return null;\n    }\n    updateInkBar() {\n      const tabHeader = DomHandler.findSingle(this.navbar?.nativeElement, 'li.p-highlight');\n      if (tabHeader) {\n        this.inkbar.nativeElement.style.width = DomHandler.getWidth(tabHeader) + 'px';\n        this.inkbar.nativeElement.style.left = DomHandler.getOffset(tabHeader).left - DomHandler.getOffset(this.navbar?.nativeElement).left + 'px';\n      }\n    }\n    getVisibleButtonWidths() {\n      return [this.prevBtn?.nativeElement, this.nextBtn?.nativeElement].reduce((acc, el) => el ? acc + DomHandler.getWidth(el) : acc, 0);\n    }\n    updateButtonState() {\n      const content = this.content?.nativeElement;\n      const {\n        scrollLeft,\n        scrollWidth\n      } = content;\n      const width = DomHandler.getWidth(content);\n      this.backwardIsDisabled = scrollLeft === 0;\n      this.forwardIsDisabled = parseInt(scrollLeft) === scrollWidth - width;\n    }\n    updateScrollBar(index) {\n      const tabHeader = this.navbar?.nativeElement.children[index];\n      if (!tabHeader) {\n        return;\n      }\n      tabHeader.scrollIntoView({\n        block: 'nearest',\n        inline: 'center'\n      });\n    }\n    onScroll(event) {\n      this.scrollable && this.updateButtonState();\n      event.preventDefault();\n    }\n    navBackward() {\n      const content = this.content?.nativeElement;\n      const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n      const pos = content.scrollLeft - width;\n      content.scrollLeft = pos <= 0 ? 0 : pos;\n    }\n    navForward() {\n      const content = this.content?.nativeElement;\n      const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n      const pos = content.scrollLeft + width;\n      const lastPos = content.scrollWidth - width;\n      content.scrollLeft = pos >= lastPos ? lastPos : pos;\n    }\n    initAutoScrollForActiveItem() {\n      if (!this.scrollable) {\n        return;\n      }\n      this.clearAutoScrollHandler();\n      // We have to wait for the rendering and then can scroll to element.\n      this.timerIdForInitialAutoScroll = setTimeout(() => {\n        const activeItem = this.model.findIndex(menuItem => this.isActive(menuItem));\n        if (activeItem !== -1) {\n          this.updateScrollBar(activeItem);\n        }\n      });\n    }\n    clearAutoScrollHandler() {\n      if (this.timerIdForInitialAutoScroll) {\n        clearTimeout(this.timerIdForInitialAutoScroll);\n        this.timerIdForInitialAutoScroll = null;\n      }\n    }\n    initButtonState() {\n      if (this.scrollable) {\n        // We have to wait for the rendering and then retrieve the actual size element from the DOM.\n        // in future `Promise.resolve` can be changed to `queueMicrotask` (if ie11 support will be dropped)\n        Promise.resolve().then(() => {\n          this.updateButtonState();\n          this.cd.markForCheck();\n        });\n      }\n    }\n    static ɵfac = function TabMenu_Factory(t) {\n      return new (t || TabMenu)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TabMenu,\n      selectors: [[\"p-tabMenu\"]],\n      contentQueries: function TabMenu_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function TabMenu_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n          i0.ɵɵviewQuery(_c6, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.navbar = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inkbar = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.prevBtn = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nextBtn = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabLink = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tab = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        model: \"model\",\n        activeItem: \"activeItem\",\n        scrollable: \"scrollable\",\n        popup: \"popup\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        ariaLabel: \"ariaLabel\",\n        ariaLabelledBy: \"ariaLabelledBy\"\n      },\n      outputs: {\n        activeItemChange: \"activeItemChange\"\n      },\n      decls: 11,\n      vars: 11,\n      consts: [[\"content\", \"\"], [\"navbar\", \"\"], [\"inkbar\", \"\"], [\"prevBtn\", \"\"], [\"tab\", \"\"], [\"tabLink\", \"\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"], [\"nextBtn\", \"\"], [3, \"ngClass\", \"ngStyle\"], [1, \"p-tabmenu-nav-container\"], [\"class\", \"p-tabmenu-nav-prev p-tabmenu-nav-btn p-link\", \"type\", \"button\", \"role\", \"navigation\", \"pRipple\", \"\", 3, \"click\", 4, \"ngIf\"], [1, \"p-tabmenu-nav-content\", 3, \"scroll\"], [\"role\", \"menubar\", 1, \"p-tabmenu-nav\", \"p-reset\"], [\"role\", \"presentation\", \"pTooltip\", \"\", 3, \"ngStyle\", \"class\", \"ngClass\", \"tooltipOptions\", \"click\", \"keydown\", \"focus\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"none\", 1, \"p-tabmenu-ink-bar\"], [\"class\", \"p-tabmenu-nav-next p-tabmenu-nav-btn p-link\", \"type\", \"button\", \"role\", \"navigation\", \"pRipple\", \"\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"role\", \"navigation\", \"pRipple\", \"\", 1, \"p-tabmenu-nav-prev\", \"p-tabmenu-nav-btn\", \"p-link\", 3, \"click\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [\"role\", \"presentation\", \"pTooltip\", \"\", 3, \"click\", \"keydown\", \"focus\", \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [\"class\", \"p-menuitem-link\", \"role\", \"menuitem\", \"pRipple\", \"\", 3, \"target\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"class\", \"p-menuitem-link\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"target\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [\"type\", \"button\", \"role\", \"navigation\", \"pRipple\", \"\", 1, \"p-tabmenu-nav-next\", \"p-tabmenu-nav-btn\", \"p-link\", 3, \"click\"]],\n      template: function TabMenu_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10);\n          i0.ɵɵtemplate(2, TabMenu_button_2_Template, 4, 2, \"button\", 11);\n          i0.ɵɵelementStart(3, \"div\", 12, 0);\n          i0.ɵɵlistener(\"scroll\", function TabMenu_Template_div_scroll_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onScroll($event));\n          });\n          i0.ɵɵelementStart(5, \"ul\", 13, 1);\n          i0.ɵɵtemplate(7, TabMenu_li_7_Template, 5, 18, \"li\", 14);\n          i0.ɵɵelement(8, \"li\", 15, 2);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(10, TabMenu_button_10_Template, 4, 2, \"button\", 16);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c7, ctx.scrollable))(\"ngStyle\", ctx.style);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.scrollable && !ctx.backwardIsDisabled);\n          i0.ɵɵadvance(3);\n          i0.ɵɵattribute(\"aria-labelledby\", ctx.ariaLabelledBy)(\"aria-label\", ctx.ariaLabel);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.focusableItems);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.scrollable && !ctx.forwardIsDisabled);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i1.RouterLink, i1.RouterLinkActive, i3.Ripple, i4.Tooltip, ChevronLeftIcon, ChevronRightIcon],\n      styles: [\"@layer primeng{.p-tabmenu-nav-container{position:relative}.p-tabmenu-scrollable .p-tabmenu-nav-container{overflow:hidden}.p-tabmenu-nav-content{overflow-x:auto;overflow-y:hidden;scroll-behavior:smooth;scrollbar-width:none;overscroll-behavior:contain auto}.p-tabmenu-nav-btn{position:absolute;top:0;z-index:2;height:100%;display:flex;align-items:center;justify-content:center}.p-tabmenu-nav-prev{left:0}.p-tabmenu-nav-next{right:0}.p-tabview-nav-content::-webkit-scrollbar{display:none}.p-tabmenu-nav{display:flex;margin:0;padding:0;list-style-type:none;flex-wrap:nowrap}.p-tabmenu-nav a{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center;position:relative;text-decoration:none;overflow:hidden}.p-tabmenu-nav a:focus{z-index:1}.p-tabmenu-nav .p-menuitem-text{line-height:1;white-space:nowrap}.p-tabmenu-ink-bar{display:none;z-index:1}.p-tabmenu-nav-content::-webkit-scrollbar{display:none}.p-tabmenuitem:not(.p-hidden){display:flex}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return TabMenu;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TabMenuModule = /*#__PURE__*/(() => {\n  class TabMenuModule {\n    static ɵfac = function TabMenuModule_Factory(t) {\n      return new (t || TabMenuModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TabMenuModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule, SharedModule, RippleModule, TooltipModule, ChevronLeftIcon, ChevronRightIcon, RouterModule, SharedModule, TooltipModule]\n    });\n  }\n  return TabMenuModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TabMenu, TabMenuModule };\n//# sourceMappingURL=primeng-tabmenu.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}