{"ast": null, "code": "import { operate } from '../util/lift';\nimport { scanInternals } from './scanInternals';\nexport function scan(accumulator, seed) {\n  return operate(scanInternals(accumulator, seed, arguments.length >= 2, true));\n}", "map": {"version": 3, "names": ["operate", "scanInternals", "scan", "accumulator", "seed", "arguments", "length"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/rxjs/dist/esm/internal/operators/scan.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { scanInternals } from './scanInternals';\nexport function scan(accumulator, seed) {\n    return operate(scanInternals(accumulator, seed, arguments.length >= 2, true));\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,OAAO,SAASC,IAAIA,CAACC,WAAW,EAAEC,IAAI,EAAE;EACpC,OAAOJ,OAAO,CAACC,aAAa,CAACE,WAAW,EAAEC,IAAI,EAAEC,SAAS,CAACC,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACjF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}