{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../activities.service\";\nimport * as i2 from \"primeng/table\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/button\";\nimport * as i5 from \"primeng/tooltip\";\nimport * as i6 from \"@angular/common\";\nfunction SalesCallFollowItemsComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 10)(2, \"div\", 11);\n    i0.ɵɵtext(3, \"Name\");\n    i0.ɵɵelement(4, \"p-sortIcon\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 13)(6, \"div\", 11);\n    i0.ɵɵtext(7, \"Type\");\n    i0.ɵɵelement(8, \"p-sortIcon\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 15)(10, \"div\", 11);\n    i0.ɵɵtext(11, \"Responsible\");\n    i0.ɵɵelement(12, \"p-sortIcon\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 17)(14, \"div\", 11);\n    i0.ɵɵtext(15, \"Created On\");\n    i0.ɵɵelement(16, \"p-sortIcon\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"th\");\n    i0.ɵɵtext(18, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 19)(11, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function SalesCallFollowItemsComponent_ng_template_8_Template_button_click_11_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const followup_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (followup_r2 == null ? null : followup_r2.acitivity_type_code) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (followup_r2 == null ? null : followup_r2.type_code) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (followup_r2 == null ? null : followup_r2.btp_role_code) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(9, 4, followup_r2 == null ? null : followup_r2.createdAt, \"dd-MM-yyyy HH:mm:ss\"), \" \");\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 21);\n    i0.ɵɵtext(2, \"No follow up found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallFollowItemsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 21);\n    i0.ɵɵtext(2, \"Loading follow up data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class SalesCallFollowItemsComponent {\n  constructor(activitiesservice) {\n    this.activitiesservice = activitiesservice;\n    this.unsubscribe$ = new Subject();\n    this.followupdetails = null;\n    this.activity_id = '';\n  }\n  ngOnInit() {\n    this.activitiesservice.activity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.activity_id = response?.activity_id;\n        this.followupdetails = response?.follow_up_and_related_items;\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function SalesCallFollowItemsComponent_Factory(t) {\n      return new (t || SalesCallFollowItemsComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SalesCallFollowItemsComponent,\n      selectors: [[\"app-sales-call-follow-items\"]],\n      decls: 11,\n      vars: 5,\n      consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"paginator\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pSortableColumn\", \"acitivity_type_code\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"acitivity_type_code\"], [\"pSortableColumn\", \"type_code\"], [\"field\", \"type_code\"], [\"pSortableColumn\", \"btp_role_code\"], [\"field\", \"btp_role_code\"], [\"pSortableColumn\", \"createdAt\"], [\"field\", \"createdAt\"], [1, \"border-round-right-lg\", \"text-center\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"click\"], [\"colspan\", \"6\"]],\n      template: function SalesCallFollowItemsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Follow Up Items\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-button\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-table\", 5);\n          i0.ɵɵtemplate(7, SalesCallFollowItemsComponent_ng_template_7_Template, 19, 0, \"ng-template\", 6)(8, SalesCallFollowItemsComponent_ng_template_8_Template, 12, 7, \"ng-template\", 7)(9, SalesCallFollowItemsComponent_ng_template_9_Template, 3, 0, \"ng-template\", 8)(10, SalesCallFollowItemsComponent_ng_template_10_Template, 3, 0, \"ng-template\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.followupdetails)(\"rows\", 10)(\"paginator\", true);\n        }\n      },\n      dependencies: [i2.Table, i3.PrimeTemplate, i2.SortableColumn, i2.SortIcon, i4.ButtonDirective, i4.Button, i5.Tooltip, i6.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "SalesCallFollowItemsComponent_ng_template_8_Template_button_click_11_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵresetView", "stopPropagation", "ɵɵadvance", "ɵɵtextInterpolate1", "followup_r2", "acitivity_type_code", "type_code", "btp_role_code", "ɵɵpipeBind2", "createdAt", "SalesCallFollowItemsComponent", "constructor", "activitiesservice", "unsubscribe$", "followupdetails", "activity_id", "ngOnInit", "activity", "pipe", "subscribe", "response", "follow_up_and_related_items", "ngOnDestroy", "next", "complete", "ɵɵdirectiveInject", "i1", "ActivitiesService", "selectors", "decls", "vars", "consts", "template", "SalesCallFollowItemsComponent_Template", "rf", "ctx", "ɵɵtemplate", "SalesCallFollowItemsComponent_ng_template_7_Template", "SalesCallFollowItemsComponent_ng_template_8_Template", "SalesCallFollowItemsComponent_ng_template_9_Template", "SalesCallFollowItemsComponent_ng_template_10_Template", "ɵɵproperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-follow-items\\sales-call-follow-items.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-follow-items\\sales-call-follow-items.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { ActivitiesService } from '../../../activities.service';\r\n\r\n@Component({\r\n  selector: 'app-sales-call-follow-items',\r\n  templateUrl: './sales-call-follow-items.component.html',\r\n  styleUrl: './sales-call-follow-items.component.scss',\r\n})\r\nexport class SalesCallFollowItemsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public followupdetails: any = null;\r\n  public activity_id: string = '';\r\n\r\n  constructor(private activitiesservice: ActivitiesService) {}\r\n\r\n  ngOnInit() {\r\n    this.activitiesservice.activity\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.activity_id = response?.activity_id;\r\n          this.followupdetails = response?.follow_up_and_related_items;\r\n        }\r\n      });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Follow Up Items</h4>\r\n        <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n            [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"followupdetails\" dataKey=\"id\" [rows]=\"10\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pSortableColumn=\"acitivity_type_code\">\r\n                        <div class=\"flex align-items-center gap-2\">Name<p-sortIcon\r\n                                field=\"acitivity_type_code\"></p-sortIcon></div>\r\n                    </th>\r\n                    <th pSortableColumn=\"type_code\">\r\n                        <div class=\"flex align-items-center gap-2\">Type<p-sortIcon field=\"type_code\"></p-sortIcon></div>\r\n                    </th>\r\n                    <th pSortableColumn=\"btp_role_code\">\r\n                        <div class=\"flex align-items-center gap-2\">Responsible<p-sortIcon\r\n                                field=\"btp_role_code\"></p-sortIcon></div>\r\n                    </th>\r\n                    <th pSortableColumn=\"createdAt\">\r\n                        <div class=\"flex align-items-center gap-2\">Created On<p-sortIcon field=\"createdAt\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th>Actions</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-followup>\r\n                <tr>\r\n                    <td>\r\n                        {{ followup?.acitivity_type_code || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ followup?.type_code || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ followup?.btp_role_code || '-' }}\r\n                    </td>\r\n                    <td>\r\n                        {{ followup?.createdAt | date:'dd-MM-yyyy HH:mm:ss'}}\r\n                    </td>\r\n                    <td class=\"border-round-right-lg text-center\">\r\n                        <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                            (click)=\"$event.stopPropagation();\"></button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"6\">No follow up found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"6\">Loading follow up data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;ICajBC,EAFR,CAAAC,cAAA,SAAI,aAC0C,cACK;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,SAAA,qBACE;IACrDH,EADqD,CAAAI,YAAA,EAAM,EACtD;IAEDJ,EADJ,CAAAC,cAAA,aAAgC,cACe;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,SAAA,qBAA2C;IAC9FH,EAD8F,CAAAI,YAAA,EAAM,EAC/F;IAEDJ,EADJ,CAAAC,cAAA,aAAoC,eACW;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,SAAA,sBACX;IAC/CH,EAD+C,CAAAI,YAAA,EAAM,EAChD;IAEDJ,EADJ,CAAAC,cAAA,cAAgC,eACe;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,SAAA,sBAA2C;IAEpGH,EADI,CAAAI,YAAA,EAAM,EACL;IACLJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IACfF,EADe,CAAAI,YAAA,EAAK,EACf;;;;;;IAKDJ,EADJ,CAAAC,cAAA,SAAI,SACI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAI,YAAA,EAAK;IAEDJ,EADJ,CAAAC,cAAA,cAA8C,kBAEF;IAApCD,EAAA,CAAAK,UAAA,mBAAAC,8EAAAC,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASH,MAAA,CAAAI,eAAA,EAAwB;IAAA,EAAE;IAE/CX,EAFgD,CAAAI,YAAA,EAAS,EAChD,EACJ;;;;IAfGJ,EAAA,CAAAY,SAAA,GACJ;IADIZ,EAAA,CAAAa,kBAAA,OAAAC,WAAA,kBAAAA,WAAA,CAAAC,mBAAA,cACJ;IAEIf,EAAA,CAAAY,SAAA,GACJ;IADIZ,EAAA,CAAAa,kBAAA,OAAAC,WAAA,kBAAAA,WAAA,CAAAE,SAAA,cACJ;IAEIhB,EAAA,CAAAY,SAAA,GACJ;IADIZ,EAAA,CAAAa,kBAAA,OAAAC,WAAA,kBAAAA,WAAA,CAAAG,aAAA,cACJ;IAEIjB,EAAA,CAAAY,SAAA,GACJ;IADIZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAkB,WAAA,OAAAJ,WAAA,kBAAAA,WAAA,CAAAK,SAAA,8BACJ;;;;;IASAnB,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IACvCF,EADuC,CAAAI,YAAA,EAAK,EACvC;;;;;IAIDJ,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,2CAAoC;IACxDF,EADwD,CAAAI,YAAA,EAAK,EACxD;;;ADnDrB,OAAM,MAAOgB,6BAA6B;EAKxCC,YAAoBC,iBAAoC;IAApC,KAAAA,iBAAiB,GAAjBA,iBAAiB;IAJ7B,KAAAC,YAAY,GAAG,IAAIzB,OAAO,EAAQ;IACnC,KAAA0B,eAAe,GAAQ,IAAI;IAC3B,KAAAC,WAAW,GAAW,EAAE;EAE4B;EAE3DC,QAAQA,CAAA;IACN,IAAI,CAACJ,iBAAiB,CAACK,QAAQ,CAC5BC,IAAI,CAAC7B,SAAS,CAAC,IAAI,CAACwB,YAAY,CAAC,CAAC,CAClCM,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACL,WAAW,GAAGK,QAAQ,EAAEL,WAAW;QACxC,IAAI,CAACD,eAAe,GAAGM,QAAQ,EAAEC,2BAA2B;MAC9D;IACF,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACT,YAAY,CAACU,IAAI,EAAE;IACxB,IAAI,CAACV,YAAY,CAACW,QAAQ,EAAE;EAC9B;;;uBArBWd,6BAA6B,EAAApB,EAAA,CAAAmC,iBAAA,CAAAC,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAA7BjB,6BAA6B;MAAAkB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPlC5C,EAFR,CAAAC,cAAA,aAAuD,aACgC,YAChC;UAAAD,EAAA,CAAAE,MAAA,sBAAe;UAAAF,EAAA,CAAAI,YAAA,EAAK;UACnEJ,EAAA,CAAAG,SAAA,kBAC2D;UAC/DH,EAAA,CAAAI,YAAA,EAAM;UAGFJ,EADJ,CAAAC,cAAA,aAAuB,iBAEW;UAgD1BD,EA9CA,CAAA8C,UAAA,IAAAC,oDAAA,0BAAgC,IAAAC,oDAAA,0BAqBW,IAAAC,oDAAA,yBAoBL,KAAAC,qDAAA,yBAKD;UAOjDlD,EAFQ,CAAAI,YAAA,EAAU,EACR,EACJ;;;UA5DMJ,EAAA,CAAAY,SAAA,GAAmC;UAACZ,EAApC,CAAAmD,UAAA,oCAAmC,iBAAiB;UAI/CnD,EAAA,CAAAY,SAAA,GAAyB;UAAwCZ,EAAjE,CAAAmD,UAAA,UAAAN,GAAA,CAAArB,eAAA,CAAyB,YAAyB,mBAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}