{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Icelandic [is]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/hinrik\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function plural(n) {\n    if (n % 100 === 11) {\n      return true;\n    } else if (n % 10 === 1) {\n      return false;\n    }\n    return true;\n  }\n  function translate(number, withoutSuffix, key, isFuture) {\n    var result = number + ' ';\n    switch (key) {\n      case 's':\n        return withoutSuffix || isFuture ? 'nokkrar sekúndur' : 'nokkrum sekúndum';\n      case 'ss':\n        if (plural(number)) {\n          return result + (withoutSuffix || isFuture ? 'sekúndur' : 'sekúndum');\n        }\n        return result + 'sekúnda';\n      case 'm':\n        return withoutSuffix ? 'mínúta' : 'mínútu';\n      case 'mm':\n        if (plural(number)) {\n          return result + (withoutSuffix || isFuture ? 'mínútur' : 'mínútum');\n        } else if (withoutSuffix) {\n          return result + 'mínúta';\n        }\n        return result + 'mínútu';\n      case 'hh':\n        if (plural(number)) {\n          return result + (withoutSuffix || isFuture ? 'klukkustundir' : 'klukkustundum');\n        }\n        return result + 'klukkustund';\n      case 'd':\n        if (withoutSuffix) {\n          return 'dagur';\n        }\n        return isFuture ? 'dag' : 'degi';\n      case 'dd':\n        if (plural(number)) {\n          if (withoutSuffix) {\n            return result + 'dagar';\n          }\n          return result + (isFuture ? 'daga' : 'dögum');\n        } else if (withoutSuffix) {\n          return result + 'dagur';\n        }\n        return result + (isFuture ? 'dag' : 'degi');\n      case 'M':\n        if (withoutSuffix) {\n          return 'mánuður';\n        }\n        return isFuture ? 'mánuð' : 'mánuði';\n      case 'MM':\n        if (plural(number)) {\n          if (withoutSuffix) {\n            return result + 'mánuðir';\n          }\n          return result + (isFuture ? 'mánuði' : 'mánuðum');\n        } else if (withoutSuffix) {\n          return result + 'mánuður';\n        }\n        return result + (isFuture ? 'mánuð' : 'mánuði');\n      case 'y':\n        return withoutSuffix || isFuture ? 'ár' : 'ári';\n      case 'yy':\n        if (plural(number)) {\n          return result + (withoutSuffix || isFuture ? 'ár' : 'árum');\n        }\n        return result + (withoutSuffix || isFuture ? 'ár' : 'ári');\n    }\n  }\n  var is = moment.defineLocale('is', {\n    months: 'janúar_febrúar_mars_apríl_maí_júní_júlí_ágúst_september_október_nóvember_desember'.split('_'),\n    monthsShort: 'jan_feb_mar_apr_maí_jún_júl_ágú_sep_okt_nóv_des'.split('_'),\n    weekdays: 'sunnudagur_mánudagur_þriðjudagur_miðvikudagur_fimmtudagur_föstudagur_laugardagur'.split('_'),\n    weekdaysShort: 'sun_mán_þri_mið_fim_fös_lau'.split('_'),\n    weekdaysMin: 'Su_Má_Þr_Mi_Fi_Fö_La'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY [kl.] H:mm',\n      LLLL: 'dddd, D. MMMM YYYY [kl.] H:mm'\n    },\n    calendar: {\n      sameDay: '[í dag kl.] LT',\n      nextDay: '[á morgun kl.] LT',\n      nextWeek: 'dddd [kl.] LT',\n      lastDay: '[í gær kl.] LT',\n      lastWeek: '[síðasta] dddd [kl.] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'eftir %s',\n      past: 'fyrir %s síðan',\n      s: translate,\n      ss: translate,\n      m: translate,\n      mm: translate,\n      h: 'klukkustund',\n      hh: translate,\n      d: translate,\n      dd: translate,\n      M: translate,\n      MM: translate,\n      y: translate,\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return is;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}