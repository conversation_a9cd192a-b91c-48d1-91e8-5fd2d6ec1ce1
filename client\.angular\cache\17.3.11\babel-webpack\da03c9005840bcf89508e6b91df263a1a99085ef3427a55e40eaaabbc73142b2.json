{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/progressspinner\";\nfunction AccountSalesOrdersComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountSalesOrdersComponent_p_table_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 14)(2, \"div\", 15);\n    i0.ɵɵtext(3, \" Order # \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"th\", 17)(6, \"div\", 15);\n    i0.ɵɵtext(7, \" P.O. # \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 19)(10, \"div\", 15);\n    i0.ɵɵtext(11, \" Date Placed \");\n    i0.ɵɵelement(12, \"p-sortIcon\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 21)(14, \"div\", 15);\n    i0.ɵɵtext(15, \" Order Status \");\n    i0.ɵɵelement(16, \"p-sortIcon\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"th\", 23)(18, \"div\", 15);\n    i0.ɵɵtext(19, \" Net Amount \");\n    i0.ɵɵelement(20, \"p-sortIcon\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"th\", 25)(22, \"div\", 15);\n    i0.ɵɵtext(23, \" Currency \");\n    i0.ɵɵelement(24, \"p-sortIcon\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"th\", 27)(26, \"div\", 15);\n    i0.ɵɵtext(27, \" Channel \");\n    i0.ɵɵelement(28, \"p-sortIcon\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountSalesOrdersComponent_p_table_7_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 29);\n    i0.ɵɵlistener(\"click\", function AccountSalesOrdersComponent_p_table_7_ng_template_3_Template_tr_click_0_listener() {\n      const order_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToOrderDetail(order_r2));\n    });\n    i0.ɵɵelementStart(1, \"td\", 30);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const order_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", order_r2 == null ? null : order_r2.SD_DOC, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", order_r2 == null ? null : order_r2.PURCH_NO, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(7, 7, order_r2 == null ? null : order_r2.DOC_DATE, \"MM/dd/yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", order_r2 == null ? null : order_r2.DOC_STATUS, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", order_r2 == null ? null : order_r2.TOTAL_NET_AMOUNT, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", order_r2 == null ? null : order_r2.TXN_CURRENCY, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", order_r2 == null ? null : order_r2.CHANNEL, \" \");\n  }\n}\nfunction AccountSalesOrdersComponent_p_table_7_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 31);\n    i0.ɵɵtext(2, \"No orders found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSalesOrdersComponent_p_table_7_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 31);\n    i0.ɵɵtext(2, \"Loading orders data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AccountSalesOrdersComponent_p_table_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-table\", 9, 0);\n    i0.ɵɵtemplate(2, AccountSalesOrdersComponent_p_table_7_ng_template_2_Template, 29, 0, \"ng-template\", 10)(3, AccountSalesOrdersComponent_p_table_7_ng_template_3_Template, 16, 10, \"ng-template\", 11)(4, AccountSalesOrdersComponent_p_table_7_ng_template_4_Template, 3, 0, \"ng-template\", 12)(5, AccountSalesOrdersComponent_p_table_7_ng_template_5_Template, 3, 0, \"ng-template\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r2.OrderData)(\"rows\", 10)(\"rowHover\", true)(\"paginator\", true)(\"totalRecords\", ctx_r2.totalRecords);\n  }\n}\nexport class AccountSalesOrdersComponent {\n  constructor(accountservice, router, route) {\n    this.accountservice = accountservice;\n    this.router = router;\n    this.route = route;\n    this.unsubscribe$ = new Subject();\n    this.totalRecords = 0;\n    this.salesloading = true;\n    this.orderStatuses = [];\n    this.orderStatusesValue = {};\n    this.orderValue = {};\n    this.orderType = '';\n    this.first = 0;\n    this.rows = 10;\n    this.currentPage = 1;\n    this.allData = [];\n    this.OrderData = [];\n    this.customer = {};\n    this.isCustomerLoaded = false;\n    this.isOrderTypeLoaded = false;\n    this.orderDetail = null;\n    this.address = [];\n  }\n  ngOnInit() {\n    this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.getPartnerFunction(response.customer.customer_id);\n      }\n    });\n    this.accountservice.fetchOrderStatuses({\n      'filters[type][$eq]': 'ORDER_STATUS'\n    }).subscribe({\n      next: response => {\n        if (response?.data.length) {\n          this.orderStatuses = response.data.map(val => {\n            this.orderStatusesValue[val.description] = val.code;\n            this.orderValue[val.code] = val.description;\n            return val.description;\n          });\n          this.orderStatuses = ['All', ...this.orderStatuses];\n        }\n      },\n      error: error => {\n        console.error('Error fetching order statuses:', error);\n      }\n    });\n    this.accountservice.fetchOrderStatuses({\n      'filters[type][$eq]': 'ORDER_TYPE'\n    }).subscribe({\n      next: response => {\n        if (response?.data.length) {\n          this.orderType = response.data.map(val => val.code).join(';');\n        }\n        this.isOrderTypeLoaded = true;\n        this.triggerFetchOrders();\n      },\n      error: error => {\n        console.error('Error fetching order types:', error);\n        this.isOrderTypeLoaded = true;\n        this.triggerFetchOrders();\n      }\n    });\n  }\n  getPartnerAddress(bp_id, callback) {\n    this.accountservice.fetchPartnerById(bp_id).subscribe({\n      next: value => {\n        const formattedAddresses = value?.data.map(account => {\n          const defaultAddress = account?.address_usages?.find(usage => usage?.address_usage === 'XXDEFAULT')?.business_partner_address;\n          return {\n            ...account,\n            address: [defaultAddress?.house_number || '-', defaultAddress?.street_name || '-', defaultAddress?.city_name || '-', defaultAddress?.region || '-', defaultAddress?.country || '-', defaultAddress?.postal_code || '-'].filter(part => part && part !== '-').join(', ')\n          };\n        }) || [];\n        this.address = formattedAddresses;\n        if (callback && this.address.length > 0) {\n          callback(this.address[0].address);\n        }\n      },\n      error: err => {\n        console.error('Error fetching partner address:', err);\n      }\n    });\n  }\n  getPartnerFunction(soldToParty) {\n    this.accountservice.getPartnerFunction(soldToParty).subscribe({\n      next: value => {\n        this.customer = value.find(o => o.customer_id === soldToParty && o.partner_function === 'SH');\n        this.getPartnerAddress(this.customer?.customer.bp_id, formattedAddress => {\n          if (this.customer) {\n            this.customer.customer.address = formattedAddress;\n          }\n        });\n        this.isCustomerLoaded = true;\n        this.triggerFetchOrders();\n      },\n      error: error => {\n        console.error('Error fetching partner function:', error);\n        this.isCustomerLoaded = true;\n        this.triggerFetchOrders();\n      }\n    });\n  }\n  triggerFetchOrders() {\n    if (this.isCustomerLoaded && this.isOrderTypeLoaded) {\n      this.fetchOrders(1000);\n    }\n  }\n  fetchOrders(count) {\n    this.salesloading = true;\n    const rawParams = {\n      SOLDTO: this.customer?.customer_id,\n      VKORG: this.customer?.sales_organization,\n      COUNT: count,\n      DOC_STATUS: Object.keys(this.orderValue).join(';'),\n      DOC_TYPE: this.orderType\n    };\n    const params = Object.fromEntries(Object.entries(rawParams).filter(([_, value]) => value !== undefined && value !== ''));\n    this.accountservice.fetchOrders(params).subscribe({\n      next: response => {\n        if (response?.resultData && response.resultData.length > 0) {\n          this.OrderData = response.resultData.map(record => ({\n            PURCH_NO: record?.PURCH_NO || '-',\n            SD_DOC: record?.SD_DOC || '-',\n            CHANNEL: record?.CHANNEL || '-',\n            DOC_TYPE: record?.DOC_TYPE || '-',\n            DOC_STATUS: record.DOC_STATUS ? this.orderValue[record.DOC_STATUS] : '-',\n            TXN_CURRENCY: record?.TXN_CURRENCY || '-',\n            DOC_DATE: record?.DOC_DATE ? `${record.DOC_DATE.substring(0, 4)}-${record.DOC_DATE.substring(4, 6)}-${record.DOC_DATE.substring(6, 8)}` : '-',\n            TOTAL_NET_AMOUNT: record?.TOTAL_NET_AMOUNT || '-'\n          }));\n          this.allData = [...this.OrderData];\n          this.totalRecords = this.allData.length;\n          this.paginateData();\n        } else {\n          this.allData = [];\n          this.totalRecords = 0;\n          this.paginateData();\n        }\n        this.salesloading = false;\n      },\n      error: error => {\n        console.error('Error fetching sales orders:', error);\n        this.salesloading = false;\n      }\n    });\n  }\n  onPageChange(event) {\n    this.first = event.first;\n    this.rows = event.rows;\n    this.currentPage = this.first / this.rows + 1;\n    if (this.first + this.rows >= this.allData.length) {\n      this.fetchOrders(this.allData.length + 1000);\n    }\n    this.paginateData();\n  }\n  paginateData() {\n    this.OrderData = this.allData.slice(this.first, this.first + this.rows);\n  }\n  navigateToOrderDetail(order) {\n    this.router.navigate([order.SD_DOC], {\n      relativeTo: this.route,\n      state: {\n        orderData: order,\n        customerData: this.customer?.customer\n      }\n    });\n  }\n  static {\n    this.ɵfac = function AccountSalesOrdersComponent_Factory(t) {\n      return new (t || AccountSalesOrdersComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountSalesOrdersComponent,\n      selectors: [[\"app-account-sales-orders\"]],\n      decls: 8,\n      vars: 4,\n      consts: [[\"dt1\", \"\"], [1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"styleClass\", \"rounded\"], [1, \"table-sec\"], [\"class\", \"flex justify-content-center align-items-center w-full my-4\", 4, \"ngIf\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"rowHover\", \"paginator\", \"totalRecords\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"my-4\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"rowHover\", \"paginator\", \"totalRecords\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pSortableColumn\", \"SD_DOC\", 1, \"border-round-left-lg\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"field\", \"SD_DOC\"], [\"pSortableColumn\", \"PURCH_NO\"], [\"field\", \"PURCH_NO\"], [\"pSortableColumn\", \"DOC_DATE\"], [\"field\", \"DOC_DATE\"], [\"pSortableColumn\", \"DOC_STATUS\"], [\"field\", \"DOC_STATUS\"], [\"pSortableColumn\", \"TOTAL_NET_AMOUNT\"], [\"field\", \"TOTAL_NET_AMOUNT\"], [\"pSortableColumn\", \"TXN_CURRENCY\"], [\"field\", \"TXN_CURRENCY\"], [\"pSortableColumn\", \"CHANNEL\"], [\"field\", \"CHANNEL\"], [1, \"cursor-pointer\", 3, \"click\"], [1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"], [\"colspan\", \"7\", 1, \"border-round-left-lg\"]],\n      template: function AccountSalesOrdersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h4\", 3);\n          i0.ɵɵtext(3, \"Sales Orders\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-button\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5);\n          i0.ɵɵtemplate(6, AccountSalesOrdersComponent_div_6_Template, 2, 0, \"div\", 6)(7, AccountSalesOrdersComponent_p_table_7_Template, 6, 5, \"p-table\", 7);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.salesloading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.salesloading);\n        }\n      },\n      dependencies: [i3.NgIf, i4.PrimeTemplate, i5.Table, i5.SortableColumn, i5.SortIcon, i6.Button, i7.ProgressSpinner, i3.DatePipe],\n      styles: [\".p-sidebar-header {\\n  display: none !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvYWNjb3VudC9hY2NvdW50LWRldGFpbHMvYWNjb3VudC1zYWxlcy1vcmRlcnMvYWNjb3VudC1zYWxlcy1vcmRlcnMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSx3QkFBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwIC5wLXNpZGViYXItaGVhZGVyIHtcclxuICAgIGRpc3BsYXk6IG5vbmUgIWltcG9ydGFudDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "AccountSalesOrdersComponent_p_table_7_ng_template_3_Template_tr_click_0_listener", "order_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "navigateToOrderDetail", "ɵɵadvance", "ɵɵtextInterpolate1", "SD_DOC", "PURCH_NO", "ɵɵpipeBind2", "DOC_DATE", "DOC_STATUS", "TOTAL_NET_AMOUNT", "TXN_CURRENCY", "CHANNEL", "ɵɵtemplate", "AccountSalesOrdersComponent_p_table_7_ng_template_2_Template", "AccountSalesOrdersComponent_p_table_7_ng_template_3_Template", "AccountSalesOrdersComponent_p_table_7_ng_template_4_Template", "AccountSalesOrdersComponent_p_table_7_ng_template_5_Template", "ɵɵproperty", "OrderData", "totalRecords", "AccountSalesOrdersComponent", "constructor", "accountservice", "router", "route", "unsubscribe$", "salesloading", "orderStatuses", "orderStatusesValue", "orderValue", "orderType", "first", "rows", "currentPage", "allData", "customer", "isCustomerLoaded", "isOrderTypeLoaded", "orderDetail", "address", "ngOnInit", "account", "pipe", "subscribe", "response", "getPartnerFunction", "customer_id", "fetchOrderStatuses", "next", "data", "length", "map", "val", "description", "code", "error", "console", "join", "triggerFetchOrders", "getPartnerAddress", "bp_id", "callback", "fetchPartnerById", "value", "formattedAddresses", "defaultAddress", "address_usages", "find", "usage", "address_usage", "business_partner_address", "house_number", "street_name", "city_name", "region", "country", "postal_code", "filter", "part", "err", "soldToParty", "o", "partner_function", "formattedAddress", "fetchOrders", "count", "rawParams", "SOLDTO", "VKORG", "sales_organization", "COUNT", "Object", "keys", "DOC_TYPE", "params", "fromEntries", "entries", "_", "undefined", "resultData", "record", "substring", "paginateData", "onPageChange", "event", "slice", "order", "navigate", "relativeTo", "state", "orderData", "customerData", "ɵɵdirectiveInject", "i1", "AccountService", "i2", "Router", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "AccountSalesOrdersComponent_Template", "rf", "ctx", "AccountSalesOrdersComponent_div_6_Template", "AccountSalesOrdersComponent_p_table_7_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-sales-orders\\account-sales-orders.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\account\\account-details\\account-sales-orders\\account-sales-orders.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AccountService } from '../../account.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport * as moment from 'moment';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\n\r\ninterface AccountTableData {\r\n  PURCH_NO?: string;\r\n  SD_DOC?: string;\r\n  CHANNEL?: string;\r\n  DOC_TYPE?: string;\r\n  DOC_STATUS?: string;\r\n  TXN_CURRENCY?: string;\r\n  DOC_DATE?: string;\r\n  TOTAL_NET_AMOUNT?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-account-sales-orders',\r\n  templateUrl: './account-sales-orders.component.html',\r\n  styleUrl: './account-sales-orders.component.scss',\r\n})\r\nexport class AccountSalesOrdersComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public totalRecords: number = 0;\r\n  public salesloading: boolean = true;\r\n  public orderStatuses: any[] = [];\r\n  public orderStatusesValue: any = {};\r\n  public orderValue: any = {};\r\n  public orderType: string = '';\r\n  public first: number = 0;\r\n  public rows: number = 10;\r\n  public currentPage: number = 1;\r\n  public allData: AccountTableData[] = [];\r\n  public OrderData: AccountTableData[] = [];\r\n  public customer: any = {};\r\n  private isCustomerLoaded = false;\r\n  private isOrderTypeLoaded = false;\r\n  public orderDetail: any = null;\r\n  public address: any[] = [];\r\n\r\n  constructor(\r\n    private accountservice: AccountService,\r\n    private router: Router,\r\n    private route: ActivatedRoute\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.accountservice.account\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.getPartnerFunction(response.customer.customer_id);\r\n        }\r\n      });\r\n\r\n    this.accountservice\r\n      .fetchOrderStatuses({\r\n        'filters[type][$eq]': 'ORDER_STATUS',\r\n      })\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (response?.data.length) {\r\n            this.orderStatuses = response.data.map((val: any) => {\r\n              this.orderStatusesValue[val.description] = val.code;\r\n              this.orderValue[val.code] = val.description;\r\n              return val.description;\r\n            });\r\n            this.orderStatuses = ['All', ...this.orderStatuses];\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching order statuses:', error);\r\n        },\r\n      });\r\n\r\n    this.accountservice\r\n      .fetchOrderStatuses({\r\n        'filters[type][$eq]': 'ORDER_TYPE',\r\n      })\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (response?.data.length) {\r\n            this.orderType = response.data\r\n              .map((val: any) => val.code)\r\n              .join(';');\r\n          }\r\n          this.isOrderTypeLoaded = true;\r\n          this.triggerFetchOrders();\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching order types:', error);\r\n          this.isOrderTypeLoaded = true;\r\n          this.triggerFetchOrders();\r\n        },\r\n      });\r\n  }\r\n\r\n  getPartnerAddress(bp_id: string, callback?: (address: string) => void) {\r\n    this.accountservice.fetchPartnerById(bp_id).subscribe({\r\n      next: (value: any) => {\r\n        const formattedAddresses =\r\n          value?.data.map((account: any) => {\r\n            const defaultAddress = account?.address_usages?.find(\r\n              (usage: any) => usage?.address_usage === 'XXDEFAULT'\r\n            )?.business_partner_address;\r\n\r\n            return {\r\n              ...account,\r\n              address: [\r\n                defaultAddress?.house_number || '-',\r\n                defaultAddress?.street_name || '-',\r\n                defaultAddress?.city_name || '-',\r\n                defaultAddress?.region || '-',\r\n                defaultAddress?.country || '-',\r\n                defaultAddress?.postal_code || '-',\r\n              ]\r\n                .filter((part) => part && part !== '-')\r\n                .join(', '),\r\n            };\r\n          }) || [];\r\n\r\n        this.address = formattedAddresses;\r\n\r\n        if (callback && this.address.length > 0) {\r\n          callback(this.address[0].address);\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error fetching partner address:', err);\r\n      },\r\n    });\r\n  }\r\n\r\n  getPartnerFunction(soldToParty: string) {\r\n    this.accountservice.getPartnerFunction(soldToParty).subscribe({\r\n      next: (value: any) => {\r\n        this.customer = value.find(\r\n          (o: any) =>\r\n            o.customer_id === soldToParty && o.partner_function === 'SH'\r\n        );\r\n        this.getPartnerAddress(\r\n          this.customer?.customer.bp_id,\r\n          (formattedAddress: string) => {\r\n            if (this.customer) {\r\n              this.customer.customer.address = formattedAddress;\r\n            }\r\n          }\r\n        );\r\n        this.isCustomerLoaded = true;\r\n        this.triggerFetchOrders();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error fetching partner function:', error);\r\n        this.isCustomerLoaded = true;\r\n        this.triggerFetchOrders();\r\n      },\r\n    });\r\n  }\r\n\r\n  triggerFetchOrders() {\r\n    if (this.isCustomerLoaded && this.isOrderTypeLoaded) {\r\n      this.fetchOrders(1000);\r\n    }\r\n  }\r\n\r\n  fetchOrders(count: number) {\r\n    this.salesloading = true;\r\n    const rawParams = {\r\n      SOLDTO: this.customer?.customer_id,\r\n      VKORG: this.customer?.sales_organization,\r\n      COUNT: count,\r\n      DOC_STATUS: Object.keys(this.orderValue).join(';'),\r\n      DOC_TYPE: this.orderType,\r\n    };\r\n\r\n    const params: any = Object.fromEntries(\r\n      Object.entries(rawParams).filter(\r\n        ([_, value]) => value !== undefined && value !== ''\r\n      )\r\n    );\r\n\r\n    this.accountservice.fetchOrders(params).subscribe({\r\n      next: (response) => {\r\n        if (response?.resultData && response.resultData.length > 0) {\r\n          this.OrderData = response.resultData.map((record) => ({\r\n            PURCH_NO: record?.PURCH_NO || '-',\r\n            SD_DOC: record?.SD_DOC || '-',\r\n            CHANNEL: record?.CHANNEL || '-',\r\n            DOC_TYPE: record?.DOC_TYPE || '-',\r\n            DOC_STATUS: record.DOC_STATUS\r\n              ? this.orderValue[record.DOC_STATUS]\r\n              : '-',\r\n            TXN_CURRENCY: record?.TXN_CURRENCY || '-',\r\n            DOC_DATE: record?.DOC_DATE\r\n              ? `${record.DOC_DATE.substring(0, 4)}-${record.DOC_DATE.substring(\r\n                  4,\r\n                  6\r\n                )}-${record.DOC_DATE.substring(6, 8)}`\r\n              : '-',\r\n            TOTAL_NET_AMOUNT: record?.TOTAL_NET_AMOUNT || '-',\r\n          }));\r\n\r\n          this.allData = [...this.OrderData];\r\n          this.totalRecords = this.allData.length;\r\n          this.paginateData();\r\n        } else {\r\n          this.allData = [];\r\n          this.totalRecords = 0;\r\n          this.paginateData();\r\n        }\r\n        this.salesloading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error fetching sales orders:', error);\r\n        this.salesloading = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  onPageChange(event: any) {\r\n    this.first = event.first;\r\n    this.rows = event.rows;\r\n    this.currentPage = this.first / this.rows + 1;\r\n\r\n    if (this.first + this.rows >= this.allData.length) {\r\n      this.fetchOrders(this.allData.length + 1000);\r\n    }\r\n    this.paginateData();\r\n  }\r\n\r\n  paginateData() {\r\n    this.OrderData = this.allData.slice(this.first, this.first + this.rows);\r\n  }\r\n\r\n  navigateToOrderDetail(order: any) {\r\n    this.router.navigate([order.SD_DOC], {\r\n      relativeTo: this.route,\r\n      state: { orderData: order, customerData: this.customer?.customer },\r\n    });\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Sales Orders</h4>\r\n        <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n            [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" />\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <div class=\"flex justify-content-center align-items-center w-full my-4\" *ngIf=\"salesloading\">\r\n            <p-progressSpinner></p-progressSpinner>\r\n        </div>\r\n        <p-table #dt1 *ngIf=\"!salesloading\" [value]=\"OrderData\" dataKey=\"id\" [rows]=\"10\" [rowHover]=\"true\"\r\n            [paginator]=\"true\" [totalRecords]=\"totalRecords\" responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th class=\"border-round-left-lg\" pSortableColumn=\"SD_DOC\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Order #\r\n                            <p-sortIcon field=\"SD_DOC\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"PURCH_NO\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            P.O. #\r\n                            <p-sortIcon field=\"PURCH_NO\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"DOC_DATE\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Date Placed\r\n                            <p-sortIcon field=\"DOC_DATE\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"DOC_STATUS\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Order Status\r\n                            <p-sortIcon field=\"DOC_STATUS\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"TOTAL_NET_AMOUNT\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Net Amount\r\n                            <p-sortIcon field=\"TOTAL_NET_AMOUNT\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"TXN_CURRENCY\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Currency\r\n                            <p-sortIcon field=\"TXN_CURRENCY\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                    <th pSortableColumn=\"CHANNEL\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            Channel\r\n                            <p-sortIcon field=\"CHANNEL\"></p-sortIcon>\r\n                        </div>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-order>\r\n                <tr (click)=\"navigateToOrderDetail(order)\" class=\"cursor-pointer\">\r\n                    <td class=\"text-orange-600 cursor-pointer font-medium underline border-round-left-lg\">\r\n                        {{ order?.SD_DOC }}\r\n                    </td>\r\n                    <td>\r\n                        {{ order?.PURCH_NO }}\r\n                    </td>\r\n                    <td>\r\n                        {{ order?.DOC_DATE | date : \"MM/dd/yyyy\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ order?.DOC_STATUS }}\r\n                    </td>\r\n                    <td>\r\n                        {{ order?.TOTAL_NET_AMOUNT }}\r\n                    </td>\r\n                    <td>\r\n                        {{ order?.TXN_CURRENCY }}\r\n                    </td>\r\n                    <td>\r\n                        {{ order?.CHANNEL }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"7\" class=\"border-round-left-lg\">No orders found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"7\" class=\"border-round-left-lg\">Loading orders data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;ICMjCC,EAAA,CAAAC,cAAA,aAA6F;IACzFD,EAAA,CAAAE,SAAA,wBAAuC;IAC3CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMUH,EAFR,CAAAC,cAAA,SAAI,aAC0D,cACX;IACvCD,EAAA,CAAAI,MAAA,gBACA;IAAAJ,EAAA,CAAAE,SAAA,qBAAwC;IAEhDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAA+B,cACgB;IACvCD,EAAA,CAAAI,MAAA,eACA;IAAAJ,EAAA,CAAAE,SAAA,qBAA0C;IAElDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,aAA+B,eACgB;IACvCD,EAAA,CAAAI,MAAA,qBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA0C;IAElDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAiC,eACc;IACvCD,EAAA,CAAAI,MAAA,sBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA4C;IAEpDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAuC,eACQ;IACvCD,EAAA,CAAAI,MAAA,oBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAAkD;IAE1DF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAmC,eACY;IACvCD,EAAA,CAAAI,MAAA,kBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAA8C;IAEtDF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAA8B,eACiB;IACvCD,EAAA,CAAAI,MAAA,iBACA;IAAAJ,EAAA,CAAAE,SAAA,sBAAyC;IAGrDF,EAFQ,CAAAG,YAAA,EAAM,EACL,EACJ;;;;;;IAILH,EAAA,CAAAC,cAAA,aAAkE;IAA9DD,EAAA,CAAAK,UAAA,mBAAAC,iFAAA;MAAA,MAAAC,QAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,qBAAA,CAAAP,QAAA,CAA4B;IAAA,EAAC;IACtCP,EAAA,CAAAC,cAAA,aAAsF;IAClFD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAI,MAAA,IACJ;IACJJ,EADI,CAAAG,YAAA,EAAK,EACJ;;;;IApBGH,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAgB,kBAAA,MAAAT,QAAA,kBAAAA,QAAA,CAAAU,MAAA,MACJ;IAEIjB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAgB,kBAAA,MAAAT,QAAA,kBAAAA,QAAA,CAAAW,QAAA,MACJ;IAEIlB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAgB,kBAAA,MAAAhB,EAAA,CAAAmB,WAAA,OAAAZ,QAAA,kBAAAA,QAAA,CAAAa,QAAA,qBACJ;IAEIpB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAgB,kBAAA,MAAAT,QAAA,kBAAAA,QAAA,CAAAc,UAAA,MACJ;IAEIrB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAgB,kBAAA,MAAAT,QAAA,kBAAAA,QAAA,CAAAe,gBAAA,MACJ;IAEItB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAgB,kBAAA,MAAAT,QAAA,kBAAAA,QAAA,CAAAgB,YAAA,MACJ;IAEIvB,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAAgB,kBAAA,MAAAT,QAAA,kBAAAA,QAAA,CAAAiB,OAAA,MACJ;;;;;IAKAxB,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAI,MAAA,uBAAgB;IACjEJ,EADiE,CAAAG,YAAA,EAAK,EACjE;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aAC6C;IAAAD,EAAA,CAAAI,MAAA,wCAAiC;IAClFJ,EADkF,CAAAG,YAAA,EAAK,EAClF;;;;;IAlFbH,EAAA,CAAAC,cAAA,oBAC+E;IA8E3ED,EA7EA,CAAAyB,UAAA,IAAAC,4DAAA,2BAAgC,IAAAC,4DAAA,4BA+CQ,IAAAC,4DAAA,0BAyBF,IAAAC,4DAAA,0BAKD;IAKzC7B,EAAA,CAAAG,YAAA,EAAU;;;;IAnFaH,EADa,CAAA8B,UAAA,UAAAnB,MAAA,CAAAoB,SAAA,CAAmB,YAAyB,kBAAkB,mBAC5E,iBAAApB,MAAA,CAAAqB,YAAA,CAA8B;;;ADU5D,OAAM,MAAOC,2BAA2B;EAmBtCC,YACUC,cAA8B,EAC9BC,MAAc,EACdC,KAAqB;IAFrB,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IArBP,KAAAC,YAAY,GAAG,IAAIxC,OAAO,EAAQ;IACnC,KAAAkC,YAAY,GAAW,CAAC;IACxB,KAAAO,YAAY,GAAY,IAAI;IAC5B,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,kBAAkB,GAAQ,EAAE;IAC5B,KAAAC,UAAU,GAAQ,EAAE;IACpB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,KAAK,GAAW,CAAC;IACjB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,OAAO,GAAuB,EAAE;IAChC,KAAAhB,SAAS,GAAuB,EAAE;IAClC,KAAAiB,QAAQ,GAAQ,EAAE;IACjB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IAC1B,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAC,OAAO,GAAU,EAAE;EAMvB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAAClB,cAAc,CAACmB,OAAO,CACxBC,IAAI,CAACxD,SAAS,CAAC,IAAI,CAACuC,YAAY,CAAC,CAAC,CAClCkB,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACC,kBAAkB,CAACD,QAAQ,CAACT,QAAQ,CAACW,WAAW,CAAC;MACxD;IACF,CAAC,CAAC;IAEJ,IAAI,CAACxB,cAAc,CAChByB,kBAAkB,CAAC;MAClB,oBAAoB,EAAE;KACvB,CAAC,CACDJ,SAAS,CAAC;MACTK,IAAI,EAAGJ,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAEK,IAAI,CAACC,MAAM,EAAE;UACzB,IAAI,CAACvB,aAAa,GAAGiB,QAAQ,CAACK,IAAI,CAACE,GAAG,CAAEC,GAAQ,IAAI;YAClD,IAAI,CAACxB,kBAAkB,CAACwB,GAAG,CAACC,WAAW,CAAC,GAAGD,GAAG,CAACE,IAAI;YACnD,IAAI,CAACzB,UAAU,CAACuB,GAAG,CAACE,IAAI,CAAC,GAAGF,GAAG,CAACC,WAAW;YAC3C,OAAOD,GAAG,CAACC,WAAW;UACxB,CAAC,CAAC;UACF,IAAI,CAAC1B,aAAa,GAAG,CAAC,KAAK,EAAE,GAAG,IAAI,CAACA,aAAa,CAAC;QACrD;MACF,CAAC;MACD4B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;KACD,CAAC;IAEJ,IAAI,CAACjC,cAAc,CAChByB,kBAAkB,CAAC;MAClB,oBAAoB,EAAE;KACvB,CAAC,CACDJ,SAAS,CAAC;MACTK,IAAI,EAAGJ,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAEK,IAAI,CAACC,MAAM,EAAE;UACzB,IAAI,CAACpB,SAAS,GAAGc,QAAQ,CAACK,IAAI,CAC3BE,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACE,IAAI,CAAC,CAC3BG,IAAI,CAAC,GAAG,CAAC;QACd;QACA,IAAI,CAACpB,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACqB,kBAAkB,EAAE;MAC3B,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAAClB,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACqB,kBAAkB,EAAE;MAC3B;KACD,CAAC;EACN;EAEAC,iBAAiBA,CAACC,KAAa,EAAEC,QAAoC;IACnE,IAAI,CAACvC,cAAc,CAACwC,gBAAgB,CAACF,KAAK,CAAC,CAACjB,SAAS,CAAC;MACpDK,IAAI,EAAGe,KAAU,IAAI;QACnB,MAAMC,kBAAkB,GACtBD,KAAK,EAAEd,IAAI,CAACE,GAAG,CAAEV,OAAY,IAAI;UAC/B,MAAMwB,cAAc,GAAGxB,OAAO,EAAEyB,cAAc,EAAEC,IAAI,CACjDC,KAAU,IAAKA,KAAK,EAAEC,aAAa,KAAK,WAAW,CACrD,EAAEC,wBAAwB;UAE3B,OAAO;YACL,GAAG7B,OAAO;YACVF,OAAO,EAAE,CACP0B,cAAc,EAAEM,YAAY,IAAI,GAAG,EACnCN,cAAc,EAAEO,WAAW,IAAI,GAAG,EAClCP,cAAc,EAAEQ,SAAS,IAAI,GAAG,EAChCR,cAAc,EAAES,MAAM,IAAI,GAAG,EAC7BT,cAAc,EAAEU,OAAO,IAAI,GAAG,EAC9BV,cAAc,EAAEW,WAAW,IAAI,GAAG,CACnC,CACEC,MAAM,CAAEC,IAAI,IAAKA,IAAI,IAAIA,IAAI,KAAK,GAAG,CAAC,CACtCrB,IAAI,CAAC,IAAI;WACb;QACH,CAAC,CAAC,IAAI,EAAE;QAEV,IAAI,CAAClB,OAAO,GAAGyB,kBAAkB;QAEjC,IAAIH,QAAQ,IAAI,IAAI,CAACtB,OAAO,CAACW,MAAM,GAAG,CAAC,EAAE;UACvCW,QAAQ,CAAC,IAAI,CAACtB,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAAC;QACnC;MACF,CAAC;MACDgB,KAAK,EAAGwB,GAAG,IAAI;QACbvB,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEwB,GAAG,CAAC;MACvD;KACD,CAAC;EACJ;EAEAlC,kBAAkBA,CAACmC,WAAmB;IACpC,IAAI,CAAC1D,cAAc,CAACuB,kBAAkB,CAACmC,WAAW,CAAC,CAACrC,SAAS,CAAC;MAC5DK,IAAI,EAAGe,KAAU,IAAI;QACnB,IAAI,CAAC5B,QAAQ,GAAG4B,KAAK,CAACI,IAAI,CACvBc,CAAM,IACLA,CAAC,CAACnC,WAAW,KAAKkC,WAAW,IAAIC,CAAC,CAACC,gBAAgB,KAAK,IAAI,CAC/D;QACD,IAAI,CAACvB,iBAAiB,CACpB,IAAI,CAACxB,QAAQ,EAAEA,QAAQ,CAACyB,KAAK,EAC5BuB,gBAAwB,IAAI;UAC3B,IAAI,IAAI,CAAChD,QAAQ,EAAE;YACjB,IAAI,CAACA,QAAQ,CAACA,QAAQ,CAACI,OAAO,GAAG4C,gBAAgB;UACnD;QACF,CAAC,CACF;QACD,IAAI,CAAC/C,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACsB,kBAAkB,EAAE;MAC3B,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACnB,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACsB,kBAAkB,EAAE;MAC3B;KACD,CAAC;EACJ;EAEAA,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACtB,gBAAgB,IAAI,IAAI,CAACC,iBAAiB,EAAE;MACnD,IAAI,CAAC+C,WAAW,CAAC,IAAI,CAAC;IACxB;EACF;EAEAA,WAAWA,CAACC,KAAa;IACvB,IAAI,CAAC3D,YAAY,GAAG,IAAI;IACxB,MAAM4D,SAAS,GAAG;MAChBC,MAAM,EAAE,IAAI,CAACpD,QAAQ,EAAEW,WAAW;MAClC0C,KAAK,EAAE,IAAI,CAACrD,QAAQ,EAAEsD,kBAAkB;MACxCC,KAAK,EAAEL,KAAK;MACZ7E,UAAU,EAAEmF,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/D,UAAU,CAAC,CAAC4B,IAAI,CAAC,GAAG,CAAC;MAClDoC,QAAQ,EAAE,IAAI,CAAC/D;KAChB;IAED,MAAMgE,MAAM,GAAQH,MAAM,CAACI,WAAW,CACpCJ,MAAM,CAACK,OAAO,CAACV,SAAS,CAAC,CAACT,MAAM,CAC9B,CAAC,CAACoB,CAAC,EAAElC,KAAK,CAAC,KAAKA,KAAK,KAAKmC,SAAS,IAAInC,KAAK,KAAK,EAAE,CACpD,CACF;IAED,IAAI,CAACzC,cAAc,CAAC8D,WAAW,CAACU,MAAM,CAAC,CAACnD,SAAS,CAAC;MAChDK,IAAI,EAAGJ,QAAQ,IAAI;QACjB,IAAIA,QAAQ,EAAEuD,UAAU,IAAIvD,QAAQ,CAACuD,UAAU,CAACjD,MAAM,GAAG,CAAC,EAAE;UAC1D,IAAI,CAAChC,SAAS,GAAG0B,QAAQ,CAACuD,UAAU,CAAChD,GAAG,CAAEiD,MAAM,KAAM;YACpD/F,QAAQ,EAAE+F,MAAM,EAAE/F,QAAQ,IAAI,GAAG;YACjCD,MAAM,EAAEgG,MAAM,EAAEhG,MAAM,IAAI,GAAG;YAC7BO,OAAO,EAAEyF,MAAM,EAAEzF,OAAO,IAAI,GAAG;YAC/BkF,QAAQ,EAAEO,MAAM,EAAEP,QAAQ,IAAI,GAAG;YACjCrF,UAAU,EAAE4F,MAAM,CAAC5F,UAAU,GACzB,IAAI,CAACqB,UAAU,CAACuE,MAAM,CAAC5F,UAAU,CAAC,GAClC,GAAG;YACPE,YAAY,EAAE0F,MAAM,EAAE1F,YAAY,IAAI,GAAG;YACzCH,QAAQ,EAAE6F,MAAM,EAAE7F,QAAQ,GACtB,GAAG6F,MAAM,CAAC7F,QAAQ,CAAC8F,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAID,MAAM,CAAC7F,QAAQ,CAAC8F,SAAS,CAC7D,CAAC,EACD,CAAC,CACF,IAAID,MAAM,CAAC7F,QAAQ,CAAC8F,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GACtC,GAAG;YACP5F,gBAAgB,EAAE2F,MAAM,EAAE3F,gBAAgB,IAAI;WAC/C,CAAC,CAAC;UAEH,IAAI,CAACyB,OAAO,GAAG,CAAC,GAAG,IAAI,CAAChB,SAAS,CAAC;UAClC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACe,OAAO,CAACgB,MAAM;UACvC,IAAI,CAACoD,YAAY,EAAE;QACrB,CAAC,MAAM;UACL,IAAI,CAACpE,OAAO,GAAG,EAAE;UACjB,IAAI,CAACf,YAAY,GAAG,CAAC;UACrB,IAAI,CAACmF,YAAY,EAAE;QACrB;QACA,IAAI,CAAC5E,YAAY,GAAG,KAAK;MAC3B,CAAC;MACD6B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,IAAI,CAAC7B,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACJ;EAEA6E,YAAYA,CAACC,KAAU;IACrB,IAAI,CAACzE,KAAK,GAAGyE,KAAK,CAACzE,KAAK;IACxB,IAAI,CAACC,IAAI,GAAGwE,KAAK,CAACxE,IAAI;IACtB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACF,KAAK,GAAG,IAAI,CAACC,IAAI,GAAG,CAAC;IAE7C,IAAI,IAAI,CAACD,KAAK,GAAG,IAAI,CAACC,IAAI,IAAI,IAAI,CAACE,OAAO,CAACgB,MAAM,EAAE;MACjD,IAAI,CAACkC,WAAW,CAAC,IAAI,CAAClD,OAAO,CAACgB,MAAM,GAAG,IAAI,CAAC;IAC9C;IACA,IAAI,CAACoD,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACpF,SAAS,GAAG,IAAI,CAACgB,OAAO,CAACuE,KAAK,CAAC,IAAI,CAAC1E,KAAK,EAAE,IAAI,CAACA,KAAK,GAAG,IAAI,CAACC,IAAI,CAAC;EACzE;EAEA/B,qBAAqBA,CAACyG,KAAU;IAC9B,IAAI,CAACnF,MAAM,CAACoF,QAAQ,CAAC,CAACD,KAAK,CAACtG,MAAM,CAAC,EAAE;MACnCwG,UAAU,EAAE,IAAI,CAACpF,KAAK;MACtBqF,KAAK,EAAE;QAAEC,SAAS,EAAEJ,KAAK;QAAEK,YAAY,EAAE,IAAI,CAAC5E,QAAQ,EAAEA;MAAQ;KACjE,CAAC;EACJ;;;uBA1NWf,2BAA2B,EAAAjC,EAAA,CAAA6H,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA/H,EAAA,CAAA6H,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAjI,EAAA,CAAA6H,iBAAA,CAAAG,EAAA,CAAAE,cAAA;IAAA;EAAA;;;YAA3BjG,2BAA2B;MAAAkG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpBhCzI,EAFR,CAAAC,cAAA,aAAuD,aACgC,YAChC;UAAAD,EAAA,CAAAI,MAAA,mBAAY;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAChEH,EAAA,CAAAE,SAAA,kBAC2D;UAC/DF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,aAAuB;UAInBD,EAHA,CAAAyB,UAAA,IAAAkH,0CAAA,iBAA6F,IAAAC,8CAAA,qBAId;UAqFvF5I,EADI,CAAAG,YAAA,EAAM,EACJ;;;UA7FMH,EAAA,CAAAe,SAAA,GAAmC;UAACf,EAApC,CAAA8B,UAAA,oCAAmC,iBAAiB;UAIiB9B,EAAA,CAAAe,SAAA,GAAkB;UAAlBf,EAAA,CAAA8B,UAAA,SAAA4G,GAAA,CAAAnG,YAAA,CAAkB;UAG5EvC,EAAA,CAAAe,SAAA,EAAmB;UAAnBf,EAAA,CAAA8B,UAAA,UAAA4G,GAAA,CAAAnG,YAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}