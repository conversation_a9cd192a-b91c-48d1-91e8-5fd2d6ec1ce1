<div class="p-3 w-full surface-card border-round shadow-1">
    <div class="card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2">
        <h4 class="m-0 pl-3 left-border relative flex">Functions</h4>
        <div class="flex gap-3 ml-auto align-items-center">
            <p-button label="Add New" (click)="showNewDialog('right')" icon="pi pi-plus-circle" iconPos="right"
                class="ml-auto" [styleClass]="'font-semibold px-3'" [rounded]="true" />

            <p-multiSelect [options]="cols" [(ngModel)]="selectedColumns" optionLabel="header"
                class="table-multiselect-dropdown" [styleClass]="
          'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'
        ">
            </p-multiSelect>
        </div>
    </div>

    <div class="table-sec">
        <p-table [value]="functionDetails" [(selection)]="selectedFunctions" dataKey="id" [rows]="14" [paginator]="true"
            [lazy]="true" responsiveLayout="scroll" [scrollable]="true" class="scrollable-table"
            [reorderableColumns]="true" (onColReorder)="onColumnReorder($event)">
            <ng-template pTemplate="header">
                <tr>
                    <th pFrozenColumn class="border-round-left-lg pl-3 w-2rem table-checkbox text-center">
                        <p-tableHeaderCheckbox />
                    </th>
                    <th pFrozenColumn (click)="customSort('start_date')">
                        <div class="flex align-items-center cursor-pointer">
                            Valid From
                            <i *ngIf="sortField === 'start_date'" class="ml-2 pi" [ngClass]="
                  sortOrder === 1
                    ? 'pi-sort-amount-up-alt'
                    : 'pi-sort-amount-down'
                ">
                            </i>
                            <i *ngIf="sortField !== 'start_date'" class="ml-2 pi pi-sort"></i>
                        </div>
                    </th>
                    <ng-container *ngFor="let col of selectedColumns">
                        <th *ngIf="col.field !== 'company_indicator' && col.field !== 'sales_indicator' && col.field !== 'sales_organisation_indicator' && col.field !== 'service_indicator' && col.field !== 'service_organisation_indicator' && col.field !== 'marketing_indicator' && col.field !== 'reporting_line_indicator'; else nonSortableColumn"
                            [pSortableColumn]="col.field" pReorderableColumn (click)="customSort(col.field)">
                            <div class="flex align-items-center cursor-pointer">
                                {{ col.header }}
                                <i *ngIf="sortField === col.field" class="ml-2 pi"
                                    [ngClass]="sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                                <i *ngIf="sortField !== col.field" class="ml-2 pi pi-sort"></i>
                            </div>
                        </th>

                        <ng-template #nonSortableColumn>
                            <th pReorderableColumn>
                                <div class="flex align-items-center">
                                    {{ col.header }}
                                </div>
                            </th>
                        </ng-template>
                    </ng-container>
                    <th>
                        <div class="flex align-items-center">Actions</div>
                    </th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-function let-columns="columns">
                <tr class="cursor-pointer">
                    <td pFrozenColumn class="border-round-left-lg pl-3 w-2rem text-center">
                        <p-tableCheckbox [value]="function" />
                    </td>
                    <td pFrozenColumn class="font-medium">
                        {{ function?.start_date ? (function.start_date | date: 'dd/MM/yyyy') : '-' }}
                    </td>

                    <ng-container *ngFor="let col of selectedColumns">
                        <td>
                            <ng-container [ngSwitch]="col.field">
                                <ng-container *ngSwitchCase="'end_date'">
                                    {{ function?.end_date ? (function.end_date | date: 'dd/MM/yyyy') : '-' }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'company_indicator'">
                                    <p-checkbox [binary]="true" [disabled]="true"
                                        [ngModel]="function?.company_indicator"></p-checkbox>
                                </ng-container>

                                <ng-container *ngSwitchCase="'sales_indicator'">
                                    <p-checkbox [binary]="true" [disabled]="true"
                                        [ngModel]="function?.sales_indicator"></p-checkbox>
                                </ng-container>

                                <ng-container *ngSwitchCase="'sales_organisation_indicator'">
                                    <p-checkbox [binary]="true" [disabled]="true"
                                        [ngModel]="function?.sales_organisation_indicator"></p-checkbox>
                                </ng-container>
                                <ng-container *ngSwitchCase="'service_indicator'">
                                    <p-checkbox [binary]="true" [disabled]="true"
                                        [ngModel]="function?.service_indicator"></p-checkbox>
                                </ng-container>
                                <ng-container *ngSwitchCase="'service_organisation_indicator'">
                                    <p-checkbox [binary]="true" [disabled]="true"
                                        [ngModel]="function?.service_organisation_indicator"></p-checkbox>
                                </ng-container>
                                <ng-container *ngSwitchCase="'marketing_indicator'">
                                    <p-checkbox [binary]="true" [disabled]="true"
                                        [ngModel]="function?.marketing_indicator"></p-checkbox>
                                </ng-container>
                                <ng-container *ngSwitchCase="'reporting_line_indicator'">
                                    <p-checkbox [binary]="true" [disabled]="true"
                                        [ngModel]="function?.reporting_line_indicator"></p-checkbox>
                                </ng-container>
                                <ng-container *ngSwitchCase="'currency_code'">
                                    {{ function?.currency_code || '-' }}
                                </ng-container>
                            </ng-container>
                        </td>
                    </ng-container>

                    <td>
                        <div class="flex align-items-center">
                            <button pButton type="button" class="mr-2" icon="pi pi-pencil" pTooltip="Edit"
                                (click)="editFunction(function)"></button>
                            <button pButton type="button" icon="pi pi-trash" pTooltip="Delete"
                                (click)="$event.stopPropagation(); confirmRemove(function)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td class="border-round-left-lg" colspan="13">No functions found.</td>
                </tr>
            </ng-template>
            <ng-template pTemplate="loadingbody">
                <tr>
                    <td colspan="13" class="border-round-left-lg">
                        Loading functions data. Please wait...
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>
<p-dialog [modal]="true" [(visible)]="addDialogVisible" [style]="{ width: '45rem' }" [position]="'right'"
    [draggable]="false" class="opportunity-contact-popup">
    <ng-template pTemplate="header">
        <h4>Functions</h4>
    </ng-template>

    <form [formGroup]="FunctionForm" class="relative flex flex-column gap-1">
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Valid From">
                <span class="material-symbols-rounded">badge</span>Valid From
                <span class="text-red-500">*</span>
            </label>
            <div class="form-input flex-1 relative">
                <p-calendar formControlName="start_date" inputId="calendar-12h" hourFormat="12" [showIcon]="true"
                    styleClass="h-3rem w-full" placeholder="Valid From" appendTo="body"
                    [ngClass]="{ 'is-invalid': submitted && f['start_date'].errors }" />
                <div *ngIf="submitted && f['start_date'].errors" class="p-error">
                    <div *ngIf="
              submitted &&
              f['start_date'].errors &&
              f['start_date'].errors['required']
            ">
                        Valid From is required.
                    </div>
                </div>
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Valid To">
                <span class="material-symbols-rounded">badge</span>Valid To
                <span class="text-red-500">*</span>
            </label>
            <div class="form-input flex-1 relative">
                <p-calendar formControlName="end_date" inputId="calendar-12h" hourFormat="12" [showIcon]="true"
                    styleClass="h-3rem w-full" placeholder="Valid To" appendTo="body"
                    [ngClass]="{ 'is-invalid': submitted && f['end_date'].errors }" />
                <div *ngIf="submitted && f['end_date'].errors" class="p-error">
                    <div *ngIf="
              submitted &&
              f['end_date'].errors &&
              f['end_date'].errors['required']
            ">
                        Valid To is required.
                    </div>
                </div>
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Company">
                <span class="material-symbols-rounded">person</span>Company
            </label>
            <div class="form-input flex-1 relative">
                <p-toggleButton formControlName="company_indicator" onLabel="On" offLabel="Off"
                    styleClass="h-3rem w-full" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Sales">
                <span class="material-symbols-rounded">business</span>Sales
            </label>
            <div class="form-input flex-1 relative">
                <p-toggleButton formControlName="sales_indicator" onLabel="On" offLabel="Off"
                    styleClass="h-3rem w-full" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Sales Organization">
                <span class="material-symbols-rounded">business</span>Sales Organization
            </label>
            <div class="form-input flex-1 relative">
                <p-toggleButton formControlName="sales_organisation_indicator" onLabel="On" offLabel="Off"
                    styleClass="h-3rem w-full" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Sales Office">
                <span class="material-symbols-rounded">store</span>Sales Office
            </label>
            <div class="form-input flex-1 relative">
                <p-toggleButton formControlName="sales_office_indicator" onLabel="On" offLabel="Off"
                    styleClass="h-3rem w-full" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Sales Group">
                <span class="material-symbols-rounded">groups</span>Sales Group
            </label>
            <div class="form-input flex-1 relative">
                <p-toggleButton formControlName="sales_group_indicator" onLabel="On" offLabel="Off"
                    styleClass="h-3rem w-full" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Service">
                <span class="material-symbols-rounded">build</span>Service
            </label>
            <div class="form-input flex-1 relative">
                <p-toggleButton formControlName="service_indicator" onLabel="On" offLabel="Off"
                    styleClass="h-3rem w-full" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Service Organization">
                <span class="material-symbols-rounded">build</span>Service Organization
            </label>
            <div class="form-input flex-1 relative">
                <p-toggleButton formControlName="service_organisation_indicator" onLabel="On" offLabel="Off"
                    styleClass="h-3rem w-full" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Marketing">
                <span class="material-symbols-rounded">campaign</span>Marketing
            </label>
            <div class="form-input flex-1 relative">
                <p-toggleButton formControlName="marketing_indicator" onLabel="On" offLabel="Off"
                    styleClass="h-3rem w-full" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Reporting Line">
                <span class="material-symbols-rounded">supervisor_account</span>Reporting Line
            </label>
            <div class="form-input flex-1 relative">
                <p-toggleButton formControlName="reporting_line_indicator" onLabel="On" offLabel="Off"
                    styleClass="h-3rem w-full" />
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Currency">
                <span class="material-symbols-rounded">attach_money</span>Currency
            </label>
            <div class="form-input flex-1 relative">
                <input pInputText type="text" id="currency_code" formControlName="currency_code" class="h-3rem w-full"
                    autocomplete="off" />
            </div>
        </div>
        <div class="flex align-items-center p-3 gap-3 mt-1">
            <button pButton type="button" label="Cancel"
                class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem"
                (click)="addDialogVisible = false"></button>
            <button pButton type="submit" label="Save" class="p-button-rounded justify-content-center w-9rem h-3rem"
                (click)="onSubmit()"></button>
        </div>
    </form>
</p-dialog>