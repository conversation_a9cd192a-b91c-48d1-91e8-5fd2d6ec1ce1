{"ast": null, "code": "import * as moment from 'moment';\nimport { Subject, takeUntil, fork<PERSON>oin } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../account.service\";\nimport * as i3 from \"../return-order.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"primeng/progressspinner\";\nfunction ReturnOrderDetailsComponent_ng_container_0_ng_template_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\", 16);\n    i0.ɵɵelementStart(2, \"th\", 17);\n    i0.ɵɵtext(3, \"Return Reason\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"th\", 17);\n    i0.ɵɵtext(5, \"Return Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 17);\n    i0.ɵɵtext(7, \"Quantity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 18);\n    i0.ɵɵtext(9, \"Price\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ReturnOrderDetailsComponent_ng_container_0_ng_template_65_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const reason_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", reason_r3.code);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", reason_r3.description, \" \");\n  }\n}\nfunction ReturnOrderDetailsComponent_ng_container_0_ng_template_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 19)(2, \"div\", 20)(3, \"div\", 21)(4, \"h5\", 22);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 23);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"td\", 24)(9, \"select\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReturnOrderDetailsComponent_ng_container_0_ng_template_65_Template_select_ngModelChange_9_listener($event) {\n      const returnOrderDetails_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(returnOrderDetails_r2.RETURN_REASON, $event) || (returnOrderDetails_r2.RETURN_REASON = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(10, \"option\", 26);\n    i0.ɵɵtext(11, \"Select Return Reason\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, ReturnOrderDetailsComponent_ng_container_0_ng_template_65_option_12_Template, 2, 2, \"option\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\", 24)(14, \"div\", 28)(15, \"label\")(16, \"input\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReturnOrderDetailsComponent_ng_container_0_ng_template_65_Template_input_ngModelChange_16_listener($event) {\n      const returnOrderDetails_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(returnOrderDetails_r2.REFUND_TYPE, $event) || (returnOrderDetails_r2.REFUND_TYPE = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18, \"Return & Refund\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"label\")(20, \"input\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReturnOrderDetailsComponent_ng_container_0_ng_template_65_Template_input_ngModelChange_20_listener($event) {\n      const returnOrderDetails_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(returnOrderDetails_r2.REFUND_TYPE, $event) || (returnOrderDetails_r2.REFUND_TYPE = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \"Replace Item \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"td\", 24)(24, \"p\", 31);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"td\", 32)(27, \"p\", 33);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"p\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const returnOrderDetails_r2 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"width\", \"60%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(returnOrderDetails_r2.SHORT_TEXT);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", returnOrderDetails_r2.MATERIAL, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", returnOrderDetails_r2.RETURN_REASON);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.returnReason);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", returnOrderDetails_r2.REFUND_TYPE);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", returnOrderDetails_r2.REFUND_TYPE);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", returnOrderDetails_r2.REQ_QTY, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(29, 9, returnOrderDetails_r2 == null ? null : returnOrderDetails_r2.NET_AMOUNT, returnOrderDetails_r2 == null ? null : returnOrderDetails_r2.TXN_CURRENCY), \" \");\n  }\n}\nfunction ReturnOrderDetailsComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 2)(2, \"h4\", 3);\n    i0.ɵɵtext(3, \"Return Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 4)(5, \"li\", 5)(6, \"div\", 6)(7, \"i\", 7);\n    i0.ɵɵtext(8, \"tag\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 8)(10, \"h6\", 9);\n    i0.ɵɵtext(11, \"Return Order #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 10);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"li\", 5)(15, \"div\", 6)(16, \"i\", 7);\n    i0.ɵɵtext(17, \"person\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 8)(19, \"h6\", 9);\n    i0.ɵɵtext(20, \"Customer Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\", 10);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"li\", 5)(24, \"div\", 6)(25, \"i\", 7);\n    i0.ɵɵtext(26, \"event_note\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 8)(28, \"h6\", 9);\n    i0.ɵɵtext(29, \"Customer # \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\", 10);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"li\", 5)(33, \"div\", 6)(34, \"i\", 7);\n    i0.ɵɵtext(35, \"shopping_bag\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 8)(37, \"h6\", 9);\n    i0.ɵɵtext(38, \"Ref. Sales Order #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"p\", 10);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"li\", 5)(42, \"div\", 6)(43, \"i\", 7);\n    i0.ɵɵtext(44, \"event\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 8)(46, \"h6\", 9);\n    i0.ɵɵtext(47, \"Date Placed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"p\", 10);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(50, \"li\", 5)(51, \"div\", 6)(52, \"i\", 7);\n    i0.ɵɵtext(53, \"event\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 8)(55, \"h6\", 9);\n    i0.ɵɵtext(56, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"p\", 10);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(59, \"div\", 2)(60, \"h5\", 11);\n    i0.ɵɵtext(61, \"Items\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"div\", 12)(63, \"p-table\", 13);\n    i0.ɵɵtemplate(64, ReturnOrderDetailsComponent_ng_container_0_ng_template_64_Template, 10, 0, \"ng-template\", 14)(65, ReturnOrderDetailsComponent_ng_container_0_ng_template_65_Template, 31, 12, \"ng-template\", 15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r3.returnOrderDetail == null ? null : ctx_r3.returnOrderDetail.RETURN_ORDER_HDR == null ? null : ctx_r3.returnOrderDetail.RETURN_ORDER_HDR.DOC_NUMBER);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r3.sellerDetails == null ? null : ctx_r3.sellerDetails.customer == null ? null : ctx_r3.sellerDetails.customer.customer_full_name);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r3.sellerDetails == null ? null : ctx_r3.sellerDetails.bp_customer_number);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r3.returnOrderDetail == null ? null : ctx_r3.returnOrderDetail.RETURN_ORDER_HDR == null ? null : ctx_r3.returnOrderDetail.RETURN_ORDER_HDR.REF_ORDER);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.moment(ctx_r3.returnOrderDetail == null ? null : ctx_r3.returnOrderDetail.RETURN_ORDER_HDR == null ? null : ctx_r3.returnOrderDetail.RETURN_ORDER_HDR.DOC_DATE).format(\"MM/DD/YYYY\"), \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getStatusName(ctx_r3.returnOrderDetail == null ? null : ctx_r3.returnOrderDetail.RETURN_ORDER_HDR == null ? null : ctx_r3.returnOrderDetail.RETURN_ORDER_HDR.DOC_STAT), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", ctx_r3.returnOrderDetail.RETURN_ORDER_LINE_DETAIL);\n  }\n}\nfunction ReturnOrderDetailsComponent_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 35);\n    i0.ɵɵelement(1, \"p-progressSpinner\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nexport let ReturnOrderDetailsComponent = /*#__PURE__*/(() => {\n  class ReturnOrderDetailsComponent {\n    constructor(activatedRoute, service, productReturnService) {\n      this.activatedRoute = activatedRoute;\n      this.service = service;\n      this.productReturnService = productReturnService;\n      this.unsubscribe$ = new Subject();\n      this.items = [{\n        label: 'Return Order Details',\n        routerLink: ['/store/return-order-details']\n      }];\n      this.home = {\n        icon: 'pi pi-home',\n        routerLink: ['/']\n      };\n      this.ngUnsubscribe = new Subject();\n      this.moment = moment;\n      this.loading = false;\n      this.returnOrderId = null;\n      this.refDocId = null;\n      this.returnOrderDetail = null;\n      this.sellerDetails = {};\n      this.statuses = [];\n      this.returnReason = [];\n    }\n    ngOnInit() {\n      this.service.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (response) {\n          this.loadInitialData(response.customer.customer_id);\n        }\n      });\n      this.getReasonReturn();\n      this.activatedRoute.paramMap.subscribe(params => {\n        const id = params.get(\"returnOrderId\");\n        const refDocId = params.get(\"refDocId\");\n        if (id && refDocId) {\n          this.returnOrderId = id;\n          this.refDocId = refDocId;\n          this.loading = true;\n          this.getOrderDetails();\n        }\n      });\n    }\n    loadInitialData(soldToParty) {\n      forkJoin({\n        partnerFunction: this.service.getPartnerFunction(soldToParty)\n      }).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: ({\n          partnerFunction\n        }) => {\n          this.sellerDetails = partnerFunction.find(o => o.customer_id === soldToParty && o.partner_function === 'SH');\n        },\n        error: error => {\n          console.error('Error fetching initial data:', error);\n        }\n      });\n    }\n    getReasonReturn() {\n      this.productReturnService.getAllReturnReason().pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n        next: res => {\n          this.returnReason = res?.data || [];\n        }\n      });\n    }\n    getOrderDetails() {\n      const payload = {\n        SD_DOC: this.returnOrderId,\n        DOC_TYPE: \"CBAR\",\n        REF_SD_DOC: this.refDocId\n      };\n      const status$ = this.productReturnService.getAllStatus();\n      const details$ = this.productReturnService.getRetrunOrderDetails(payload);\n      forkJoin([status$, details$]).pipe(takeUntil(this.ngUnsubscribe)).subscribe({\n        next: result => {\n          this.statuses = result[0]?.data || [];\n          this.returnOrderDetail = result[1]?.RETURNORDER || null;\n          this.loading = false;\n        },\n        error: () => {\n          this.loading = false;\n        }\n      });\n    }\n    getStatusName(code) {\n      const status = this.statuses.find(o => o.code === code);\n      if (status) {\n        return status.description;\n      }\n      return \"\";\n    }\n    ngOnDestroy() {\n      this.ngUnsubscribe.next();\n      this.ngUnsubscribe.complete();\n    }\n    static {\n      this.ɵfac = function ReturnOrderDetailsComponent_Factory(t) {\n        return new (t || ReturnOrderDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.AccountService), i0.ɵɵdirectiveInject(i3.ReturnOrderService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ReturnOrderDetailsComponent,\n        selectors: [[\"app-return-order-details\"]],\n        decls: 2,\n        vars: 2,\n        consts: [[4, \"ngIf\"], [\"class\", \"h-30rem flex justify-content-center align-items-center\", 4, \"ngIf\"], [1, \"card\", \"p-4\", \"shadow-1\"], [1, \"mb-4\", \"pb-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"text-primary\", \"surface-border\"], [1, \"order-details-list\", \"m-0\", \"my-3\", \"p-0\", \"d-grid\", \"gap-5\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"icon\", \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"border-circle\", \"bg-blue-200\"], [1, \"material-symbols-rounded\"], [1, \"text\"], [1, \"m-0\"], [1, \"m-0\", \"font-medium\", \"text-400\"], [1, \"mb-3\", \"uppercase\"], [1, \"border-round\", \"overflow-hidden\"], [3, \"value\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"surface-50\", \"px-4\", \"py-3\", \"text-700\", \"font-semibold\", \"uppercase\"], [1, \"surface-50\", \"py-3\", \"text-700\", \"font-semibold\", \"uppercase\"], [1, \"surface-50\", \"py-3\", \"px-4\", \"text-700\", \"font-semibold\", \"uppercase\", \"text-right\"], [1, \"px-0\", \"py-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", 3, \"width\"], [1, \"relative\", \"flex\", \"gap-3\"], [1, \"flex\", \"flex-column\"], [1, \"my-2\", \"text-lg\"], [1, \"m-0\", \"text-sm\", \"font-semibold\", \"text-color-secondary\"], [1, \"py-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", \"vertical-align-top\"], [\"disabled\", \"\", 1, \"form-control\", \"select-arrow-down\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"initiate-return-list\", \"flex-wrap\"], [\"type\", \"radio\", \"value\", \"\", \"disabled\", \"\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"radio\", \"value\", \"1\", \"disabled\", \"\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"m-0\", \"py-2\", \"font-semibold\", \"text-color-secondary\", \"border-1\", \"border-round\", \"surface-border\", \"text-center\"], [1, \"py-4\", \"px-4\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-50\", \"vertical-align-top\"], [1, \"m-0\", \"text-lg\", \"font-semibold\", \"text-right\"], [1, \"m-0\", \"font-semibold\", \"text-color-secondary\", \"text-right\"], [1, \"h-30rem\", \"flex\", \"justify-content-center\", \"align-items-center\"], [\"ariaLabel\", \"loading\"]],\n        template: function ReturnOrderDetailsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, ReturnOrderDetailsComponent_ng_container_0_Template, 66, 7, \"ng-container\", 0)(1, ReturnOrderDetailsComponent_section_1_Template, 2, 0, \"section\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i5.PrimeTemplate, i6.Table, i7.NgSelectOption, i7.ɵNgSelectMultipleOption, i7.DefaultValueAccessor, i7.SelectControlValueAccessor, i7.RadioControlValueAccessor, i7.NgControlStatus, i7.NgModel, i8.ProgressSpinner, i4.CurrencyPipe]\n      });\n    }\n  }\n  return ReturnOrderDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}