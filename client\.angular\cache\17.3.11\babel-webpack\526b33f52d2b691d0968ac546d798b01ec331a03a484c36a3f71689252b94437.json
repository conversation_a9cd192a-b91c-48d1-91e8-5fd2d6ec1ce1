{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SalesQuotesComponent } from './sales-quotes.component';\nimport { SalesQuotesDetailsComponent } from './sales-quotes-details/sales-quotes-details.component';\nimport { SalesQuotesOverviewComponent } from './sales-quotes-details/sales-quotes-overview/sales-quotes-overview.component';\nimport { SalesQuotesContactsComponent } from './sales-quotes-details/sales-quotes-contacts/sales-quotes-contacts.component';\nimport { SalesQuotePartnersComponent } from './sales-quotes-details/sales-quote-partners/sales-quote-partners.component';\nimport { SalesQuoteOpportunitiesComponent } from './sales-quotes-details/sales-quote-opportunities/sales-quote-opportunities.component';\nimport { SalesQuoteOrganizationDataComponent } from './sales-quotes-details/sales-quote-organization-data/sales-quote-organization-data.component';\nimport { SalesQuoteSalesTeamComponent } from './sales-quotes-details/sales-quote-sales-team/sales-quote-sales-team.component';\nimport { SalesQuoteAttachmentsComponent } from './sales-quotes-details/sales-quote-attachments/sales-quote-attachments.component';\nimport { SalesQuoteActivitiesComponent } from './sales-quotes-details/sales-quote-activities/sales-quote-activities.component';\nimport { SalesQuoteRelationshipsComponent } from './sales-quotes-details/sales-quote-relationships/sales-quote-relationships.component';\nimport { SalesQuoteTicketsComponent } from './sales-quotes-details/sales-quote-tickets/sales-quote-tickets.component';\nimport { SalesQuotesNotesComponent } from './sales-quotes-details/sales-quotes-notes/sales-quotes-notes.component';\nimport { SalesQuotesAiInsightsComponent } from './sales-quotes-details/sales-quotes-ai-insights/sales-quotes-ai-insights.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SalesQuotesComponent\n}, {\n  path: '',\n  component: SalesQuotesDetailsComponent,\n  children: [{\n    path: 'overview/:id',\n    component: SalesQuotesOverviewComponent\n  }, {\n    path: 'contacts',\n    component: SalesQuotesContactsComponent\n  }, {\n    path: 'partners',\n    component: SalesQuotePartnersComponent\n  }, {\n    path: 'opportunities',\n    component: SalesQuoteOpportunitiesComponent\n  }, {\n    path: 'organization-data',\n    component: SalesQuoteOrganizationDataComponent\n  }, {\n    path: 'ai-insights',\n    component: SalesQuotesAiInsightsComponent\n  }, {\n    path: 'sales-team',\n    component: SalesQuoteSalesTeamComponent\n  }, {\n    path: 'attachments',\n    component: SalesQuoteAttachmentsComponent\n  }, {\n    path: 'activities',\n    component: SalesQuoteActivitiesComponent\n  }, {\n    path: 'relationships',\n    component: SalesQuoteRelationshipsComponent\n  }, {\n    path: 'tickets',\n    component: SalesQuoteTicketsComponent\n  }, {\n    path: 'notes',\n    component: SalesQuotesNotesComponent\n  }, {\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }]\n}];\nexport class SalesQuotesRoutingModule {\n  static {\n    this.ɵfac = function SalesQuotesRoutingModule_Factory(t) {\n      return new (t || SalesQuotesRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SalesQuotesRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SalesQuotesRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SalesQuotesComponent", "SalesQuotesDetailsComponent", "SalesQuotesOverviewComponent", "SalesQuotesContactsComponent", "SalesQuotePartnersComponent", "SalesQuoteOpportunitiesComponent", "SalesQuoteOrganizationDataComponent", "SalesQuoteSalesTeamComponent", "SalesQuoteAttachmentsComponent", "SalesQuoteActivitiesComponent", "SalesQuoteRelationshipsComponent", "SalesQuoteTicketsComponent", "SalesQuotesNotesComponent", "SalesQuotesAiInsightsComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "SalesQuotesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\sales-quotes\\sales-quotes-routing.module.ts"], "sourcesContent": ["import { Component, NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { SalesQuotesComponent } from './sales-quotes.component';\r\nimport { SalesQuotesDetailsComponent } from './sales-quotes-details/sales-quotes-details.component';\r\nimport { SalesQuotesOverviewComponent } from './sales-quotes-details/sales-quotes-overview/sales-quotes-overview.component';\r\nimport { SalesQuotesContactsComponent } from './sales-quotes-details/sales-quotes-contacts/sales-quotes-contacts.component';\r\nimport { SalesQuotePartnersComponent } from './sales-quotes-details/sales-quote-partners/sales-quote-partners.component';\r\nimport { SalesQuoteOpportunitiesComponent } from './sales-quotes-details/sales-quote-opportunities/sales-quote-opportunities.component';\r\nimport { SalesQuoteOrganizationDataComponent } from './sales-quotes-details/sales-quote-organization-data/sales-quote-organization-data.component';\r\nimport { SalesQuoteSalesTeamComponent } from './sales-quotes-details/sales-quote-sales-team/sales-quote-sales-team.component';\r\nimport { SalesQuoteAttachmentsComponent } from './sales-quotes-details/sales-quote-attachments/sales-quote-attachments.component';\r\nimport { SalesQuoteActivitiesComponent } from './sales-quotes-details/sales-quote-activities/sales-quote-activities.component';\r\nimport { SalesQuoteRelationshipsComponent } from './sales-quotes-details/sales-quote-relationships/sales-quote-relationships.component';\r\nimport { SalesQuoteTicketsComponent } from './sales-quotes-details/sales-quote-tickets/sales-quote-tickets.component';\r\nimport { SalesQuotesNotesComponent } from './sales-quotes-details/sales-quotes-notes/sales-quotes-notes.component';\r\nimport { SalesQuotesAiInsightsComponent } from './sales-quotes-details/sales-quotes-ai-insights/sales-quotes-ai-insights.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: SalesQuotesComponent },\r\n  {\r\n    path: '',\r\n    component: SalesQuotesDetailsComponent,\r\n    children: [\r\n      { path: 'overview/:id', component: SalesQuotesOverviewComponent },\r\n      { path: 'contacts', component: SalesQuotesContactsComponent },\r\n      { path: 'partners', component: SalesQuotePartnersComponent },\r\n      { path: 'opportunities', component: SalesQuoteOpportunitiesComponent },\r\n      { path: 'organization-data', component: SalesQuoteOrganizationDataComponent },\r\n      { path: 'ai-insights', component: SalesQuotesAiInsightsComponent },\r\n      { path: 'sales-team', component: SalesQuoteSalesTeamComponent },\r\n      { path: 'attachments', component: SalesQuoteAttachmentsComponent },\r\n      { path: 'activities', component: SalesQuoteActivitiesComponent },\r\n      { path: 'relationships', component: SalesQuoteRelationshipsComponent },\r\n      { path: 'tickets', component: SalesQuoteTicketsComponent },\r\n      { path: 'notes', component: SalesQuotesNotesComponent },\r\n      { path: '', redirectTo: 'overview', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'overview', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class SalesQuotesRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,4BAA4B,QAAQ,8EAA8E;AAC3H,SAASC,4BAA4B,QAAQ,8EAA8E;AAC3H,SAASC,2BAA2B,QAAQ,4EAA4E;AACxH,SAASC,gCAAgC,QAAQ,sFAAsF;AACvI,SAASC,mCAAmC,QAAQ,8FAA8F;AAClJ,SAASC,4BAA4B,QAAQ,gFAAgF;AAC7H,SAASC,8BAA8B,QAAQ,kFAAkF;AACjI,SAASC,6BAA6B,QAAQ,gFAAgF;AAC9H,SAASC,gCAAgC,QAAQ,sFAAsF;AACvI,SAASC,0BAA0B,QAAQ,0EAA0E;AACrH,SAASC,yBAAyB,QAAQ,wEAAwE;AAClH,SAASC,8BAA8B,QAAQ,oFAAoF;;;AAEnI,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEhB;AAAoB,CAAE,EAC7C;EACEe,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEf,2BAA2B;EACtCgB,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEd;EAA4B,CAAE,EACjE;IAAEa,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEb;EAA4B,CAAE,EAC7D;IAAEY,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEZ;EAA2B,CAAE,EAC5D;IAAEW,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAEX;EAAgC,CAAE,EACtE;IAAEU,IAAI,EAAE,mBAAmB;IAAEC,SAAS,EAAEV;EAAmC,CAAE,EAC7E;IAAES,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEH;EAA8B,CAAE,EAClE;IAAEE,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAET;EAA4B,CAAE,EAC/D;IAAEQ,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAER;EAA8B,CAAE,EAClE;IAAEO,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEP;EAA6B,CAAE,EAChE;IAAEM,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAEN;EAAgC,CAAE,EACtE;IAAEK,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEL;EAA0B,CAAE,EAC1D;IAAEI,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAEJ;EAAyB,CAAE,EACvD;IAAEG,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE,EACvD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE5D,CACF;AAMD,OAAM,MAAOC,wBAAwB;;;uBAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA;IAAwB;EAAA;;;gBAHzBrB,YAAY,CAACsB,QAAQ,CAACP,MAAM,CAAC,EAC7Bf,YAAY;IAAA;EAAA;;;2EAEXqB,wBAAwB;IAAAE,OAAA,GAAAC,EAAA,CAAAxB,YAAA;IAAAyB,OAAA,GAFzBzB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}