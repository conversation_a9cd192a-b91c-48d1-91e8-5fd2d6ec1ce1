{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input } from '@angular/core';\nimport { ObjectUtils } from 'primeng/utils';\nconst _c0 = [\"*\"];\nclass BaseIcon {\n  label;\n  spin = false;\n  styleClass;\n  role;\n  ariaLabel;\n  ariaHidden;\n  ngOnInit() {\n    this.getAttributes();\n  }\n  getAttributes() {\n    const isLabelEmpty = ObjectUtils.isEmpty(this.label);\n    this.role = !isLabelEmpty ? 'img' : undefined;\n    this.ariaLabel = !isLabelEmpty ? this.label : undefined;\n    this.ariaHidden = isLabelEmpty;\n  }\n  getClassNames() {\n    return `p-icon ${this.styleClass ? this.styleClass + ' ' : ''}${this.spin ? 'p-icon-spin' : ''}`;\n  }\n  static ɵfac = function BaseIcon_Factory(t) {\n    return new (t || BaseIcon)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: BaseIcon,\n    selectors: [[\"ng-component\"]],\n    hostAttrs: [1, \"p-element\", \"p-icon-wrapper\"],\n    inputs: {\n      label: \"label\",\n      spin: \"spin\",\n      styleClass: \"styleClass\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function BaseIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseIcon, [{\n    type: Component,\n    args: [{\n      template: ` <ng-content></ng-content> `,\n      standalone: true,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element p-icon-wrapper'\n      }\n    }]\n  }], null, {\n    label: [{\n      type: Input\n    }],\n    spin: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseIcon };", "map": {"version": 3, "names": ["i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ObjectUtils", "_c0", "BaseIcon", "label", "spin", "styleClass", "role", "aria<PERSON><PERSON><PERSON>", "ariaHidden", "ngOnInit", "getAttributes", "isLabelEmpty", "isEmpty", "undefined", "getClassNames", "ɵfac", "BaseIcon_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "template", "BaseIcon_Template", "rf", "ctx", "ɵɵprojectionDef", "ɵɵprojection", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "OnPush", "None", "host", "class"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/primeng/fesm2022/primeng-baseicon.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input } from '@angular/core';\nimport { ObjectUtils } from 'primeng/utils';\n\nclass BaseIcon {\n    label;\n    spin = false;\n    styleClass;\n    role;\n    ariaLabel;\n    ariaHidden;\n    ngOnInit() {\n        this.getAttributes();\n    }\n    getAttributes() {\n        const isLabelEmpty = ObjectUtils.isEmpty(this.label);\n        this.role = !isLabelEmpty ? 'img' : undefined;\n        this.ariaLabel = !isLabelEmpty ? this.label : undefined;\n        this.ariaHidden = isLabelEmpty;\n    }\n    getClassNames() {\n        return `p-icon ${this.styleClass ? this.styleClass + ' ' : ''}${this.spin ? 'p-icon-spin' : ''}`;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: BaseIcon, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: BaseIcon, isStandalone: true, selector: \"ng-component\", inputs: { label: \"label\", spin: \"spin\", styleClass: \"styleClass\" }, host: { classAttribute: \"p-element p-icon-wrapper\" }, ngImport: i0, template: ` <ng-content></ng-content> `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: BaseIcon, decorators: [{\n            type: Component,\n            args: [{\n                    template: ` <ng-content></ng-content> `,\n                    standalone: true,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        class: 'p-element p-icon-wrapper'\n                    }\n                }]\n        }], propDecorators: { label: [{\n                type: Input\n            }], spin: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseIcon };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,QAAQ,eAAe;AAC5F,SAASC,WAAW,QAAQ,eAAe;AAAC,MAAAC,GAAA;AAE5C,MAAMC,QAAQ,CAAC;EACXC,KAAK;EACLC,IAAI,GAAG,KAAK;EACZC,UAAU;EACVC,IAAI;EACJC,SAAS;EACTC,UAAU;EACVC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,aAAa,CAAC,CAAC;EACxB;EACAA,aAAaA,CAAA,EAAG;IACZ,MAAMC,YAAY,GAAGX,WAAW,CAACY,OAAO,CAAC,IAAI,CAACT,KAAK,CAAC;IACpD,IAAI,CAACG,IAAI,GAAG,CAACK,YAAY,GAAG,KAAK,GAAGE,SAAS;IAC7C,IAAI,CAACN,SAAS,GAAG,CAACI,YAAY,GAAG,IAAI,CAACR,KAAK,GAAGU,SAAS;IACvD,IAAI,CAACL,UAAU,GAAGG,YAAY;EAClC;EACAG,aAAaA,CAAA,EAAG;IACZ,OAAQ,UAAS,IAAI,CAACT,UAAU,GAAG,IAAI,CAACA,UAAU,GAAG,GAAG,GAAG,EAAG,GAAE,IAAI,CAACD,IAAI,GAAG,aAAa,GAAG,EAAG,EAAC;EACpG;EACA,OAAOW,IAAI,YAAAC,iBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFf,QAAQ;EAAA;EAC3G,OAAOgB,IAAI,kBAD8EvB,EAAE,CAAAwB,iBAAA;IAAAC,IAAA,EACJlB,QAAQ;IAAAmB,SAAA;IAAAC,SAAA;IAAAC,MAAA;MAAApB,KAAA;MAAAC,IAAA;MAAAC,UAAA;IAAA;IAAAmB,UAAA;IAAAC,QAAA,GADN9B,EAAE,CAAA+B,mBAAA;IAAAC,kBAAA,EAAA1B,GAAA;IAAA2B,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrC,EAAE,CAAAuC,eAAA;QAAFvC,EAAE,CAAAwC,YAAA,EACgO,CAAC;MAAA;IAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAChU;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6F3C,EAAE,CAAA4C,iBAAA,CAGJrC,QAAQ,EAAc,CAAC;IACtGkB,IAAI,EAAExB,SAAS;IACf4C,IAAI,EAAE,CAAC;MACCV,QAAQ,EAAG,6BAA4B;MACvCN,UAAU,EAAE,IAAI;MAChBa,eAAe,EAAExC,uBAAuB,CAAC4C,MAAM;MAC/CL,aAAa,EAAEtC,iBAAiB,CAAC4C,IAAI;MACrCC,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEzC,KAAK,EAAE,CAAC;MACtBiB,IAAI,EAAErB;IACV,CAAC,CAAC;IAAEK,IAAI,EAAE,CAAC;MACPgB,IAAI,EAAErB;IACV,CAAC,CAAC;IAAEM,UAAU,EAAE,CAAC;MACbe,IAAI,EAAErB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}