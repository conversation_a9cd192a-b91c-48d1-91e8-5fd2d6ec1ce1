<div class="p-3 w-full surface-card border-round shadow-1">

    <div class="card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2">
        <h4 class="m-0 pl-3 left-border relative flex">Involved Parties</h4>

        <div class="flex align-items-center gap-3">
            <p-button label="Add" (click)="showNewDialog('right')" icon="pi pi-plus-circle" iconPos="right"
                class="ml-auto" [styleClass]="'font-semibold px-3'" [rounded]="true" />

            <p-multiSelect [options]="cols" [(ngModel)]="selectedColumns" optionLabel="header"
                class="table-multiselect-dropdown"
                [styleClass]="'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'">
            </p-multiSelect>
        </div>
    </div>

    <div class="table-sec">
        <p-table #dt1 [value]="involvedpartiesdetails" dataKey="id" [rows]="10" [paginator]="true" [lazy]="true"
            responsiveLayout="scroll" [scrollable]="true" class="scrollable-table" [reorderableColumns]="true"
            (onColReorder)="onColumnReorder($event)">

            <ng-template pTemplate="header">
                <tr>
                    <th pFrozenColumn (click)="customSort('role_code')" class="border-round-left-lg">
                        <div class="flex align-items-center cursor-pointer">
                            Role
                            <i *ngIf="sortField === 'role_code'" class="ml-2 pi"
                                [ngClass]="sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'">
                            </i>
                            <i *ngIf="sortField !== 'role_code'" class="ml-2 pi pi-sort"></i>
                        </div>
                    </th>
                    <ng-container *ngFor="let col of selectedColumns">
                        <th [pSortableColumn]="col.field" pReorderableColumn (click)="customSort(col.field)">
                            <div class="flex align-items-center cursor-pointer">
                                {{ col.header }}
                                <i *ngIf="sortField === col.field" class="ml-2 pi"
                                    [ngClass]="sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'">
                                </i>
                                <i *ngIf="sortField !== col.field" class="ml-2 pi pi-sort"></i>
                            </div>
                        </th>
                    </ng-container>
                    <th class="border-round-right-lg">Action</th>
                </tr>
            </ng-template>

            <ng-template pTemplate="body" let-partie let-columns="columns">
                <tr class="cursor-pointer">
                    <td pFrozenColumn class="border-round-left-lg font-medium">
                        {{ partie?.role_code || "-" }}
                    </td>
                    <ng-container *ngFor="let col of selectedColumns">
                        <td>
                            <ng-container [ngSwitch]="col.field">
                                <ng-container *ngSwitchCase="'bp_full_name'">
                                    {{ partie?.bp_full_name || "-" }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'mobile'">
                                    {{ partie?.mobile || "-" }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'phone_number'">
                                    {{ partie?.phone_number || "-" }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'email_address'">
                                    {{ partie?.email_address || "-" }}
                                </ng-container>

                                <ng-container *ngSwitchCase="'address'">
                                    {{ partie?.address || "-" }}
                                </ng-container>

                            </ng-container>
                        </td>
                    </ng-container>
                    <td class="border-round-right-lg">
                        <button pButton type="button" icon="pi pi-trash" pTooltip="Delete"
                            (click)="$event.stopPropagation(); confirmRemove(partie)"></button>
                    </td>
                </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="7" class="border-round-left-lg">No involved parties found.</td>
                </tr>
            </ng-template>
            <ng-template pTemplate="loadingbody">
                <tr>
                    <td colspan="7" class="border-round-left-lg">Loading involved parties data. Please wait.</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>
<p-dialog [modal]="true" [(visible)]="addDialogVisible" [position]="'right'"
    [draggable]="false" class="party-popup">
    <ng-template pTemplate="header">
        <h4>Involved Parties</h4>
    </ng-template>

    <form [formGroup]="InvolvedPartiesForm" class="relative flex flex-column gap-1">
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Role">
                <span class="material-symbols-rounded">supervisor_account</span>Role
                <span class="text-red-500">*</span>
            </label>
            <div class="form-input flex-1 relative">
                <p-dropdown [options]="role" formControlName="role_code" placeholder="Select a Role" optionLabel="label"
                    optionValue="value" styleClass="h-3rem w-full">
                </p-dropdown>
            </div>
        </div>
        <div class="field flex align-items-center text-base">
            <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Involved Party">
                <span class="material-symbols-rounded">person</span>Involved Party
            </label>
            <div class="form-input flex-1 relative">
                <ng-select [items]="partyData$ | async" bindLabel="bp_full_name" bindValue="bp_id" [hideSelected]="true"
                    [loading]="partyDataLoading" [minTermLength]="3" [typeahead]="partyInput$"
                    formControlName="party_id" appendTo="body" [class]="'multiselect-dropdown p-inputtext p-component p-element'"
                    (search)="partyInput$.next($event.term)" placeholder="Search for a party">
                    <ng-template ng-option-tmp let-item="item">
                        <span>{{ item.bp_id }}</span>
                        <span *ngIf="item.bp_full_name"> : {{ item.bp_full_name }}</span>
                        <span *ngIf="item.email && item.bp_full_name">
                            : {{ item.email }}</span>
                        <span *ngIf="item.email && !item.bp_full_name">
                            : {{ item.email }}</span>
                        <span *ngIf="item.mobile && (item.email || item.bp_full_name)">
                            : {{ item.mobile }}</span>
                        <span *ngIf="item.mobile && !item.email && !item.bp_full_name">
                            : {{ item.mobile }}</span>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="flex align-items-center p-3 gap-3 mt-1">
            <button pButton type="button" label="Cancel"
                class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem"
                (click)="addDialogVisible = false"></button>
            <button pButton type="submit" label="Save" class="p-button-rounded justify-content-center w-9rem h-3rem"
                (click)="onSubmit()"></button>
        </div>
    </form>
</p-dialog>