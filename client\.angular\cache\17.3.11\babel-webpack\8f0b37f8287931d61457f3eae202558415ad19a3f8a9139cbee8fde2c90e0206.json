{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../account.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"primeng/calendar\";\nimport * as i8 from \"primeng/button\";\nfunction AccountOverviewComponent_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"label\", 6)(4, \"span\", 7);\n    i0.ɵɵtext(5, \"pool\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Pool \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 8);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 4)(10, \"div\", 5)(11, \"label\", 6)(12, \"span\", 7);\n    i0.ɵɵtext(13, \"restaurant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Restaurant \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 8);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 4)(18, \"div\", 5)(19, \"label\", 6)(20, \"span\", 7);\n    i0.ɵɵtext(21, \"meeting_room\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" Conference Room \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 8);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 4)(26, \"div\", 5)(27, \"label\", 6)(28, \"span\", 7);\n    i0.ɵɵtext(29, \"fitness_center\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Fitness Center / Gym \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 8);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 4)(34, \"div\", 5)(35, \"label\", 6)(36, \"span\", 7);\n    i0.ɵɵtext(37, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" STR Chain Scale \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 8);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 4)(42, \"div\", 5)(43, \"label\", 6)(44, \"span\", 7);\n    i0.ɵɵtext(45, \"scale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(46, \" Size \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 8);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 4)(50, \"div\", 5)(51, \"label\", 6)(52, \"span\", 7);\n    i0.ɵɵtext(53, \"straighten\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(54, \" Size Unit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 8);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(57, \"div\", 4)(58, \"div\", 5)(59, \"label\", 6)(60, \"span\", 7);\n    i0.ɵɵtext(61, \"build\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(62, \" Renovation Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"div\", 8);\n    i0.ɵɵtext(64);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(65, \"div\", 4)(66, \"div\", 5)(67, \"label\", 6)(68, \"span\", 7);\n    i0.ɵɵtext(69, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(70, \" Date Opened \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"div\", 8);\n    i0.ɵɵtext(72);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(73, \"div\", 4)(74, \"div\", 5)(75, \"label\", 6)(76, \"span\", 7);\n    i0.ɵɵtext(77, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(78, \" Seasonal Open Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(79, \"div\", 8);\n    i0.ɵɵtext(80);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(81, \"div\", 4)(82, \"div\", 5)(83, \"label\", 6)(84, \"span\", 7);\n    i0.ɵɵtext(85, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(86, \" Seasonal Close Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(87, \"div\", 8);\n    i0.ɵɵtext(88);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.marketingDetails == null ? null : ctx_r0.marketingDetails.pool) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.marketingDetails == null ? null : ctx_r0.marketingDetails.restaurant) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.marketingDetails == null ? null : ctx_r0.marketingDetails.conference_room) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.marketingDetails == null ? null : ctx_r0.marketingDetails.fitness_center) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getLabelFromDropdown(\"chainscale\", ctx_r0.customer == null ? null : ctx_r0.customer.free_defined_attribute_03) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.marketingDetails == null ? null : ctx_r0.marketingDetails.size) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getLabelFromDropdown(\"sizeunit\", ctx_r0.customer == null ? null : ctx_r0.customer.free_defined_attribute_04) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r0.marketingDetails == null ? null : ctx_r0.marketingDetails.renovation_date) || \"-\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.marketingDetails == null ? null : ctx_r0.marketingDetails.date_opened) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.marketingDetails == null ? null : ctx_r0.marketingDetails.seasonal_open_date) || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.marketingDetails == null ? null : ctx_r0.marketingDetails.seasonal_close_date) || \"-\");\n  }\n}\nfunction AccountOverviewComponent_form_75_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 13)(1, \"div\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"label\", 14)(5, \"span\", 15);\n    i0.ɵɵtext(6, \"pool\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" Pool \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p-dropdown\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 4)(10, \"div\", 5)(11, \"label\", 14)(12, \"span\", 15);\n    i0.ɵɵtext(13, \"restaurant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Restaurant \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"p-dropdown\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 4)(17, \"div\", 5)(18, \"label\", 14)(19, \"span\", 15);\n    i0.ɵɵtext(20, \"meeting_room\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Conference Room \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"p-dropdown\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 4)(24, \"div\", 5)(25, \"label\", 14)(26, \"span\", 15);\n    i0.ɵɵtext(27, \"fitness_center\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \" Fitness Center / Gym \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"p-dropdown\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 4)(31, \"div\", 5)(32, \"label\", 14)(33, \"span\", 15);\n    i0.ɵɵtext(34, \"build\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(35, \" Renovation Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"p-calendar\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 4)(38, \"div\", 5)(39, \"label\", 14)(40, \"span\", 15);\n    i0.ɵɵtext(41, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(42, \" Date Opened \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(43, \"p-calendar\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 4)(45, \"div\", 5)(46, \"label\", 14)(47, \"span\", 15);\n    i0.ɵɵtext(48, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(49, \" Seasonal Open Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(50, \"p-dropdown\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 4)(52, \"div\", 5)(53, \"label\", 14)(54, \"span\", 15);\n    i0.ɵɵtext(55, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(56, \" Seasonal Close Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(57, \"p-dropdown\", 23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(58, \"div\", 24)(59, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function AccountOverviewComponent_form_75_Template_button_click_59_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onAttributeSubmit());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.AccountAttributeForm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"options\", ctx_r0.marketingoptions);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.marketingoptions);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.marketingoptions);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.marketingoptions);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"showButtonBar\", true)(\"showIcon\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.months);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r0.months);\n  }\n}\nexport let AccountOverviewComponent = /*#__PURE__*/(() => {\n  class AccountOverviewComponent {\n    constructor(accountservice, formBuilder, router, messageservice) {\n      this.accountservice = accountservice;\n      this.formBuilder = formBuilder;\n      this.router = router;\n      this.messageservice = messageservice;\n      this.unsubscribe$ = new Subject();\n      this.accountoverview = null;\n      this.marketingDetails = null;\n      this.customer = null;\n      this.bpextensionDetails = null;\n      this.isAttributeEditMode = false;\n      this.bp_id = '';\n      this.documentId = '';\n      this.submitted = false;\n      this.saving = false;\n      this.months = [{\n        label: 'January',\n        value: 'January'\n      }, {\n        label: 'February',\n        value: 'February'\n      }, {\n        label: 'March',\n        value: 'March'\n      }, {\n        label: 'April',\n        value: 'April'\n      }, {\n        label: 'May',\n        value: 'May'\n      }, {\n        label: 'June',\n        value: 'June'\n      }, {\n        label: 'July',\n        value: 'July'\n      }, {\n        label: 'August',\n        value: 'August'\n      }, {\n        label: 'September',\n        value: 'September'\n      }, {\n        label: 'October',\n        value: 'October'\n      }, {\n        label: 'November',\n        value: 'November'\n      }, {\n        label: 'December',\n        value: 'December'\n      }];\n      this.marketingoptions = [{\n        label: 'Yes',\n        value: 'Yes'\n      }, {\n        label: 'No',\n        value: 'No'\n      }];\n      this.dropdowns = {\n        nativeLanguage: [],\n        purchasingControl: [],\n        chainscale: [],\n        sizeunit: []\n      };\n      this.AccountAttributeForm = this.formBuilder.group({\n        pool: [''],\n        restaurant: [''],\n        conference_room: [''],\n        fitness_center: [''],\n        renovation_date: [''],\n        date_opened: [''],\n        seasonal_open_date: [''],\n        seasonal_close_date: ['']\n      });\n    }\n    ngOnInit() {\n      this.loadDropDown('nativeLanguage', 'CRM_NATIVE_LANG');\n      this.loadDropDown('purchasingControl', 'CRM_PURCHASING_CTRL');\n      this.loadDropDown('chainscale', 'BPMA_STR_CHAIN_SCALE');\n      this.loadDropDown('sizeunit', 'BPMA_SIZE_UNIT');\n      this.accountservice.account.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n        if (!response) return;\n        this.bp_id = response?.bp_id;\n        this.documentId = response?.documentId;\n        this.marketingDetails = response?.marketing_attributes;\n        this.customer = response?.customer;\n        this.bpextensionDetails = response?.bp_extension;\n        const defaultAddress = response?.addresses?.find(address => {\n          return address.address_usages.find(usage => usage.address_usage === 'XXDEFAULT');\n        });\n        this.accountoverview = [{\n          ...response,\n          address: [defaultAddress?.house_number, defaultAddress?.street_name, defaultAddress?.city_name, defaultAddress?.region, defaultAddress?.country, defaultAddress?.postal_code].filter(Boolean).join(', '),\n          mobile: (defaultAddress?.phone_numbers || []).find(item => item.phone_number_type === '3')?.phone_number || '-',\n          phone_number: (defaultAddress?.phone_numbers || []).find(item => item.phone_number_type === '1')?.phone_number || '-',\n          status: response?.is_marked_for_archiving ? 'Obsolete' : 'Active'\n        }];\n        if (this.marketingDetails) {\n          this.AccountAttributeForm.patchValue({\n            pool: this.marketingDetails.pool || '',\n            restaurant: this.marketingDetails.restaurant || '',\n            conference_room: this.marketingDetails.conference_room || '',\n            fitness_center: this.marketingDetails.fitness_center || '',\n            renovation_date: this.marketingDetails.renovation_date ? new Date(this.marketingDetails.renovation_date) : null,\n            date_opened: this.marketingDetails.date_opened ? new Date(this.marketingDetails.date_opened) : null,\n            seasonal_open_date: this.marketingDetails.seasonal_open_date || '',\n            seasonal_close_date: this.marketingDetails.seasonal_close_date || ''\n          });\n        }\n      });\n    }\n    loadDropDown(target, type) {\n      this.accountservice.getDropdownOptions(type).subscribe(res => {\n        this.dropdowns[target] = res?.data?.map(attr => ({\n          label: attr.description,\n          value: attr.code\n        })) ?? [];\n      });\n    }\n    getLabelFromDropdown(dropdownKey, value) {\n      const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n      return item?.label || value;\n    }\n    onAttributeSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        if (_this.AccountAttributeForm.invalid) {\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.AccountAttributeForm.value\n        };\n        const data = {\n          pool: value?.pool,\n          restaurant: value?.restaurant,\n          conference_room: value?.conference_room,\n          fitness_center: value?.fitness_center,\n          date_opened: value?.date_opened ? _this.formatDate(value.date_opened) : null,\n          renovation_date: value?.renovation_date ? _this.formatDate(value.renovation_date) : null,\n          seasonal_open_date: value?.seasonal_open_date,\n          seasonal_close_date: value?.seasonal_close_date,\n          bp_id: _this?.bp_id\n        };\n        const apiCall = _this.marketingDetails ? _this.accountservice.updateMarketing(_this.marketingDetails.documentId, data) // Update if exists\n        : _this.accountservice.createMarketing(data); // Create if not exists\n        apiCall.pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          next: () => {\n            _this.messageservice.add({\n              severity: 'success',\n              detail: 'Account Attributes Updated successFully!'\n            });\n            _this.accountservice.getAccountByID(_this.documentId).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n            _this.isAttributeEditMode = false;\n          },\n          error: () => {\n            _this.saving = false;\n            _this.isAttributeEditMode = true;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      })();\n    }\n    formatDate(date) {\n      if (!date) return '';\n      const yyyy = date.getFullYear();\n      const mm = String(date.getMonth() + 1).padStart(2, '0');\n      const dd = String(date.getDate()).padStart(2, '0');\n      return `${yyyy}-${mm}-${dd}`;\n    }\n    toggleAttributeEdit() {\n      this.isAttributeEditMode = !this.isAttributeEditMode;\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function AccountOverviewComponent_Factory(t) {\n        return new (t || AccountOverviewComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MessageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AccountOverviewComponent,\n        selectors: [[\"app-account-overview\"]],\n        decls: 76,\n        vars: 13,\n        consts: [[1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"m-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"mb-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-primary\"], [1, \"readonly-field\", \"font-medium\", \"text-700\", \"p-2\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\", \"mt-5\"], [\"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"label\", \"icon\", \"styleClass\", \"rounded\"], [\"class\", \"p-fluid p-formgrid grid m-0\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [3, \"formGroup\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-600\"], [\"formControlName\", \"pool\", \"placeholder\", \"Select a Pool\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"formControlName\", \"restaurant\", \"placeholder\", \"Select a Restaurant\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"formControlName\", \"conference_room\", \"placeholder\", \"Select a Conference Room\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"formControlName\", \"fitness_center\", \"placeholder\", \"Select a Fitness Center / Gym\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"formControlName\", \"renovation_date\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Renovation Date\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"formControlName\", \"date_opened\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Date Opened\", \"styleClass\", \"h-3rem w-full\", 3, \"showButtonBar\", \"showIcon\"], [\"formControlName\", \"seasonal_open_date\", \"placeholder\", \"Select a Seasonal Open Date\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [\"formControlName\", \"seasonal_close_date\", \"placeholder\", \"Select a Seasonal Close Date\", \"dataKey\", \"value\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"styleClass\", \"h-3rem w-full\", 3, \"options\"], [1, \"flex\", \"align-items-center\", \"p-3\", \"gap-3\", \"mt-1\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Save\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"]],\n        template: function AccountOverviewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n            i0.ɵɵtext(3, \"Overview\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"label\", 6)(8, \"span\", 7);\n            i0.ɵɵtext(9, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(10, \" Name \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"div\", 8);\n            i0.ɵɵtext(12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 4)(14, \"div\", 5)(15, \"label\", 6)(16, \"span\", 7);\n            i0.ɵɵtext(17, \"assignment_ind\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(18, \" Account ID \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"div\", 8);\n            i0.ɵɵtext(20);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(21, \"div\", 4)(22, \"div\", 5)(23, \"label\", 6)(24, \"span\", 7);\n            i0.ɵɵtext(25, \"verified_user\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(26, \" Role \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"div\", 8);\n            i0.ɵɵtext(28, \"Customer \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(29, \"div\", 4)(30, \"div\", 5)(31, \"label\", 6)(32, \"span\", 7);\n            i0.ɵɵtext(33, \"location_on\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(34, \" Address \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"div\", 8);\n            i0.ɵɵtext(36);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"div\", 4)(38, \"div\", 5)(39, \"label\", 6)(40, \"span\", 7);\n            i0.ɵɵtext(41, \"phone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(42, \" Phone \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"div\", 8);\n            i0.ɵɵtext(44);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(45, \"div\", 4)(46, \"div\", 5)(47, \"label\", 6)(48, \"span\", 7);\n            i0.ɵɵtext(49, \"language\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(50, \" Native Language \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"div\", 8);\n            i0.ɵɵtext(52);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(53, \"div\", 4)(54, \"div\", 5)(55, \"label\", 6)(56, \"span\", 7);\n            i0.ɵɵtext(57, \"lock\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(58, \" Purchasing Control \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"div\", 8);\n            i0.ɵɵtext(60);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(61, \"div\", 4)(62, \"div\", 5)(63, \"label\", 6)(64, \"span\", 7);\n            i0.ɵɵtext(65, \"check_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(66, \" Status \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"div\", 8);\n            i0.ɵɵtext(68);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(69, \"div\", 9)(70, \"div\", 1)(71, \"h4\", 2);\n            i0.ɵɵtext(72, \"Marketing Attributes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"p-button\", 10);\n            i0.ɵɵlistener(\"click\", function AccountOverviewComponent_Template_p_button_click_73_listener() {\n              return ctx.toggleAttributeEdit();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(74, AccountOverviewComponent_div_74_Template, 89, 11, \"div\", 11)(75, AccountOverviewComponent_form_75_Template, 60, 11, \"form\", 12);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate1(\"\", (ctx.accountoverview == null ? null : ctx.accountoverview[0] == null ? null : ctx.accountoverview[0].bp_full_name) || \"-\", \" \");\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate1(\"\", (ctx.accountoverview == null ? null : ctx.accountoverview[0] == null ? null : ctx.accountoverview[0].bp_id) || \"-\", \" \");\n            i0.ɵɵadvance(16);\n            i0.ɵɵtextInterpolate1(\"\", (ctx.accountoverview == null ? null : ctx.accountoverview[0] == null ? null : ctx.accountoverview[0].address) || \"-\", \" \");\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate1(\"\", (ctx.accountoverview == null ? null : ctx.accountoverview[0] == null ? null : ctx.accountoverview[0].phone_number) || \"-\", \" \");\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getLabelFromDropdown(\"nativeLanguage\", ctx.bpextensionDetails == null ? null : ctx.bpextensionDetails.native_language) || \"-\", \" \");\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getLabelFromDropdown(\"purchasingControl\", ctx.bpextensionDetails == null ? null : ctx.bpextensionDetails.purchasing_control) || \"-\", \" \");\n            i0.ɵɵadvance(8);\n            i0.ɵɵtextInterpolate1(\"\", (ctx.accountoverview == null ? null : ctx.accountoverview[0] == null ? null : ctx.accountoverview[0].status) || \"-\", \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"label\", ctx.isAttributeEditMode ? \"Close\" : \"Edit\")(\"icon\", !ctx.isAttributeEditMode ? \"pi pi-pencil\" : \"\")(\"styleClass\", \"w-5rem font-semibold px-3\")(\"rounded\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isAttributeEditMode);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isAttributeEditMode);\n          }\n        },\n        dependencies: [i5.NgIf, i6.Dropdown, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i7.Calendar, i8.ButtonDirective, i8.Button]\n      });\n    }\n  }\n  return AccountOverviewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}