{"ast": null, "code": "import { stringify } from \"qs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../account/account.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"primeng/accordion\";\nfunction IdentifyAccountComponent_p_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No records found.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction IdentifyAccountComponent_p_accordionTab_91_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"span\", 42);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h5\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 44)(7, \"div\", 45)(8, \"span\", 46);\n    i0.ɵɵtext(9, \"Account Name :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"h6\", 47);\n    i0.ɵɵtext(11, \"Marriott\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 45)(13, \"span\", 46);\n    i0.ɵɵtext(14, \"Email :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"h6\", 47);\n    i0.ɵɵtext(16, \"<EMAIL>\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 45)(18, \"span\", 46);\n    i0.ɵɵtext(19, \"Phone :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"h6\", 47);\n    i0.ɵɵtext(21, \"****** 525 5265\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 45)(23, \"span\", 46);\n    i0.ɵɵtext(24, \"Address :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"h6\", 47);\n    i0.ɵɵtext(26, \"8400 Costa Verde Dr, Myrtle Beach, SC 29572... \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getInitials(item_r1.bp_full_name));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.bp_full_name);\n  }\n}\nfunction IdentifyAccountComponent_p_accordionTab_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-accordionTab\");\n    i0.ɵɵtemplate(1, IdentifyAccountComponent_p_accordionTab_91_ng_template_1_Template, 27, 2, \"ng-template\", 38);\n    i0.ɵɵelement(2, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class IdentifyAccountComponent {\n  constructor(renderer, fb, service) {\n    this.renderer = renderer;\n    this.fb = fb;\n    this.service = service;\n    this.bodyClass = 'identify-account-body';\n    this.items = [{\n      label: 'Identify Account',\n      routerLink: ['/store/identify-account']\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: ['/']\n    };\n    this.filterForm = this.fb.group({\n      bp_id: [''],\n      bp_name: [''],\n      // s4_hana_id: [''],\n      street: [''],\n      city: [''],\n      state: [''],\n      zip_code: [''],\n      country: [''],\n      email: [''],\n      phone: [''],\n      invoice_no: ['']\n    });\n    this.data = [];\n    this.loading = false;\n    this.checked = false;\n  }\n  ngOnInit() {\n    this.renderer.addClass(document.body, this.bodyClass);\n  }\n  search() {\n    const obj = {\n      populate: ['roles'],\n      fields: ['bp_id', {\n        roles: ['bp_role']\n      }],\n      filters: {\n        $and: [{\n          roles: {\n            bp_role: {\n              $in: ['FLCU00', 'FLCU01', 'BUP001']\n            }\n          }\n        }]\n      }\n    };\n    if (this.filterForm.value.bp_id) {\n      obj.filters.$and.push({\n        'bp_id': {\n          $eq: this.filterForm.value.bp_id || ''\n        }\n      });\n    }\n    if (this.filterForm.value.bp_name) {\n      obj.filters.$and.push({\n        'bp_full_name': {\n          $containsi: this.filterForm.value.bp_name || ''\n        }\n      });\n    }\n    if (this.filterForm.value.street) {\n      obj.filters.$and.push({\n        'address_usages': {\n          business_partner_address: {\n            street_name: {\n              $containsi: this.filterForm.value.street || ''\n            }\n          }\n        }\n      });\n    }\n    if (this.filterForm.value.city) {\n      obj.filters.$and.push({\n        'address_usages': {\n          business_partner_address: {\n            city_name: {\n              $containsi: this.filterForm.value.city || ''\n            }\n          }\n        }\n      });\n    }\n    if (this.filterForm.value.state) {\n      obj.filters.$and.push({\n        'address_usages': {\n          business_partner_address: {\n            region: {\n              $containsi: this.filterForm.value.state || ''\n            }\n          }\n        }\n      });\n    }\n    if (this.filterForm.value.zip_code) {\n      obj.filters.$and.push({\n        'address_usages': {\n          business_partner_address: {\n            postal_code: {\n              $containsi: this.filterForm.value.zip_code || ''\n            }\n          }\n        }\n      });\n    }\n    if (this.filterForm.value.country) {\n      obj.filters.$and.push({\n        'address_usages': {\n          business_partner_address: {\n            country: {\n              $containsi: this.filterForm.value.country || ''\n            }\n          }\n        }\n      });\n    }\n    if (this.filterForm.value.email) {\n      obj.filters.$and.push({\n        'address_usages': {\n          business_partner_address: {\n            emails: {\n              email_address: {\n                $containsi: this.filterForm.value.email || ''\n              }\n            }\n          }\n        }\n      });\n    }\n    if (this.filterForm.value.phone) {\n      obj.filters.$and.push({\n        'address_usages': {\n          business_partner_address: {\n            phone_numbers: {\n              phone_number: {\n                $containsi: this.filterForm.value.phone || ''\n              }\n            }\n          }\n        }\n      });\n    }\n    const params = stringify(obj);\n    this.loading = true;\n    this.data = [];\n    this.service.search(params).subscribe(res => {\n      this.data = res;\n      this.loading = false;\n    }, () => {\n      this.loading = false;\n    });\n  }\n  reset() {\n    this.filterForm.reset();\n  }\n  getInitials(name) {\n    return name.trim().split(/\\s+/) // split by spaces\n    .slice(0, 2) // only take first two words\n    .map(word => word[0].toUpperCase()).join('');\n  }\n  static {\n    this.ɵfac = function IdentifyAccountComponent_Factory(t) {\n      return new (t || IdentifyAccountComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AccountService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: IdentifyAccountComponent,\n      selectors: [[\"app-identify-account\"]],\n      decls: 92,\n      vars: 14,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"mt-3\", \"flex\", \"flex-column\", \"gap-3\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-6\"], [1, \"identify-name-box\", \"px-3\", \"flex\", \"align-items-center\", \"w-full\", \"h-4rem\", \"surface-b\", \"border-round\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"m-0\", \"flex\", \"align-items-center\", \"gap-2\", \"text-lg\", \"font-semibold\", \"text-primary\"], [1, \"material-symbols-rounded\"], [1, \"account-sec\", \"w-full\", \"border-none\", \"border-bottom-2\", \"border-solid\", \"border-gray-50\"], [1, \"acc-title\", \"mb-3\", \"pb-3\", \"flex\", \"align-items-center\", \"justify-content-between\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"border-gray-50\"], [1, \"header-title\", \"m-0\", \"pl-3\", \"relative\"], [1, \"account-p-tabs\", \"relative\", \"flex\", \"gap-3\", \"flex-column\", 3, \"formGroup\"], [1, \"acc-tab-info\", \"pb-2\"], [1, \"col-12\", \"lg:col-3\", \"md:col-3\"], [1, \"flex\", \"flex-column\", \"gap-2\"], [\"for\", \"username\", 1, \"text-500\", \"font-medium\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"bp_name\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"bp_id\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"street\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [1, \"col-12\", \"lg:col-3\", \"md:col-3\", \"pt-0\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"city\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"state\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"zip_code\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"country\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"email\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-500\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"phone\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-primary\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"invoice_no\", 1, \"surface-b\", \"h-2-8rem\", \"w-full\", \"font-semibold\", \"text-primary\"], [1, \"acc-title\", \"pb-3\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"severity\", \"success\", 3, \"click\", \"outlined\", \"disabled\", \"styleClass\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [3, \"click\", \"outlined\", \"styleClass\"], [1, \"search-result\", \"mt-3\", \"w-full\"], [3, \"outlined\", \"styleClass\"], [4, \"ngIf\"], [\"expandIcon\", \"pi pi-angle-down\", \"collapseIcon\", \"pi pi-angle-up\", 1, \"w-full\"], [4, \"ngFor\", \"ngForOf\"], [\"pTemplate\", \"header\"], [1, \"table-sec\"], [1, \"flex\", \"gap-3\", \"w-full\"], [1, \"user-box\", \"flex\", \"align-items-center\", \"gap-2\", \"min-width\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-2rem\", \"h-2rem\", \"bg-blue-500\", \"border-circle\", \"font-semibold\", \"text-white\"], [1, \"m-0\", \"text-base\", \"text-900\", \"w-20rem\", \"text-overflow-ellipsis\"], [1, \"relative\", \"flex\", \"gap-3\", \"pl-3\", \"flex-1\", \"justify-content-between\"], [1, \"relative\", \"flex-1\", \"flex\", \"flex-column\", \"gap-1\"], [1, \"m-0\", \"text-sm\", \"font-normal\"], [1, \"m-0\", \"text-sm\", \"font-semibold\"]],\n      template: function IdentifyAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"h5\", 7)(8, \"i\", 8);\n          i0.ɵɵtext(9, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(10, \" Red Roof \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 5);\n          i0.ɵɵelement(12, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 10)(15, \"h5\", 11);\n          i0.ɵɵtext(16, \"Account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"form\", 12)(18, \"div\", 13)(19, \"div\", 4)(20, \"div\", 14)(21, \"div\", 15)(22, \"label\", 16);\n          i0.ɵɵtext(23, \"Account Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"input\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 14)(26, \"div\", 15)(27, \"label\", 16);\n          i0.ɵɵtext(28, \"CRM ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(29, \"input\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 14)(31, \"div\", 15)(32, \"label\", 16);\n          i0.ɵɵtext(33, \"Street / House Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"input\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"div\", 20)(36, \"div\", 15)(37, \"label\", 16);\n          i0.ɵɵtext(38, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(39, \"input\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 20)(41, \"div\", 15)(42, \"label\", 16);\n          i0.ɵɵtext(43, \"State\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(44, \"input\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 20)(46, \"div\", 15)(47, \"label\", 16);\n          i0.ɵɵtext(48, \"Zip Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(49, \"input\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 20)(51, \"div\", 15)(52, \"label\", 16);\n          i0.ɵɵtext(53, \"Country\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(54, \"input\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 20)(56, \"div\", 15)(57, \"label\", 16);\n          i0.ɵɵtext(58, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(59, \"input\", 25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 20)(61, \"div\", 15)(62, \"label\", 16);\n          i0.ɵɵtext(63, \"Telephone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(64, \"input\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"div\", 20)(66, \"div\", 15)(67, \"label\", 16);\n          i0.ɵɵtext(68, \"Invoice #\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(69, \"input\", 27);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(70, \"div\", 28)(71, \"div\", 29)(72, \"p-button\", 30);\n          i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_Template_p_button_click_72_listener() {\n            return ctx.search();\n          });\n          i0.ɵɵelementStart(73, \"i\", 31);\n          i0.ɵɵtext(74, \"search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(75);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"p-button\", 32);\n          i0.ɵɵlistener(\"click\", function IdentifyAccountComponent_Template_p_button_click_76_listener() {\n            return ctx.reset();\n          });\n          i0.ɵɵelementStart(77, \"i\", 31);\n          i0.ɵɵtext(78, \"cancel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(79, \" Clear \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(80, \"div\", 33)(81, \"div\", 10)(82, \"h5\", 11);\n          i0.ɵɵtext(83, \"Result List\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"div\", 29)(85, \"p-button\", 34)(86, \"i\", 31);\n          i0.ɵɵtext(87, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(88, \" Confirm \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(89, IdentifyAccountComponent_p_89_Template, 2, 0, \"p\", 35);\n          i0.ɵɵelementStart(90, \"p-accordion\", 36);\n          i0.ɵɵtemplate(91, IdentifyAccountComponent_p_accordionTab_91_Template, 3, 0, \"p-accordionTab\", 37);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n          i0.ɵɵadvance(55);\n          i0.ɵɵproperty(\"outlined\", true)(\"disabled\", ctx.loading)(\"styleClass\", \"flex align-items-center justify-content-center gap-1 text-primary\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Searching...\" : \"Search\", \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"outlined\", true)(\"styleClass\", \"w-6rem flex align-items-center justify-content-center gap-1 text-red-600\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"outlined\", true)(\"styleClass\", \"w-7rem flex align-items-center justify-content-center gap-2 text-color-secondary\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.data.length && !ctx.loading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.data);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i4.Breadcrumb, i5.PrimeTemplate, i6.Button, i7.InputText, i8.Accordion, i8.AccordionTab],\n      styles: [\".identify-account-body .topbar-start h1 {\\n  display: none;\\n}\\n  .min-width {\\n  min-width: 18rem;\\n}\\n  .custom-ratio-btn {\\n  width: 16px;\\n  height: 16px;\\n  accent-color: var(--primary-500);\\n}\\n  .search-result p-accordion p-accordiontab {\\n  margin: 0 0 4px 0 !important;\\n  display: flex;\\n}\\n  .search-result p-accordion p-accordiontab:last-child {\\n  margin: 0 0 0px 0 !important;\\n}\\n  .search-result p-accordion p-accordiontab .p-accordion-tab {\\n  width: 100%;\\n}\\n  .search-result p-accordion p-accordiontab .p-accordion-header .p-accordion-header-link {\\n  border: none;\\n  flex-direction: row-reverse;\\n  width: 100%;\\n  justify-content: space-between;\\n  border-radius: 8px;\\n  min-height: 48px;\\n}\\n  .search-result p-accordion p-accordiontab .p-accordion-header .p-accordion-header-link span.p-accordion-toggle-icon {\\n  margin: 0;\\n}\\n  .search-result p-accordion p-accordiontab .p-accordion-header .p-accordion-header-link:hover {\\n  box-shadow: 0 1px 3px var(--surface-100);\\n}\\n  .search-result p-accordion p-accordiontab .p-accordion-content {\\n  border-radius: 8px;\\n  border: 1px solid var(--surface-b);\\n}\\n  .search-result p-accordion p-accordiontab:nth-child(odd) .p-accordion-header .p-accordion-header-link {\\n  background: var(--surface-b);\\n}\\n  .search-result p-accordion p-accordiontab:nth-child(even) .p-accordion-header .p-accordion-header-link {\\n  background: var(--surface-0);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["stringify", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "getInitials", "item_r1", "bp_full_name", "ɵɵtemplate", "IdentifyAccountComponent_p_accordionTab_91_ng_template_1_Template", "ɵɵelement", "IdentifyAccountComponent", "constructor", "renderer", "fb", "service", "bodyClass", "items", "label", "routerLink", "home", "icon", "filterForm", "group", "bp_id", "bp_name", "street", "city", "state", "zip_code", "country", "email", "phone", "invoice_no", "data", "loading", "checked", "ngOnInit", "addClass", "document", "body", "search", "obj", "populate", "fields", "roles", "filters", "$and", "bp_role", "$in", "value", "push", "$eq", "$containsi", "business_partner_address", "street_name", "city_name", "region", "postal_code", "emails", "email_address", "phone_numbers", "phone_number", "params", "subscribe", "res", "reset", "name", "trim", "split", "slice", "map", "word", "toUpperCase", "join", "ɵɵdirectiveInject", "Renderer2", "i1", "FormBuilder", "i2", "AccountService", "selectors", "decls", "vars", "consts", "template", "IdentifyAccountComponent_Template", "rf", "ctx", "ɵɵlistener", "IdentifyAccountComponent_Template_p_button_click_72_listener", "IdentifyAccountComponent_Template_p_button_click_76_listener", "IdentifyAccountComponent_p_89_Template", "IdentifyAccountComponent_p_accordionTab_91_Template", "ɵɵproperty", "ɵɵtextInterpolate1", "length"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\identify-account\\identify-account.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\identify-account\\identify-account.component.html"], "sourcesContent": ["import { Component, Renderer2 } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { AccountService } from '../account/account.service';\r\nimport { stringify } from \"qs\";\r\nimport { FormBuilder } from '@angular/forms';\r\n@Component({\r\n  selector: 'app-identify-account',\r\n  templateUrl: './identify-account.component.html',\r\n  styleUrl: './identify-account.component.scss'\r\n})\r\nexport class IdentifyAccountComponent {\r\n\r\n  private bodyClass = 'identify-account-body';\r\n\r\n  items: MenuItem[] | any = [\r\n    { label: 'Identify Account', routerLink: ['/store/identify-account'] },\r\n  ];\r\n  home: MenuItem | any = { icon: 'pi pi-home', routerLink: ['/'] };\r\n  filterForm = this.fb.group({\r\n    bp_id: [''],\r\n    bp_name: [''],\r\n    // s4_hana_id: [''],\r\n    street: [''],\r\n    city: [''],\r\n    state: [''],\r\n    zip_code: [''],\r\n    country: [''],\r\n    email: [''],\r\n    phone: [''],\r\n    invoice_no: [''],\r\n  });\r\n  data: any[] = [];\r\n  loading: boolean = false;\r\n\r\n  constructor(\r\n    private renderer: Renderer2,\r\n    private fb: FormBuilder,\r\n    private service: AccountService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.renderer.addClass(document.body, this.bodyClass);\r\n  }\r\n\r\n  checked: boolean = false;\r\n\r\n  search() {\r\n    const obj: any = {\r\n      populate: ['roles'],\r\n      fields: ['bp_id',\r\n        {\r\n          roles: ['bp_role']\r\n        }\r\n      ],\r\n      filters: {\r\n        $and: [\r\n          {\r\n            roles: {\r\n              bp_role: {\r\n                $in: ['FLCU00', 'FLCU01', 'BUP001']\r\n              }\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    };\r\n    if (this.filterForm.value.bp_id) {\r\n      obj.filters.$and.push({\r\n        'bp_id': {\r\n          $eq: this.filterForm.value.bp_id || ''\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.bp_name) {\r\n      obj.filters.$and.push({\r\n        'bp_full_name': {\r\n          $containsi: this.filterForm.value.bp_name || ''\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.street) {\r\n      obj.filters.$and.push({\r\n        'address_usages': {\r\n          business_partner_address: {\r\n            street_name: {\r\n              $containsi: this.filterForm.value.street || ''\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.city) {\r\n      obj.filters.$and.push({\r\n        'address_usages': {\r\n          business_partner_address: {\r\n            city_name: {\r\n              $containsi: this.filterForm.value.city || ''\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.state) {\r\n      obj.filters.$and.push({\r\n        'address_usages': {\r\n          business_partner_address: {\r\n            region: {\r\n              $containsi: this.filterForm.value.state || ''\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.zip_code) {\r\n      obj.filters.$and.push({\r\n        'address_usages': {\r\n          business_partner_address: {\r\n            postal_code: {\r\n              $containsi: this.filterForm.value.zip_code || ''\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.country) {\r\n      obj.filters.$and.push({\r\n        'address_usages': {\r\n          business_partner_address: {\r\n            country: {\r\n              $containsi: this.filterForm.value.country || ''\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.email) {\r\n      obj.filters.$and.push({\r\n        'address_usages': {\r\n          business_partner_address: {\r\n            emails: {\r\n              email_address: {\r\n                $containsi: this.filterForm.value.email || ''\r\n              }\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    if (this.filterForm.value.phone) {\r\n      obj.filters.$and.push({\r\n        'address_usages': {\r\n          business_partner_address: {\r\n            phone_numbers: {\r\n              phone_number: {\r\n                $containsi: this.filterForm.value.phone || ''\r\n              }\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n    const params = stringify(obj);\r\n    this.loading = true;\r\n    this.data = [];\r\n    this.service.search(params).subscribe((res: any) => {\r\n      this.data = res;\r\n      this.loading = false;\r\n    }, () => {\r\n      this.loading = false;\r\n    });\r\n  }\r\n\r\n  reset() {\r\n    this.filterForm.reset();\r\n  }\r\n\r\n  getInitials(name: string) {\r\n    return name\r\n      .trim()\r\n      .split(/\\s+/) // split by spaces\r\n      .slice(0, 2) // only take first two words\r\n      .map(word => word[0].toUpperCase())\r\n      .join('');\r\n  }\r\n\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec mt-3 flex  flex-column gap-3\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"items\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"grid mt-0\">\r\n            <div class=\"col-12 lg:col-6\">\r\n                <div\r\n                    class=\"identify-name-box px-3 flex align-items-center w-full h-4rem surface-b border-round border-1 border-solid border-50\">\r\n                    <h5 class=\"m-0 flex align-items-center gap-2 text-lg font-semibold text-primary\">\r\n                        <i class=\"material-symbols-rounded\">person</i> Red Roof\r\n                    </h5>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-6\">\r\n                <div\r\n                    class=\"identify-name-box px-3 flex align-items-center w-full h-4rem surface-b border-round border-1 border-solid border-50\">\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"account-sec w-full border-none border-bottom-2 border-solid border-gray-50\">\r\n        <div\r\n            class=\"acc-title mb-3 pb-3 flex align-items-center justify-content-between border-none border-bottom-1 border-solid border-gray-50\">\r\n            <h5 class=\"header-title m-0 pl-3 relative\">Account</h5>\r\n            <!-- <div class=\"flex align-items-center gap-3\">\r\n                <p-button [outlined]=\"true\"\r\n                    [styleClass]=\"'w-9rem flex align-items-center justify-content-center gap-2 text-primary'\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">variable_add</i> More Fields\r\n                </p-button>\r\n            </div> -->\r\n        </div>\r\n        <form class=\"account-p-tabs relative flex gap-3 flex-column\" [formGroup]=\"filterForm\">\r\n            <div class=\"acc-tab-info pb-2\">\r\n                <div class=\"grid mt-0\">\r\n                    <div class=\"col-12 lg:col-3 md:col-3\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Account Name</label>\r\n                            <input pInputText id=\"username\" formControlName=\"bp_name\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-3 md:col-3\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">CRM ID</label>\r\n                            <input pInputText id=\"username\" formControlName=\"bp_id\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <!-- <div class=\"col-12 lg:col-3 md:col-3\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">S4/HANA ID</label>\r\n                            <input pInputText id=\"username\" formControlName=\"s4_hana_id\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div> -->\r\n                    <div class=\"col-12 lg:col-3 md:col-3\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Street / House Number</label>\r\n                            <input pInputText id=\"username\" formControlName=\"street\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-3 md:col-3 pt-0\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">City</label>\r\n                            <input pInputText id=\"username\" formControlName=\"city\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-3 md:col-3 pt-0\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">State</label>\r\n                            <input pInputText id=\"username\" formControlName=\"state\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-3 md:col-3 pt-0\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Zip Code</label>\r\n                            <input pInputText id=\"username\" formControlName=\"zip_code\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-3 md:col-3 pt-0\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Country</label>\r\n                            <input pInputText id=\"username\" formControlName=\"country\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-3 md:col-3 pt-0\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Email</label>\r\n                            <input pInputText id=\"username\" formControlName=\"email\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-500\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-3 md:col-3 pt-0\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Telephone</label>\r\n                            <input pInputText id=\"username\" formControlName=\"phone\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-primary\" />\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-3 md:col-3 pt-0\">\r\n                        <div class=\"flex flex-column gap-2\">\r\n                            <label for=\"username\" class=\"text-500 font-medium\">Invoice #</label>\r\n                            <input pInputText id=\"username\" formControlName=\"invoice_no\"\r\n                                class=\"surface-b h-2-8rem w-full font-semibold text-primary\" />\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </form>\r\n        <div class=\"acc-title pb-3 flex align-items-center justify-content-between\">\r\n            <div class=\"flex align-items-center gap-3\">\r\n                <p-button [outlined]=\"true\" severity=\"success\" (click)=\"search()\" [disabled]=\"loading\"\r\n                    [styleClass]=\"'flex align-items-center justify-content-center gap-1 text-primary'\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">search</i> {{ loading ? 'Searching...': 'Search'}}\r\n                </p-button>\r\n                <p-button [outlined]=\"true\" (click)=\"reset()\"\r\n                    [styleClass]=\"'w-6rem flex align-items-center justify-content-center gap-1 text-red-600'\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">cancel</i> Clear\r\n                </p-button>\r\n                <!-- <p-button [outlined]=\"true\"\r\n                    [styleClass]=\"'w-6rem flex align-items-center justify-content-center gap-1 text-indigo-400'\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">rule_settings</i> Reset\r\n                </p-button> -->\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"search-result mt-3 w-full\">\r\n        <div\r\n            class=\"acc-title mb-3 pb-3 flex align-items-center justify-content-between border-none border-bottom-1 border-solid border-gray-50\">\r\n            <h5 class=\"header-title m-0 pl-3 relative\">Result List</h5>\r\n            <div class=\"flex align-items-center gap-3\">\r\n                <p-button [outlined]=\"true\"\r\n                    [styleClass]=\"'w-7rem flex align-items-center justify-content-center gap-2 text-color-secondary'\">\r\n                    <i class=\"material-symbols-rounded text-2xl\">check_circle</i> Confirm\r\n                </p-button>\r\n            </div>\r\n        </div>\r\n\r\n        <p *ngIf=\"!data.length && !loading\">No records found.</p>\r\n\r\n        <p-accordion class=\"w-full\" expandIcon=\"pi pi-angle-down\" collapseIcon=\"pi pi-angle-up\">\r\n            <p-accordionTab *ngFor=\"let item of data\">\r\n                <ng-template pTemplate=\"header\">\r\n                    <div class=\"flex gap-3 w-full\">\r\n                        <div class=\"user-box flex align-items-center gap-2 min-width\">\r\n                            <span\r\n                                class=\"flex align-items-center justify-content-center w-2rem h-2rem bg-blue-500 border-circle font-semibold text-white\">{{ getInitials(item.bp_full_name) }}</span>\r\n                            <h5 class=\"m-0 text-base text-900 w-20rem text-overflow-ellipsis\">{{ item.bp_full_name }}</h5>\r\n                        </div>\r\n                        <div class=\"relative flex gap-3 pl-3 flex-1 justify-content-between\">\r\n                            <div class=\"relative flex-1 flex flex-column gap-1\">\r\n                                <span class=\"m-0 text-sm font-normal\">Account Name :</span>\r\n                                <h6 class=\"m-0 text-sm font-semibold\">Marriott</h6>\r\n                            </div>\r\n                            <div class=\"relative flex-1 flex flex-column gap-1\">\r\n                                <span class=\"m-0 text-sm font-normal\">Email :</span>\r\n                                <h6 class=\"m-0 text-sm font-semibold\">marriot&#x40;email.com</h6>\r\n                            </div>\r\n                            <div class=\"relative flex-1 flex flex-column gap-1\">\r\n                                <span class=\"m-0 text-sm font-normal\">Phone :</span>\r\n                                <h6 class=\"m-0 text-sm font-semibold\">****** 525 5265</h6>\r\n                            </div>\r\n                            <div class=\"relative flex-1 flex flex-column gap-1\">\r\n                                <span class=\"m-0 text-sm font-normal\">Address :</span>\r\n                                <h6 class=\"m-0 text-sm font-semibold\">8400 Costa Verde Dr, Myrtle Beach, SC 29572...\r\n                                </h6>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </ng-template>\r\n                <div class=\"table-sec\">\r\n                    <!-- <p-table [value]=\"tableData\" dataKey=\"id\" [rows]=\"8\" styleClass=\"\" [paginator]=\"true\"\r\n                        responsiveLayout=\"scroll\" class=\"w-full\">\r\n                        <ng-template pTemplate=\"header\">\r\n                            <tr>\r\n                                <th class=\"border-round-left-lg\"></th>\r\n                                <th>ID #</th>\r\n                                <th>First name</th>\r\n                                <th>Last name</th>\r\n                                <th>Email Id</th>\r\n                                <th>Phone no</th>\r\n                                <th class=\"border-round-right-lg\">Status</th>\r\n                            </tr>\r\n                        </ng-template>\r\n\r\n                        <ng-template pTemplate=\"body\" let-tableinfo>\r\n                            <tr>\r\n                                <td class=\"border-round-left-lg\">\r\n                                    <input type=\"radio\" class=\"custom-ratio-btn\" />\r\n                                </td>\r\n                                <td class=\"text-orange-600 cursor-pointer font-medium underline\"\r\n                                    [routerLink]=\"'/store/sales-quotes/overview'\">\r\n                                    {{ tableinfo.Id }}\r\n                                </td>\r\n                                <td>\r\n                                    {{ tableinfo.Firstname }}\r\n                                </td>\r\n                                <td>\r\n                                    {{ tableinfo.Lastname }}\r\n                                </td>\r\n                                <td>\r\n                                    {{ tableinfo.EmailId }}\r\n                                </td>\r\n                                <td>\r\n                                    {{ tableinfo.Phoneno }}\r\n                                </td>\r\n                                <td class=\"border-round-right-lg\">\r\n                                    {{ tableinfo.Status }}\r\n                                </td>\r\n                            </tr>\r\n                        </ng-template>\r\n                    </p-table> -->\r\n                </div>\r\n            </p-accordionTab>\r\n        </p-accordion>\r\n    </div>\r\n</div>"], "mappings": "AAGA,SAASA,SAAS,QAAQ,IAAI;;;;;;;;;;;;IC6ItBC,EAAA,CAAAC,cAAA,QAAoC;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAOrCH,EAFR,CAAAC,cAAA,cAA+B,cACmC,eAEkE;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvKH,EAAA,CAAAC,cAAA,aAAkE;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAC7FF,EAD6F,CAAAG,YAAA,EAAK,EAC5F;IAGEH,EAFR,CAAAC,cAAA,cAAqE,cACb,eACV;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,cAAsC;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAClDF,EADkD,CAAAG,YAAA,EAAK,EACjD;IAEFH,EADJ,CAAAC,cAAA,eAAoD,gBACV;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpDH,EAAA,CAAAC,cAAA,cAAsC;IAAAD,EAAA,CAAAE,MAAA,yBAAsB;IAChEF,EADgE,CAAAG,YAAA,EAAK,EAC/D;IAEFH,EADJ,CAAAC,cAAA,eAAoD,gBACV;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpDH,EAAA,CAAAC,cAAA,cAAsC;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IACzDF,EADyD,CAAAG,YAAA,EAAK,EACxD;IAEFH,EADJ,CAAAC,cAAA,eAAoD,gBACV;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtDH,EAAA,CAAAC,cAAA,cAAsC;IAAAD,EAAA,CAAAE,MAAA,uDACtC;IAGZF,EAHY,CAAAG,YAAA,EAAK,EACH,EACJ,EACJ;;;;;IAtB8HH,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,OAAA,CAAAC,YAAA,EAAoC;IAC9FT,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAG,OAAA,CAAAC,YAAA,CAAuB;;;;;IANzGT,EAAA,CAAAC,cAAA,qBAA0C;IACtCD,EAAA,CAAAU,UAAA,IAAAC,iEAAA,2BAAgC;IA4BhCX,EAAA,CAAAY,SAAA,cA0CM;IACVZ,EAAA,CAAAG,YAAA,EAAiB;;;ADjN7B,OAAM,MAAOU,wBAAwB;EAwBnCC,YACUC,QAAmB,EACnBC,EAAe,EACfC,OAAuB;IAFvB,KAAAF,QAAQ,GAARA,QAAQ;IACR,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,OAAO,GAAPA,OAAO;IAzBT,KAAAC,SAAS,GAAG,uBAAuB;IAE3C,KAAAC,KAAK,GAAqB,CACxB;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,UAAU,EAAE,CAAC,yBAAyB;IAAC,CAAE,CACvE;IACD,KAAAC,IAAI,GAAmB;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAChE,KAAAG,UAAU,GAAG,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;MACzBC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,OAAO,EAAE,CAAC,EAAE,CAAC;MACb;MACAC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,UAAU,EAAE,CAAC,EAAE;KAChB,CAAC;IACF,KAAAC,IAAI,GAAU,EAAE;IAChB,KAAAC,OAAO,GAAY,KAAK;IAYxB,KAAAC,OAAO,GAAY,KAAK;EANpB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACxB,QAAQ,CAACyB,QAAQ,CAACC,QAAQ,CAACC,IAAI,EAAE,IAAI,CAACxB,SAAS,CAAC;EACvD;EAIAyB,MAAMA,CAAA;IACJ,MAAMC,GAAG,GAAQ;MACfC,QAAQ,EAAE,CAAC,OAAO,CAAC;MACnBC,MAAM,EAAE,CAAC,OAAO,EACd;QACEC,KAAK,EAAE,CAAC,SAAS;OAClB,CACF;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,CACJ;UACEF,KAAK,EAAE;YACLG,OAAO,EAAE;cACPC,GAAG,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ;;;SAGvC;;KAGN;IACD,IAAI,IAAI,CAAC3B,UAAU,CAAC4B,KAAK,CAAC1B,KAAK,EAAE;MAC/BkB,GAAG,CAACI,OAAO,CAACC,IAAI,CAACI,IAAI,CAAC;QACpB,OAAO,EAAE;UACPC,GAAG,EAAE,IAAI,CAAC9B,UAAU,CAAC4B,KAAK,CAAC1B,KAAK,IAAI;;OAEvC,CAAC;IACJ;IACA,IAAI,IAAI,CAACF,UAAU,CAAC4B,KAAK,CAACzB,OAAO,EAAE;MACjCiB,GAAG,CAACI,OAAO,CAACC,IAAI,CAACI,IAAI,CAAC;QACpB,cAAc,EAAE;UACdE,UAAU,EAAE,IAAI,CAAC/B,UAAU,CAAC4B,KAAK,CAACzB,OAAO,IAAI;;OAEhD,CAAC;IACJ;IACA,IAAI,IAAI,CAACH,UAAU,CAAC4B,KAAK,CAACxB,MAAM,EAAE;MAChCgB,GAAG,CAACI,OAAO,CAACC,IAAI,CAACI,IAAI,CAAC;QACpB,gBAAgB,EAAE;UAChBG,wBAAwB,EAAE;YACxBC,WAAW,EAAE;cACXF,UAAU,EAAE,IAAI,CAAC/B,UAAU,CAAC4B,KAAK,CAACxB,MAAM,IAAI;;;;OAInD,CAAC;IACJ;IACA,IAAI,IAAI,CAACJ,UAAU,CAAC4B,KAAK,CAACvB,IAAI,EAAE;MAC9Be,GAAG,CAACI,OAAO,CAACC,IAAI,CAACI,IAAI,CAAC;QACpB,gBAAgB,EAAE;UAChBG,wBAAwB,EAAE;YACxBE,SAAS,EAAE;cACTH,UAAU,EAAE,IAAI,CAAC/B,UAAU,CAAC4B,KAAK,CAACvB,IAAI,IAAI;;;;OAIjD,CAAC;IACJ;IACA,IAAI,IAAI,CAACL,UAAU,CAAC4B,KAAK,CAACtB,KAAK,EAAE;MAC/Bc,GAAG,CAACI,OAAO,CAACC,IAAI,CAACI,IAAI,CAAC;QACpB,gBAAgB,EAAE;UAChBG,wBAAwB,EAAE;YACxBG,MAAM,EAAE;cACNJ,UAAU,EAAE,IAAI,CAAC/B,UAAU,CAAC4B,KAAK,CAACtB,KAAK,IAAI;;;;OAIlD,CAAC;IACJ;IACA,IAAI,IAAI,CAACN,UAAU,CAAC4B,KAAK,CAACrB,QAAQ,EAAE;MAClCa,GAAG,CAACI,OAAO,CAACC,IAAI,CAACI,IAAI,CAAC;QACpB,gBAAgB,EAAE;UAChBG,wBAAwB,EAAE;YACxBI,WAAW,EAAE;cACXL,UAAU,EAAE,IAAI,CAAC/B,UAAU,CAAC4B,KAAK,CAACrB,QAAQ,IAAI;;;;OAIrD,CAAC;IACJ;IACA,IAAI,IAAI,CAACP,UAAU,CAAC4B,KAAK,CAACpB,OAAO,EAAE;MACjCY,GAAG,CAACI,OAAO,CAACC,IAAI,CAACI,IAAI,CAAC;QACpB,gBAAgB,EAAE;UAChBG,wBAAwB,EAAE;YACxBxB,OAAO,EAAE;cACPuB,UAAU,EAAE,IAAI,CAAC/B,UAAU,CAAC4B,KAAK,CAACpB,OAAO,IAAI;;;;OAIpD,CAAC;IACJ;IACA,IAAI,IAAI,CAACR,UAAU,CAAC4B,KAAK,CAACnB,KAAK,EAAE;MAC/BW,GAAG,CAACI,OAAO,CAACC,IAAI,CAACI,IAAI,CAAC;QACpB,gBAAgB,EAAE;UAChBG,wBAAwB,EAAE;YACxBK,MAAM,EAAE;cACNC,aAAa,EAAE;gBACbP,UAAU,EAAE,IAAI,CAAC/B,UAAU,CAAC4B,KAAK,CAACnB,KAAK,IAAI;;;;;OAKpD,CAAC;IACJ;IACA,IAAI,IAAI,CAACT,UAAU,CAAC4B,KAAK,CAAClB,KAAK,EAAE;MAC/BU,GAAG,CAACI,OAAO,CAACC,IAAI,CAACI,IAAI,CAAC;QACpB,gBAAgB,EAAE;UAChBG,wBAAwB,EAAE;YACxBO,aAAa,EAAE;cACbC,YAAY,EAAE;gBACZT,UAAU,EAAE,IAAI,CAAC/B,UAAU,CAAC4B,KAAK,CAAClB,KAAK,IAAI;;;;;OAKpD,CAAC;IACJ;IACA,MAAM+B,MAAM,GAAGlE,SAAS,CAAC6C,GAAG,CAAC;IAC7B,IAAI,CAACP,OAAO,GAAG,IAAI;IACnB,IAAI,CAACD,IAAI,GAAG,EAAE;IACd,IAAI,CAACnB,OAAO,CAAC0B,MAAM,CAACsB,MAAM,CAAC,CAACC,SAAS,CAAEC,GAAQ,IAAI;MACjD,IAAI,CAAC/B,IAAI,GAAG+B,GAAG;MACf,IAAI,CAAC9B,OAAO,GAAG,KAAK;IACtB,CAAC,EAAE,MAAK;MACN,IAAI,CAACA,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC;EACJ;EAEA+B,KAAKA,CAAA;IACH,IAAI,CAAC5C,UAAU,CAAC4C,KAAK,EAAE;EACzB;EAEA7D,WAAWA,CAAC8D,IAAY;IACtB,OAAOA,IAAI,CACRC,IAAI,EAAE,CACNC,KAAK,CAAC,KAAK,CAAC,CAAC;IAAA,CACbC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAAA,CACZC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,CAAC,CAClCC,IAAI,CAAC,EAAE,CAAC;EACb;;;uBA7KW/D,wBAAwB,EAAAb,EAAA,CAAA6E,iBAAA,CAAA7E,EAAA,CAAA8E,SAAA,GAAA9E,EAAA,CAAA6E,iBAAA,CAAAE,EAAA,CAAAC,WAAA,GAAAhF,EAAA,CAAA6E,iBAAA,CAAAI,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAxBrE,wBAAwB;MAAAsE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR7BzF,EAFR,CAAAC,cAAA,aAA8D,aACL,aACrB;UACxBD,EAAA,CAAAY,SAAA,sBAAqF;UACzFZ,EAAA,CAAAG,YAAA,EAAM;UAMUH,EALhB,CAAAC,cAAA,aAAuB,aACU,aAEuG,YAC3C,WACzC;UAAAD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,kBACnD;UAERF,EAFQ,CAAAG,YAAA,EAAK,EACH,EACJ;UACNH,EAAA,CAAAC,cAAA,cAA6B;UACzBD,EAAA,CAAAY,SAAA,cAEM;UAGlBZ,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;UAIEH,EAHR,CAAAC,cAAA,cAAwF,eAEoD,cACzF;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAOtDF,EAPsD,CAAAG,YAAA,EAAK,EAOrD;UAMcH,EALpB,CAAAC,cAAA,gBAAsF,eACnD,cACJ,eACmB,eACE,iBACmB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvEH,EAAA,CAAAY,SAAA,iBAC+D;UAEvEZ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAsC,eACE,iBACmB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjEH,EAAA,CAAAY,SAAA,iBAC+D;UAEvEZ,EADI,CAAAG,YAAA,EAAM,EACJ;UAUEH,EAFR,CAAAC,cAAA,eAAsC,eACE,iBACmB;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChFH,EAAA,CAAAY,SAAA,iBAC+D;UAEvEZ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAA2C,eACH,iBACmB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC/DH,EAAA,CAAAY,SAAA,iBAC+D;UAEvEZ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAA2C,eACH,iBACmB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChEH,EAAA,CAAAY,SAAA,iBAC+D;UAEvEZ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAA2C,eACH,iBACmB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnEH,EAAA,CAAAY,SAAA,iBAC+D;UAEvEZ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAA2C,eACH,iBACmB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClEH,EAAA,CAAAY,SAAA,iBAC+D;UAEvEZ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAA2C,eACH,iBACmB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChEH,EAAA,CAAAY,SAAA,iBAC+D;UAEvEZ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAA2C,eACH,iBACmB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpEH,EAAA,CAAAY,SAAA,iBACmE;UAE3EZ,EADI,CAAAG,YAAA,EAAM,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAA2C,eACH,iBACmB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpEH,EAAA,CAAAY,SAAA,iBACmE;UAKvFZ,EAJgB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACH;UAGCH,EAFR,CAAAC,cAAA,eAA4E,eAC7B,oBAEgD;UADxCD,EAAA,CAAA2F,UAAA,mBAAAC,6DAAA;YAAA,OAASF,GAAA,CAAA/C,MAAA,EAAQ;UAAA,EAAC;UAE7D3C,EAAA,CAAAC,cAAA,aAA6C;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,IAC5D;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACXH,EAAA,CAAAC,cAAA,oBAC8F;UADlED,EAAA,CAAA2F,UAAA,mBAAAE,6DAAA;YAAA,OAASH,GAAA,CAAAtB,KAAA,EAAO;UAAA,EAAC;UAEzCpE,EAAA,CAAAC,cAAA,aAA6C;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,eAC5D;UAOZF,EAPY,CAAAG,YAAA,EAAW,EAKT,EACJ,EACJ;UAIEH,EAHR,CAAAC,cAAA,eAAuC,eAEqG,cACzF;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAInDH,EAHR,CAAAC,cAAA,eAA2C,oBAE+D,aACrD;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,iBAClE;UAERF,EAFQ,CAAAG,YAAA,EAAW,EACT,EACJ;UAENH,EAAA,CAAAU,UAAA,KAAAoF,sCAAA,gBAAoC;UAEpC9F,EAAA,CAAAC,cAAA,uBAAwF;UACpFD,EAAA,CAAAU,UAAA,KAAAqF,mDAAA,6BAA0C;UA2EtD/F,EAFQ,CAAAG,YAAA,EAAc,EACZ,EACJ;;;UA3NoBH,EAAA,CAAAI,SAAA,GAAe;UAAeJ,EAA9B,CAAAgG,UAAA,UAAAN,GAAA,CAAAvE,KAAA,CAAe,SAAAuE,GAAA,CAAApE,IAAA,CAAc,uCAAuC;UA6BzBtB,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAgG,UAAA,cAAAN,GAAA,CAAAlE,UAAA,CAAwB;UAqFnExB,EAAA,CAAAI,SAAA,IAAiB;UACvBJ,EADM,CAAAgG,UAAA,kBAAiB,aAAAN,GAAA,CAAArD,OAAA,CAA2D,mFACA;UAC1BrC,EAAA,CAAAI,SAAA,GAC5D;UAD4DJ,EAAA,CAAAiG,kBAAA,MAAAP,GAAA,CAAArD,OAAA,kCAC5D;UACUrC,EAAA,CAAAI,SAAA,EAAiB;UACvBJ,EADM,CAAAgG,UAAA,kBAAiB,0FACkE;UAenFhG,EAAA,CAAAI,SAAA,GAAiB;UACvBJ,EADM,CAAAgG,UAAA,kBAAiB,kGAC0E;UAMzGhG,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAgG,UAAA,UAAAN,GAAA,CAAAtD,IAAA,CAAA8D,MAAA,KAAAR,GAAA,CAAArD,OAAA,CAA8B;UAGGrC,EAAA,CAAAI,SAAA,GAAO;UAAPJ,EAAA,CAAAgG,UAAA,YAAAN,GAAA,CAAAtD,IAAA,CAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}