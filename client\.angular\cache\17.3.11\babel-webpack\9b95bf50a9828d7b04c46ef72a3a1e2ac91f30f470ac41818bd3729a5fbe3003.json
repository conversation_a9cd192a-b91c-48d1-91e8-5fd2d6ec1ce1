{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"./reset-password.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c1 = () => [\"/auth/login\"];\nfunction ResetPasswordComponent_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_16_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must be at least 8 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one number\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_16_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Letter in Capital Case\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_16_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Letter in Small Case\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_16_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Special Character\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, ResetPasswordComponent_div_16_div_1_Template, 2, 0, \"div\", 20)(2, ResetPasswordComponent_div_16_div_2_Template, 2, 0, \"div\", 20)(3, ResetPasswordComponent_div_16_div_3_Template, 2, 0, \"div\", 20)(4, ResetPasswordComponent_div_16_div_4_Template, 2, 0, \"div\", 20)(5, ResetPasswordComponent_div_16_div_5_Template, 2, 0, \"div\", 20)(6, ResetPasswordComponent_div_16_div_6_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"minlength\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"hasNumber\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"hasCapitalCase\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"hasSmallCase\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"hasSpecialCharacters\"]);\n  }\n}\nfunction ResetPasswordComponent_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_26_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Passwords must match \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, ResetPasswordComponent_div_26_div_1_Template, 2, 0, \"div\", 20)(2, ResetPasswordComponent_div_26_div_2_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"passwordConfirm\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"passwordConfirm\"].errors[\"confirmedValidator\"]);\n  }\n}\nfunction ConfirmedValidator(controlName, matchingControlName) {\n  return formGroup => {\n    const control = formGroup.controls[controlName];\n    const matchingControl = formGroup.controls[matchingControlName];\n    if (matchingControl.errors && !matchingControl.errors['confirmedValidator']) {\n      return;\n    }\n    if (control.value !== matchingControl.value) {\n      matchingControl.setErrors({\n        confirmedValidator: true\n      });\n    } else {\n      matchingControl.setErrors(null);\n    }\n  };\n}\nfunction patternValidator(regex, error) {\n  return control => {\n    if (!control.value) {\n      // if control is empty return no error\n      return null;\n    }\n    // test the value of the control against the regexp supplied\n    const valid = regex.test(control.value);\n    // if true, return no error (no error), else return error passed in the second parameter\n    return valid ? null : error;\n  };\n}\nexport let ResetPasswordComponent = /*#__PURE__*/(() => {\n  class ResetPasswordComponent {\n    constructor(formBuilder, service, route, router) {\n      this.formBuilder = formBuilder;\n      this.service = service;\n      this.route = route;\n      this.router = router;\n      this.form = this.formBuilder.group({\n        password: ['', [Validators.required, Validators.minLength(8),\n        // check whether the entered password has a number\n        patternValidator(/\\d/, {\n          hasNumber: true\n        }),\n        // check whether the entered password has upper case letter\n        patternValidator(/[A-Z]/, {\n          hasCapitalCase: true\n        }),\n        // check whether the entered password has a lower case letter\n        patternValidator(/[a-z]/, {\n          hasSmallCase: true\n        }),\n        // check whether the entered password has a special character\n        patternValidator(/[ !@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/, {\n          hasSpecialCharacters: true\n        })]],\n        passwordConfirm: ['', Validators.required]\n      }, {\n        validators: ConfirmedValidator('password', 'passwordConfirm')\n      });\n      this.submitted = false;\n      this.saving = false;\n      this.token = '';\n    }\n    ngOnInit() {\n      this.route.queryParams.subscribe(params => {\n        this.token = params['code'];\n      });\n    }\n    get f() {\n      return this.form.controls;\n    }\n    onSubmit() {\n      this.submitted = true;\n      if (this.form.invalid) {\n        return;\n      }\n      if (!this.token) {\n        // this._snackBar.open('Invalid Url.', { type: 'Warning' });\n        return;\n      }\n      this.saving = true;\n      this.service.resetPassword({\n        passwordConfirmation: this.form.value.password,\n        password: this.form.value.password,\n        code: this.token\n      }).subscribe({\n        complete: () => {\n          this.onReset();\n          this.saving = false;\n          // this._snackBar.open('Password reset successfully!');\n        },\n        error: err => {\n          this.saving = false;\n          // this._snackBar.open(err?.error?.message || 'Error while processing your request.', { type: 'Error' });\n        }\n      });\n    }\n    onReset() {\n      this.submitted = false;\n      this.form.reset();\n      setTimeout(() => {\n        this.router.navigate([\"store\"]);\n      }, 2000);\n    }\n    static {\n      this.ɵfac = function ResetPasswordComponent_Factory(t) {\n        return new (t || ResetPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ResetPasswordService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ResetPasswordComponent,\n        selectors: [[\"app-reset-password\"]],\n        decls: 35,\n        vars: 12,\n        consts: [[1, \"login-sec\", \"bg-white\", \"min-h-screen\", \"flex\", \"align-items-center\", \"lg:pt-6\", \"pb-6\", \"md:pt-4\", \"pb-4\"], [1, \"login-page-body\", \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-8\", \"m-auto\", \"h-full\", \"px-5\"], [1, \"login-form\", \"mx-auto\", \"p-5\", \"w-full\", \"bg-white\", \"border-round-3xl\", \"shadow-2\"], [1, \"flex\", \"flex-column\", \"position-relative\", 3, \"formGroup\"], [1, \"mb-2\", \"flex\", \"justify-content-center\", \"text-4xl\", \"font-bold\", \"text-primary\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\"], [1, \"field\", \"col-12\", \"mb-0\"], [1, \"text-base\", \"font-medium\", \"text-gray-600\"], [1, \"form-group\", \"relative\"], [1, \"relative\"], [\"type\", \"password\", \"formControlName\", \"password\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\", 3, \"ngClass\"], [\"type\", \"button\", 1, \"pass-show-btn\", \"absolute\", \"top-0\", \"bottom-0\", \"m-auto\", \"p-0\", \"border-none\", \"bg-white-alpha-10\", \"text-gray-500\", \"cursor-pointer\"], [1, \"material-symbols-rounded\"], [\"class\", \"invalid-feedback text-red-500\", 4, \"ngIf\"], [\"type\", \"password\", \"formControlName\", \"passwordConfirm\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\", 3, \"ngClass\"], [1, \"form-footer\", \"mt-4\"], [1, \"field\", \"col-12\", \"md:col-6\", \"mb-0\"], [\"type\", \"button\", 1, \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"justify-content-center\", \"h-3-3rem\", \"font-semibold\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"h-3-3rem\", \"justify-content-center\", \"gap-3\", \"font-semibold\", \"border-2\", \"border-black-alpha-20\", 3, \"routerLink\"], [1, \"invalid-feedback\", \"text-red-500\"], [4, \"ngIf\"]],\n        template: function ResetPasswordComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"form\", 3)(4, \"h1\", 4);\n            i0.ɵɵtext(5, \"Reset Password \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"label\", 7);\n            i0.ɵɵtext(9, \"Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9);\n            i0.ɵɵelement(12, \"input\", 10);\n            i0.ɵɵelementStart(13, \"button\", 11)(14, \"span\", 12);\n            i0.ɵɵtext(15, \"visibility\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(16, ResetPasswordComponent_div_16_Template, 7, 6, \"div\", 13);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 6)(18, \"label\", 7);\n            i0.ɵɵtext(19, \"Retype Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"div\", 8)(21, \"div\", 9);\n            i0.ɵɵelement(22, \"input\", 14);\n            i0.ɵɵelementStart(23, \"button\", 11)(24, \"span\", 12);\n            i0.ɵɵtext(25, \"visibility\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(26, ResetPasswordComponent_div_26_Template, 3, 2, \"div\", 13);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(27, \"div\", 15)(28, \"div\", 5)(29, \"div\", 16)(30, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function ResetPasswordComponent_Template_button_click_30_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtext(31, \" Login\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"div\", 16)(33, \"button\", 18);\n            i0.ɵɵtext(34, \" Cancel \");\n            i0.ɵɵelementEnd()()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"formGroup\", ctx.form);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx.submitted && ctx.f[\"password\"].errors));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"password\"].errors);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx.submitted && ctx.f[\"passwordConfirm\"].errors));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"passwordConfirm\"].errors);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.saving);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(11, _c1));\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n        styles: [\".login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]{max-width:1440px}.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]{max-width:480px!important}.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .pass-show-btn[_ngcontent-%COMP%]{right:12px;height:24px;width:24px}.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check-box[_ngcontent-%COMP%]{accent-color:var(--primarycolor)}.p-inputtext[_ngcontent-%COMP%]{height:3rem;appearance:auto!important}.h-3-3rem[_ngcontent-%COMP%]{height:3.3rem}.min-h-screen[_ngcontent-%COMP%]{min-height:100vh!important}\"]\n      });\n    }\n  }\n  return ResetPasswordComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}