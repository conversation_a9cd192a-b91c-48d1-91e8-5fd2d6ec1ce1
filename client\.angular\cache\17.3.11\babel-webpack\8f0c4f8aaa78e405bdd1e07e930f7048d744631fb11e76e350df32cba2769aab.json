{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"./users.service\";\nimport * as i3 from \"src/app/core/authentication/auth.service\";\nimport * as i4 from \"@angular/common\";\nfunction ProfileComponent_div_12_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter a valid email address.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, ProfileComponent_div_12_div_10_div_1_Template, 2, 0, \"div\", 23)(2, ProfileComponent_div_12_div_10_div_2_Template, 2, 0, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"email\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"email\"].errors[\"email\"]);\n  }\n}\nfunction ProfileComponent_div_12_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, ProfileComponent_div_12_div_19_div_1_Template, 2, 0, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"firstName\"].errors[\"required\"]);\n  }\n}\nfunction ProfileComponent_div_12_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, ProfileComponent_div_12_div_28_div_1_Template, 2, 0, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"lastName\"].errors[\"required\"]);\n  }\n}\nfunction ProfileComponent_div_12_div_37_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, ProfileComponent_div_12_div_37_div_1_Template, 2, 0, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"phone\"].errors[\"required\"]);\n  }\n}\nfunction ProfileComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"form\", 11);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_12_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveChanges());\n    });\n    i0.ɵɵelementStart(2, \"div\", 12)(3, \"label\", 13)(4, \"span\", 8);\n    i0.ɵɵtext(5, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" User Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\")(8, \"div\", 14);\n    i0.ɵɵelement(9, \"input\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, ProfileComponent_div_12_div_10_Template, 3, 2, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 12)(12, \"label\", 13)(13, \"span\", 8);\n    i0.ɵɵtext(14, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\")(17, \"div\", 14);\n    i0.ɵɵelement(18, \"input\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ProfileComponent_div_12_div_19_Template, 2, 1, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 12)(21, \"label\", 13)(22, \"span\", 8);\n    i0.ɵɵtext(23, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24, \" Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\")(26, \"div\", 14);\n    i0.ɵɵelement(27, \"input\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, ProfileComponent_div_12_div_28_Template, 2, 1, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 12)(30, \"label\", 13)(31, \"span\", 8);\n    i0.ɵɵtext(32, \"phone_in_talk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33, \" Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\")(35, \"div\", 14);\n    i0.ɵɵelement(36, \"input\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(37, ProfileComponent_div_12_div_37_Template, 2, 1, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 20)(39, \"button\", 21);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.profileForm);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFormSubmitted && ctx_r1.f[\"email\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFormSubmitted && ctx_r1.f[\"firstName\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFormSubmitted && ctx_r1.f[\"lastName\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFormSubmitted && ctx_r1.f[\"phone\"].errors);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.saving ? \"Saving\" : \"Save Changes\");\n  }\n}\nexport let ProfileComponent = /*#__PURE__*/(() => {\n  class ProfileComponent {\n    constructor(fb, manageUserService, authService) {\n      this.fb = fb;\n      this.manageUserService = manageUserService;\n      this.authService = authService;\n      this.toggle = {\n        profile: true,\n        password: false\n      };\n      this.isFormSubmitted = false;\n      this.isPasswordFormSubmitted = false;\n      this.loadingUserDetails = false;\n      this.userDetails = {};\n      this.saving = false;\n      this.changingPassword = false;\n    }\n    ngOnInit() {\n      this.profileForm = this.fb.group({\n        firstName: ['', Validators.required],\n        lastName: ['', Validators.required],\n        email: ['', [Validators.required, Validators.email]],\n        phone: ['']\n      });\n      this.fetchUserDetails();\n    }\n    get f() {\n      return this.profileForm.controls;\n    }\n    saveChanges() {\n      this.isFormSubmitted = true;\n      if (!this.profileForm.valid) {\n        return;\n      }\n      const value = this.profileForm.value;\n      this.saving = true;\n      const isAdmin = this.authService.userDetail.isAdmin || false;\n      if (isAdmin) {\n        this.manageUserService.updateAdminUser(this.userDetails.id, {\n          firstname: value.firstName,\n          lastname: value.lastName,\n          address: value.address,\n          email: value.email,\n          phone: value.phone\n        }).subscribe(user => {\n          this.saving = false;\n          this.userDetails = user;\n          this.authService.checkAdminUser().subscribe();\n        }, err => {\n          this.saving = false;\n        });\n      } else {\n        this.manageUserService.updateUser(this.userDetails.id, {\n          firstname: value.firstName,\n          lastname: value.lastName,\n          address: value.address,\n          email: value.email\n        }).subscribe(user => {\n          this.saving = false;\n          this.userDetails = user;\n          this.authService.checkAdminUser().subscribe();\n        }, err => {\n          this.saving = false;\n        });\n      }\n    }\n    fetchUserDetails() {\n      this.loadingUserDetails = true;\n      const user = this.authService.userDetail;\n      this.manageUserService.getUserForallRoles(user.documentId).subscribe(user => {\n        if (user) {\n          this.userDetails = user;\n          this.profileForm.patchValue({\n            firstName: user.firstname,\n            lastName: user.lastname,\n            address: user.address,\n            email: user.email,\n            phone: user.phone\n          });\n        }\n        this.loadingUserDetails = false;\n      });\n    }\n    toggleState(key) {\n      this.toggle[key] = !this.toggle[key];\n    }\n    static {\n      this.ɵfac = function ProfileComponent_Factory(t) {\n        return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.UsersService), i0.ɵɵdirectiveInject(i3.AuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProfileComponent,\n        selectors: [[\"app-profile\"]],\n        decls: 13,\n        vars: 3,\n        consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-4\", \"border-round-lg\", \"surface-card\"], [1, \"all-page-title-sec\", \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"m-0\"], [1, \"card-list\", \"flex\", \"flex-column\", \"gap-2\", \"mb-3\"], [1, \"card\", \"shadow-2\", \"p-4\", \"h-full\", \"flex\", \"flex-column\", \"border-left-5\", \"gap-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"cursor-pointer\", 3, \"click\"], [1, \"block\", \"font-bold\", \"text-xl\", \"m-0\", \"text-primary\"], [1, \"show-hide-btn\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-rounded\", \"p-button-outlined\", \"justify-content-center\", \"w-2rem\", \"h-2rem\"], [1, \"material-symbols-rounded\"], [\"class\", \"v-details-sec mt-3 pt-5 border-solid border-black-alpha-20 border-none border-top-1\", 4, \"ngIf\"], [1, \"v-details-sec\", \"mt-3\", \"pt-5\", \"border-solid\", \"border-black-alpha-20\", \"border-none\", \"border-top-1\"], [\"autocomplete\", \"off\", 1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"ngSubmit\", \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-15rem\", \"gap-1\"], [1, \"form-input\"], [\"type\", \"email\", \"formControlName\", \"email\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"class\", \"text-red-600 mt-2\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"firstName\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"type\", \"text\", \"formControlName\", \"lastName\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"type\", \"text\", \"formControlName\", \"phone\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [1, \"form-submit-sec\", \"mt-5\"], [\"type\", \"submit\", 1, \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-rounded\", \"justify-content-center\", \"w-12rem\", \"h-3rem\"], [1, \"text-red-600\", \"mt-2\"], [4, \"ngIf\"]],\n        template: function ProfileComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n            i0.ɵɵtext(3, \"My Account\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5);\n            i0.ɵɵlistener(\"click\", function ProfileComponent_Template_div_click_6_listener() {\n              return ctx.toggleState(\"profile\");\n            });\n            i0.ɵɵelementStart(7, \"h3\", 6);\n            i0.ɵɵtext(8, \"User Details\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"button\", 7)(10, \"span\", 8);\n            i0.ɵɵtext(11, \"keyboard_arrow_down\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(12, ProfileComponent_div_12_Template, 41, 6, \"div\", 9);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵclassProp(\"active\", ctx.toggle[\"profile\"]);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.toggle[\"profile\"]);\n          }\n        },\n        dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName]\n      });\n    }\n  }\n  return ProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}