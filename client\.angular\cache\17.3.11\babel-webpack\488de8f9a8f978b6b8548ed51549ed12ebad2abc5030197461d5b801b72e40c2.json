{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { Subject, takeUntil, concat, map, of, forkJoin } from 'rxjs';\nimport { distinctUntilChanged, switchMap, tap, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"../contacts.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@ng-select/ng-select\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/inputtext\";\nimport * as i10 from \"primeng/toast\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction AddContactComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" First Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddContactComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, AddContactComponent_div_15_div_1_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"first_name\"].errors[\"required\"]);\n  }\n}\nfunction AddContactComponent_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Last Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddContactComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, AddContactComponent_div_25_div_1_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"last_name\"].errors[\"required\"]);\n  }\n}\nfunction AddContactComponent_ng_template_36_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" : \", item_r2.bp_full_name, \"\");\n  }\n}\nfunction AddContactComponent_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, AddContactComponent_ng_template_36_span_2_Template, 2, 1, \"span\", 20);\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r2.bp_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.bp_full_name);\n  }\n}\nfunction AddContactComponent_div_37_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Account is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddContactComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, AddContactComponent_div_37_div_1_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"bp_id\"].errors[\"required\"]);\n  }\n}\nfunction AddContactComponent_div_73_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \" Please enter a valid Phone number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddContactComponent_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AddContactComponent_div_73_div_1_Template, 2, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.ContactForm.get(\"phone_number\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction AddContactComponent_div_90_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Mobile is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddContactComponent_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, AddContactComponent_div_90_div_1_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"mobile\"].errors[\"required\"]);\n  }\n}\nfunction AddContactComponent_div_91_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \" Please enter a valid Mobile number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddContactComponent_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AddContactComponent_div_91_div_1_Template, 2, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.ContactForm.get(\"mobile\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction AddContactComponent_div_101_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddContactComponent_div_101_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is invalid. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddContactComponent_div_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, AddContactComponent_div_101_div_1_Template, 2, 0, \"div\", 20)(2, AddContactComponent_div_101_div_2_Template, 2, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"email_address\"].errors && ctx_r0.f[\"email_address\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"email_address\"].errors[\"email\"]);\n  }\n}\nexport let AddContactComponent = /*#__PURE__*/(() => {\n  class AddContactComponent {\n    constructor(formBuilder, router, messageservice, contactsservice) {\n      this.formBuilder = formBuilder;\n      this.router = router;\n      this.messageservice = messageservice;\n      this.contactsservice = contactsservice;\n      this.unsubscribe$ = new Subject();\n      this.submitted = false;\n      this.cpDepartments = [];\n      this.cpFunctions = [];\n      this.accountLoading = false;\n      this.accountInput$ = new Subject();\n      this.defaultOptions = [];\n      this.saving = false;\n      this.ContactForm = this.formBuilder.group({\n        first_name: ['', [Validators.required]],\n        last_name: ['', [Validators.required]],\n        bp_id: ['', [Validators.required]],\n        title: [''],\n        job_title: [''],\n        contact_person_function_name: [''],\n        contact_person_department_name: [''],\n        phone_number: ['', [Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\n        fax_number: [''],\n        mobile: ['', [Validators.required, Validators.pattern(/^[\\d\\+\\-\\s]*$/)]],\n        email_address: ['', [Validators.required, Validators.email]]\n      });\n    }\n    ngOnInit() {\n      this.loadAccounts();\n      forkJoin({\n        departments: this.contactsservice.getCPDepartment(),\n        functions: this.contactsservice.getCPFunction()\n      }).pipe(takeUntil(this.unsubscribe$)).subscribe(({\n        departments,\n        functions\n      }) => {\n        // Load departments\n        this.cpDepartments = (departments?.data || []).map(item => ({\n          name: item.description,\n          value: item.code\n        }));\n        // Load functions\n        this.cpFunctions = (functions?.data || []).map(item => ({\n          name: item.description,\n          value: item.code\n        }));\n      });\n    }\n    loadAccounts() {\n      this.accounts$ = concat(of(this.defaultOptions),\n      // Default empty options\n      this.accountInput$.pipe(distinctUntilChanged(), tap(() => this.accountLoading = true), switchMap(term => {\n        const params = {\n          [`filters[roles][bp_role][$eq][0]`]: 'FLCU01',\n          [`filters[roles][bp_role][$eq][1]`]: 'FLCU00',\n          [`fields[0]`]: 'bp_id',\n          [`fields[1]`]: 'first_name',\n          [`fields[2]`]: 'last_name',\n          [`fields[3]`]: 'bp_full_name'\n        };\n        if (term) {\n          params[`filters[$or][0][bp_id][$containsi]`] = term;\n          params[`filters[$or][1][bp_full_name][$containsi]`] = term;\n        }\n        return this.contactsservice.getAccounts(params).pipe(map(data => {\n          return data || []; // Make sure to return correct data structure\n        }), tap(() => this.accountLoading = false), catchError(error => {\n          this.accountLoading = false;\n          return of([]);\n        }));\n      })));\n    }\n    onSubmit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.submitted = true;\n        if (_this.ContactForm.invalid) {\n          console.log('Form is invalid:', _this.ContactForm.errors);\n          return;\n        }\n        _this.saving = true;\n        const value = {\n          ..._this.ContactForm.value\n        };\n        const data = {\n          first_name: value?.first_name || '',\n          middle_name: value?.middle_name,\n          last_name: value?.last_name || '',\n          bp_id: value?.bp_id || '',\n          title: value?.title || '',\n          job_title: value?.job_title || '',\n          contact_person_function_name: value?.contact_person_function_name?.name || '',\n          contact_person_function: value?.contact_person_function_name?.value || '',\n          contact_person_department_name: value?.contact_person_department_name?.name || '',\n          contact_person_department: value?.contact_person_department_name?.value || '',\n          email_address: value?.email_address,\n          phone_number: value?.phone_number,\n          fax_number: value?.fax_number,\n          mobile: value?.mobile\n        };\n        _this.contactsservice.createContact(data).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n          next: response => {\n            if (response?.data?.documentId) {\n              sessionStorage.setItem('contactMessage', 'Contact created successfully!');\n              window.location.href = `${window.location.origin}#/store/contacts/${response?.data?.documentId}/overview`;\n            } else {\n              console.error('Missing documentId in response:', response);\n            }\n          },\n          error: res => {\n            _this.saving = false;\n            _this.messageservice.add({\n              severity: 'error',\n              detail: 'Error while processing your request.'\n            });\n          }\n        });\n      })();\n    }\n    onCancel() {\n      this.router.navigate(['/store/prospects']);\n    }\n    get f() {\n      return this.ContactForm.controls;\n    }\n    ngOnDestroy() {\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    }\n    static {\n      this.ɵfac = function AddContactComponent_Factory(t) {\n        return new (t || AddContactComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ContactsService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AddContactComponent,\n        selectors: [[\"app-add-contact\"]],\n        decls: 106,\n        vars: 38,\n        consts: [[\"position\", \"top-right\", 3, \"life\"], [3, \"formGroup\"], [1, \"card\", \"shadow-1\", \"border-round-xl\", \"surface-0\", \"p-5\", \"mb-4\", \"border-1\", \"border-solid\", \"border-50\"], [1, \"mb-2\", \"flex\", \"align-items-center\", \"h-3rem\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\", \"sm:col-6\"], [1, \"input-main\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"mb-2\", \"font-medium\"], [1, \"material-symbols-rounded\", \"text-2xl\", \"text-300\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"first_name\", \"type\", \"text\", \"formControlName\", \"first_name\", \"placeholder\", \"First Name\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"pInputText\", \"\", \"id\", \"last_name\", \"type\", \"text\", \"formControlName\", \"last_name\", \"placeholder\", \"Last Name\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"pInputText\", \"\", \"bindLabel\", \"bp_full_name\", \"bindValue\", \"bp_id\", \"formControlName\", \"bp_id\", \"appendTo\", \"body\", 3, \"items\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"ngClass\"], [\"ng-option-tmp\", \"\"], [\"pInputText\", \"\", \"id\", \"title\", \"type\", \"text\", \"formControlName\", \"title\", \"placeholder\", \"Title\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"job_title\", \"type\", \"text\", \"formControlName\", \"job_title\", \"placeholder\", \"Job Title\", 1, \"h-3rem\", \"w-full\"], [\"formControlName\", \"contact_person_function_name\", \"optionLabel\", \"name\", \"dataKey\", \"value\", \"placeholder\", \"Select Function\", 3, \"options\", \"styleClass\"], [\"formControlName\", \"contact_person_department_name\", \"optionLabel\", \"name\", \"dataKey\", \"value\", \"placeholder\", \"Select Department\", 3, \"options\", \"styleClass\"], [\"pInputText\", \"\", \"id\", \"phone_number\", \"type\", \"text\", \"formControlName\", \"phone_number\", \"placeholder\", \"Phone\", 1, \"h-3rem\", \"w-full\"], [4, \"ngIf\"], [\"pInputText\", \"\", \"id\", \"fax_number\", \"type\", \"text\", \"formControlName\", \"fax_number\", \"placeholder\", \"Fax\", 1, \"h-3rem\", \"w-full\"], [\"pInputText\", \"\", \"id\", \"mobile\", \"type\", \"text\", \"formControlName\", \"mobile\", \"placeholder\", \"Mobile\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [\"pInputText\", \"\", \"id\", \"email_address\", \"type\", \"text\", \"formControlName\", \"email_address\", \"placeholder\", \"E-mail\", 1, \"h-3rem\", \"w-full\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"mt-4\", \"ml-auto\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-rounded\", \"bg-light-blue\", \"border-none\", \"text-primary-700\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Create\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"invalid-feedback\"], [\"class\", \"p-error\", 4, \"ngIf\"], [1, \"p-error\"]],\n        template: function AddContactComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"p-toast\", 0);\n            i0.ɵɵelementStart(1, \"form\", 1)(2, \"div\", 2)(3, \"h3\", 3);\n            i0.ɵɵtext(4, \"Create Contact\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5)(7, \"div\", 6)(8, \"label\", 7)(9, \"span\", 8);\n            i0.ɵɵtext(10, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(11, \" First Name \");\n            i0.ɵɵelementStart(12, \"span\", 9);\n            i0.ɵɵtext(13, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(14, \"input\", 10);\n            i0.ɵɵtemplate(15, AddContactComponent_div_15_Template, 2, 1, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 5)(17, \"div\", 6)(18, \"label\", 7)(19, \"span\", 8);\n            i0.ɵɵtext(20, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(21, \" Last Name \");\n            i0.ɵɵelementStart(22, \"span\", 9);\n            i0.ɵɵtext(23, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(24, \"input\", 12);\n            i0.ɵɵtemplate(25, AddContactComponent_div_25_Template, 2, 1, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 5)(27, \"div\", 6)(28, \"label\", 7)(29, \"span\", 8);\n            i0.ɵɵtext(30, \"account_circle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(31, \" Account \");\n            i0.ɵɵelementStart(32, \"span\", 9);\n            i0.ɵɵtext(33, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(34, \"ng-select\", 13);\n            i0.ɵɵpipe(35, \"async\");\n            i0.ɵɵtemplate(36, AddContactComponent_ng_template_36_Template, 3, 2, \"ng-template\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(37, AddContactComponent_div_37_Template, 2, 1, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"div\", 5)(39, \"div\", 6)(40, \"label\", 7)(41, \"span\", 8);\n            i0.ɵɵtext(42, \"title\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(43, \" Title \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(44, \"input\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(45, \"div\", 5)(46, \"div\", 6)(47, \"label\", 7)(48, \"span\", 8);\n            i0.ɵɵtext(49, \"work\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(50, \" Job Title \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(51, \"input\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(52, \"div\", 5)(53, \"div\", 6)(54, \"label\", 7)(55, \"span\", 8);\n            i0.ɵɵtext(56, \"functions\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(57, \" Function \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(58, \"p-dropdown\", 17);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(59, \"div\", 5)(60, \"div\", 6)(61, \"label\", 7)(62, \"span\", 8);\n            i0.ɵɵtext(63, \"inbox_text_person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(64, \" Department \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(65, \"p-dropdown\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(66, \"div\", 5)(67, \"div\", 6)(68, \"label\", 7)(69, \"span\", 8);\n            i0.ɵɵtext(70, \"phone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(71, \" Phone \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(72, \"input\", 19);\n            i0.ɵɵtemplate(73, AddContactComponent_div_73_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(74, \"div\", 5)(75, \"div\", 6)(76, \"label\", 7)(77, \"span\", 8);\n            i0.ɵɵtext(78, \"fax\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(79, \" Fax \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(80, \"input\", 21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(81, \"div\", 5)(82, \"div\", 6)(83, \"label\", 7)(84, \"span\", 8);\n            i0.ɵɵtext(85, \"phone_iphone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(86, \" Mobile \");\n            i0.ɵɵelementStart(87, \"span\", 9);\n            i0.ɵɵtext(88, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(89, \"input\", 22);\n            i0.ɵɵtemplate(90, AddContactComponent_div_90_Template, 2, 1, \"div\", 11)(91, AddContactComponent_div_91_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(92, \"div\", 5)(93, \"div\", 6)(94, \"label\", 7)(95, \"span\", 8);\n            i0.ɵɵtext(96, \"mail\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(97, \" E-mail \");\n            i0.ɵɵelementStart(98, \"span\", 9);\n            i0.ɵɵtext(99, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(100, \"input\", 23);\n            i0.ɵɵtemplate(101, AddContactComponent_div_101_Template, 3, 2, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(102, \"div\", 5);\n            i0.ɵɵelementStart(103, \"div\", 24)(104, \"button\", 25);\n            i0.ɵɵlistener(\"click\", function AddContactComponent_Template_button_click_104_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(105, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function AddContactComponent_Template_button_click_105_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            let tmp_19_0;\n            let tmp_22_0;\n            i0.ɵɵproperty(\"life\", 3000);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.ContactForm);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(28, _c0, ctx.submitted && ctx.f[\"first_name\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"first_name\"].errors);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(30, _c0, ctx.submitted && ctx.f[\"last_name\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"last_name\"].errors);\n            i0.ɵɵadvance(9);\n            i0.ɵɵclassMap(\"multiselect-dropdown p-inputtext p-component p-element\");\n            i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(35, 26, ctx.accounts$))(\"hideSelected\", true)(\"loading\", ctx.accountLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.accountInput$)(\"maxSelectedItems\", 10)(\"ngClass\", i0.ɵɵpureFunction1(32, _c0, ctx.submitted && ctx.f[\"bp_id\"].errors));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"bp_id\"].errors);\n            i0.ɵɵadvance(21);\n            i0.ɵɵproperty(\"options\", ctx.cpFunctions)(\"styleClass\", \"h-3rem w-full\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"options\", ctx.cpDepartments)(\"styleClass\", \"h-3rem w-full\");\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_19_0 = ctx.ContactForm.get(\"phone_number\")) == null ? null : tmp_19_0.touched) && ((tmp_19_0 = ctx.ContactForm.get(\"phone_number\")) == null ? null : tmp_19_0.invalid));\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(34, _c0, ctx.submitted && ctx.f[\"mobile\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"mobile\"].errors);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ((tmp_22_0 = ctx.ContactForm.get(\"mobile\")) == null ? null : tmp_22_0.touched) && ((tmp_22_0 = ctx.ContactForm.get(\"mobile\")) == null ? null : tmp_22_0.invalid));\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(36, _c0, ctx.submitted && ctx.f[\"email_address\"].errors));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"email_address\"].errors);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgIf, i6.NgSelectComponent, i6.NgOptionTemplateDirective, i7.Dropdown, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i8.ButtonDirective, i9.InputText, i10.Toast, i5.AsyncPipe],\n        styles: [\".invalid-feedback[_ngcontent-%COMP%], .p-inputtext[_ngcontent-%COMP%]:invalid, .is-checkbox-invalid[_ngcontent-%COMP%], .p-inputtext.is-invalid[_ngcontent-%COMP%]{color:#dc3545}\"]\n      });\n    }\n  }\n  return AddContactComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}