{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { ActivitiesFormComponent } from 'src/app/store/common-form/activities-form/activities-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/store/activities/activities.service\";\nimport * as i2 from \"../../contacts.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/tooltip\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"../../../common-form/activities-form/activities-form.component\";\nimport * as i11 from \"primeng/multiselect\";\nfunction ContactsActivitiesComponent_ng_template_9_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 18);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_9_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 18);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_9_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 19);\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_9_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 20);\n    i0.ɵɵlistener(\"click\", function ContactsActivitiesComponent_ng_template_9_ng_container_6_Template_th_click_1_listener() {\n      const col_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.customSort(col_r4.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, ContactsActivitiesComponent_ng_template_9_ng_container_6_i_4_Template, 1, 1, \"i\", 15)(5, ContactsActivitiesComponent_ng_template_9_ng_container_6_i_5_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r4.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r4.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === col_r4.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== col_r4.field);\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 13);\n    i0.ɵɵlistener(\"click\", function ContactsActivitiesComponent_ng_template_9_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.customSort(\"subject\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵtext(3, \" Subject \");\n    i0.ɵɵtemplate(4, ContactsActivitiesComponent_ng_template_9_i_4_Template, 1, 1, \"i\", 15)(5, ContactsActivitiesComponent_ng_template_9_i_5_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ContactsActivitiesComponent_ng_template_9_ng_container_6_Template, 6, 4, \"ng-container\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField === \"subject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sortField !== \"subject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 27)(2, \"div\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"slice\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"pTooltip\", ctx_r1.stripHtml(activity_r5 == null ? null : activity_r5.globalNote == null ? null : activity_r5.globalNote.note));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5 == null ? null : activity_r5.globalNote == null ? null : activity_r5.globalNote.note) ? i0.ɵɵpipeBind3(4, 2, ctx_r1.stripHtml(activity_r5.globalNote.note), 0, 80) + (activity_r5.globalNote.note.length > 80 ? \"...\" : \"\") : \"-\", \" \");\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5 == null ? null : activity_r5.business_partner_contact == null ? null : activity_r5.business_partner_contact.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5 == null ? null : activity_r5.start_date) ? i0.ɵɵpipeBind2(2, 1, activity_r5 == null ? null : activity_r5.start_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5 == null ? null : activity_r5.end_date) ? i0.ɵɵpipeBind2(2, 1, activity_r5 == null ? null : activity_r5.end_date, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5 == null ? null : activity_r5.createdAt) ? i0.ɵɵpipeBind2(2, 1, activity_r5 == null ? null : activity_r5.createdAt, \"MM-dd-yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5 == null ? null : activity_r5.business_partner_organizer == null ? null : activity_r5.business_partner_organizer.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityCategory\", activity_r5 == null ? null : activity_r5.phone_call_category) || \"-\", \" \");\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityStatus\", activity_r5 == null ? null : activity_r5.activity_status) || \"-\", \" \");\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityPriority\", activity_r5 == null ? null : activity_r5.priority) || \"-\", \" \");\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5.business_partner == null ? null : activity_r5.business_partner.addresses == null ? null : activity_r5.business_partner.addresses[0] == null ? null : activity_r5.business_partner.addresses[0].phone) || \"-\", \" \");\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getLabelFromDropdown(\"activityDocumentType\", activity_r5 == null ? null : activity_r5.document_type) || \"-\", \" \");\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_10_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 25);\n    i0.ɵɵtemplate(3, ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_3_Template, 5, 6, \"ng-container\", 26)(4, ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 26)(5, ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_5_Template, 3, 4, \"ng-container\", 26)(6, ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_6_Template, 3, 4, \"ng-container\", 26)(7, ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_7_Template, 3, 4, \"ng-container\", 26)(8, ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_8_Template, 2, 1, \"ng-container\", 26)(9, ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_9_Template, 2, 1, \"ng-container\", 26)(10, ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_10_Template, 2, 1, \"ng-container\", 26)(11, ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_11_Template, 2, 1, \"ng-container\", 26)(12, ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_12_Template, 2, 1, \"ng-container\", 26)(13, ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_13_Template, 2, 1, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r6.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"globalNote.note\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_contact.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"start_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"end_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"createdAt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_organizer.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"phone_call_category\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"activity_status\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"priority\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"phone\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"document_type\");\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 21)(1, \"td\", 22)(2, \"div\", 23)(3, \"a\", 24);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, ContactsActivitiesComponent_ng_template_10_ng_container_5_Template, 14, 12, \"ng-container\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const activity_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", \"/#/store/activities/calls/\" + (activity_r5 == null ? null : activity_r5.activity_id) + \"/overview\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (activity_r5 == null ? null : activity_r5.subject) || \"-\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedColumns);\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 29);\n    i0.ɵɵtext(2, \" No activities found. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ContactsActivitiesComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 29);\n    i0.ɵɵtext(2, \" Loading activities data. Please wait... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ContactsActivitiesComponent {\n  constructor(activitiesservice, contactsservice, router, route) {\n    this.activitiesservice = activitiesservice;\n    this.contactsservice = contactsservice;\n    this.router = router;\n    this.route = route;\n    this.unsubscribe$ = new Subject();\n    this.activitiesDetails = [];\n    this.bp_id = '';\n    this.documentId = '';\n    this.dropdowns = {\n      activityCategory: [],\n      activityStatus: [],\n      activityPriority: []\n    };\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'globalNote.note',\n      header: 'Notes'\n    }, {\n      field: 'business_partner_contact.bp_full_name',\n      header: 'Primary Contact'\n    }, {\n      field: 'start_date',\n      header: 'Start Date/Time'\n    }, {\n      field: 'end_date',\n      header: 'End Date/Time'\n    }, {\n      field: 'createdAt',\n      header: 'Created On'\n    }, {\n      field: 'business_partner_organizer.bp_full_name',\n      header: 'Organizer'\n    }, {\n      field: 'phone_call_category',\n      header: 'Category'\n    }, {\n      field: 'activity_status',\n      header: 'Status'\n    }, {\n      field: 'priority',\n      header: 'Priority'\n    }, {\n      field: 'phone',\n      header: 'Phone'\n    }, {\n      field: 'document_type',\n      header: 'Activity Type'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.activitiesDetails.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_PHONE_CALL_CATEGORY');\n    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\n    this.loadActivityDropDown('activityDocumentType', 'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL');\n    this.loadActivityDropDown('activityPriority', 'CRM_ACTIVITY_PRIORITY');\n    this.contactsservice.contact.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.bp_id = response.bp_company_id;\n        this.documentId = response.documentId;\n        if (this.bp_id) {\n          this.activitiesservice.getActivity(this.bp_id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n            next: activityResponse => {\n              if (Array.isArray(activityResponse.data)) {\n                const allActivities = activityResponse.data.slice(0, 10);\n                this.activitiesDetails = allActivities.map(activity => {\n                  // Find global note from activity.notes array\n                  const globalNote = activity.notes?.find(note => note.is_global_note === true);\n                  return {\n                    ...activity,\n                    globalNote: globalNote || null\n                  };\n                });\n              } else {\n                this.activitiesDetails = [];\n              }\n            },\n            error: error => {\n              console.error('Error fetching activities:', error);\n            }\n          });\n        }\n      }\n    });\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  stripHtml(html) {\n    const temp = document.createElement('div');\n    temp.innerHTML = html;\n    return temp.textContent || temp.innerText || '';\n  }\n  loadActivityDropDown(target, type) {\n    this.activitiesservice.getActivityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  openActivityDialog() {\n    this.activityDialog.showDialog('right');\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ContactsActivitiesComponent_Factory(t) {\n      return new (t || ContactsActivitiesComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService), i0.ɵɵdirectiveInject(i2.ContactsService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContactsActivitiesComponent,\n      selectors: [[\"app-contacts-activities\"]],\n      viewQuery: function ContactsActivitiesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ActivitiesFormComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.activityDialog = _t.first);\n        }\n      },\n      decls: 14,\n      vars: 11,\n      consts: [[1, \"p-3\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"ml-auto\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"styleClass\", \"\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [3, \"account_id\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", \"border-round-left-lg\"], [1, \"readonly-field\", \"font-medium\", \"p-2\", \"text-orange-600\", \"cursor-pointer\"], [\"target\", \"_blank\", 2, \"text-decoration\", \"none\", \"color\", \"inherit\", 3, \"href\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"tooltipPosition\", \"top\", \"tooltipStyleClass\", \"multi-line-tooltip\", 3, \"pTooltip\"], [1, \"note-text\"], [\"colspan\", \"12\", 1, \"border-round-left-lg\", \"pl-3\"]],\n      template: function ContactsActivitiesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h4\", 2);\n          i0.ɵɵtext(3, \"Activities\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"p-button\", 4);\n          i0.ɵɵlistener(\"click\", function ContactsActivitiesComponent_Template_p_button_click_5_listener() {\n            return ctx.openActivityDialog();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p-multiSelect\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContactsActivitiesComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-table\", 7);\n          i0.ɵɵlistener(\"onColReorder\", function ContactsActivitiesComponent_Template_p_table_onColReorder_8_listener($event) {\n            return ctx.onColumnReorder($event);\n          });\n          i0.ɵɵtemplate(9, ContactsActivitiesComponent_ng_template_9_Template, 7, 3, \"ng-template\", 8)(10, ContactsActivitiesComponent_ng_template_10_Template, 6, 3, \"ng-template\", 9)(11, ContactsActivitiesComponent_ng_template_11_Template, 3, 0, \"ng-template\", 10)(12, ContactsActivitiesComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(13, \"app-activities-form\", 12);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.activitiesDetails)(\"rows\", 8)(\"paginator\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"account_id\", ctx.bp_id);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i5.Tooltip, i6.PrimeTemplate, i7.Table, i7.SortableColumn, i7.FrozenColumn, i7.ReorderableColumn, i8.NgControlStatus, i8.NgModel, i9.Button, i10.ActivitiesFormComponent, i11.MultiSelect, i4.SlicePipe, i4.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "ActivitiesFormComponent", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "sortOrder", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "ContactsActivitiesComponent_ng_template_9_ng_container_6_Template_th_click_1_listener", "col_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "ContactsActivitiesComponent_ng_template_9_ng_container_6_i_4_Template", "ContactsActivitiesComponent_ng_template_9_ng_container_6_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "ContactsActivitiesComponent_ng_template_9_Template_th_click_1_listener", "_r1", "ContactsActivitiesComponent_ng_template_9_i_4_Template", "ContactsActivitiesComponent_ng_template_9_i_5_Template", "ContactsActivitiesComponent_ng_template_9_ng_container_6_Template", "selectedColumns", "ɵɵpropertyInterpolate", "stripHtml", "activity_r5", "globalNote", "note", "ɵɵpipeBind3", "length", "business_partner_contact", "bp_full_name", "start_date", "ɵɵpipeBind2", "end_date", "createdAt", "business_partner_organizer", "getLabelFromDropdown", "phone_call_category", "activity_status", "priority", "business_partner", "addresses", "phone", "document_type", "ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_3_Template", "ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_4_Template", "ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_5_Template", "ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_6_Template", "ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_7_Template", "ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_8_Template", "ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_9_Template", "ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_10_Template", "ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_11_Template", "ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_12_Template", "ContactsActivitiesComponent_ng_template_10_ng_container_5_ng_container_13_Template", "col_r6", "ContactsActivitiesComponent_ng_template_10_ng_container_5_Template", "activity_id", "ɵɵsanitizeUrl", "subject", "ContactsActivitiesComponent", "constructor", "activitiesservice", "contactsservice", "router", "route", "unsubscribe$", "activitiesDetails", "bp_id", "documentId", "dropdowns", "activityCategory", "activityStatus", "activityPriority", "_selectedColumns", "cols", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "loadActivityDropDown", "contact", "pipe", "subscribe", "response", "bp_company_id", "getActivity", "next", "activityResponse", "Array", "isArray", "allActivities", "slice", "map", "activity", "notes", "find", "is_global_note", "error", "console", "val", "filter", "col", "includes", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "html", "temp", "document", "createElement", "innerHTML", "textContent", "innerText", "target", "type", "getActivityDropdownOptions", "res", "attr", "label", "description", "value", "code", "dropdownKey", "item", "opt", "openActivityDialog", "activityDialog", "showDialog", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivitiesService", "i2", "ContactsService", "i3", "Router", "ActivatedRoute", "selectors", "viewQuery", "ContactsActivitiesComponent_Query", "rf", "ctx", "ContactsActivitiesComponent_Template_p_button_click_5_listener", "ɵɵtwoWayListener", "ContactsActivitiesComponent_Template_p_multiSelect_ngModelChange_6_listener", "$event", "ɵɵtwoWayBindingSet", "ContactsActivitiesComponent_Template_p_table_onColReorder_8_listener", "ContactsActivitiesComponent_ng_template_9_Template", "ContactsActivitiesComponent_ng_template_10_Template", "ContactsActivitiesComponent_ng_template_11_Template", "ContactsActivitiesComponent_ng_template_12_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts-details\\contacts-activities\\contacts-activities.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\contacts\\contacts-details\\contacts-activities\\contacts-activities.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { ActivitiesService } from 'src/app/store/activities/activities.service';\r\nimport { ContactsService } from '../../contacts.service';\r\nimport { ActivitiesFormComponent } from 'src/app/store/common-form/activities-form/activities-form.component';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\n\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-contacts-activities',\r\n  templateUrl: './contacts-activities.component.html',\r\n  styleUrl: './contacts-activities.component.scss',\r\n})\r\nexport class ContactsActivitiesComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  @ViewChild(ActivitiesFormComponent) activityDialog!: ActivitiesFormComponent;\r\n  public activitiesDetails: any[] = [];\r\n  public bp_id: string = '';\r\n  public documentId: string = '';\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    activityCategory: [],\r\n    activityStatus: [],\r\n    activityPriority: [],\r\n  };\r\n\r\n  constructor(\r\n    private activitiesservice: ActivitiesService,\r\n    private contactsservice: ContactsService,\r\n    private router: Router,\r\n    private route: ActivatedRoute\r\n  ) { }\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'globalNote.note', header: 'Notes' },\r\n    { field: 'business_partner_contact.bp_full_name', header: 'Primary Contact' },\r\n    { field: 'start_date', header: 'Start Date/Time' },\r\n    { field: 'end_date', header: 'End Date/Time' },\r\n    { field: 'createdAt', header: 'Created On' },\r\n    { field: 'business_partner_organizer.bp_full_name', header: 'Organizer' },\r\n    { field: 'phone_call_category', header: 'Category' },\r\n    { field: 'activity_status', header: 'Status' },\r\n    { field: 'priority', header: 'Priority' },\r\n    { field: 'phone', header: 'Phone' },\r\n    { field: 'document_type', header: 'Activity Type' }\r\n  ];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.activitiesDetails.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = (value1 < value2 ? -1 : (value1 > value2 ? 1 : 0));\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.loadActivityDropDown(\r\n      'activityCategory',\r\n      'CRM_ACTIVITY_PHONE_CALL_CATEGORY'\r\n    );\r\n    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');\r\n    this.loadActivityDropDown(\r\n      'activityDocumentType',\r\n      'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL'\r\n    );\r\n    this.loadActivityDropDown('activityPriority', 'CRM_ACTIVITY_PRIORITY');\r\n    this.contactsservice.contact\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.bp_id = response.bp_company_id;\r\n          this.documentId = response.documentId;\r\n          if (this.bp_id) {\r\n            this.activitiesservice\r\n              .getActivity(this.bp_id)\r\n              .pipe(takeUntil(this.unsubscribe$))\r\n              .subscribe({\r\n                next: (activityResponse: any) => {\r\n                  if (Array.isArray(activityResponse.data)) {\r\n                    const allActivities = activityResponse.data.slice(0, 10);\r\n                    this.activitiesDetails = allActivities.map(\r\n                      (activity: any) => {\r\n                        // Find global note from activity.notes array\r\n                        const globalNote = activity.notes?.find(\r\n                          (note: any) => note.is_global_note === true\r\n                        );\r\n\r\n                        return {\r\n                          ...activity,\r\n                          globalNote: globalNote || null,\r\n                        };\r\n                      }\r\n                    );\r\n                  } else {\r\n                    this.activitiesDetails = [];\r\n                  }\r\n                },\r\n                error: (error: any) => {\r\n                  console.error('Error fetching activities:', error);\r\n                },\r\n              });\r\n          }\r\n        }\r\n      });\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  stripHtml(html: string): string {\r\n    const temp = document.createElement('div');\r\n    temp.innerHTML = html;\r\n    return temp.textContent || temp.innerText || '';\r\n  }\r\n\r\n  loadActivityDropDown(target: string, type: string): void {\r\n    this.activitiesservice\r\n      .getActivityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  openActivityDialog() {\r\n    this.activityDialog.showDialog('right');\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full bg-white border-round shadow-1\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Activities</h4>\r\n        <div class=\"flex align-items-center gap-3 ml-auto\">\r\n            <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n                [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" (click)=\"openActivityDialog()\" />\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table [value]=\"activitiesDetails\" dataKey=\"id\" [rows]=\"8\" styleClass=\"\" [paginator]=\"true\"\r\n            responsiveLayout=\"scroll\" [scrollable]=\"true\" class=\"scrollable-table\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn (click)=\"customSort('subject')\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            Subject\r\n                            <i *ngIf=\"sortField === 'subject'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'subject'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-activity let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"text-orange-600 cursor-pointer font-medium underline border-round-left-lg\">\r\n                        <div class=\"readonly-field font-medium p-2 text-orange-600 cursor-pointer\">\r\n                            <a [href]=\"'/#/store/activities/calls/' + activity?.activity_id + '/overview'\"\r\n                                style=\"text-decoration: none; color: inherit;\" target=\"_blank\">\r\n                                {{ activity?.subject || '-' }}\r\n                            </a>\r\n                        </div>\r\n                    </td>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'globalNote.note'\">\r\n                                    <div pTooltip=\"{{ stripHtml(activity?.globalNote?.note) }}\" tooltipPosition=\"top\"\r\n                                        tooltipStyleClass=\"multi-line-tooltip\">\r\n                                        <div class=\"note-text\">\r\n                                            {{ activity?.globalNote?.note ? (stripHtml(activity.globalNote.note) |\r\n                                            slice:0:80) +\r\n                                            (activity.globalNote.note.length > 80 ? '...' : '') : '-' }}\r\n                                        </div>\r\n                                    </div>\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'business_partner_contact.bp_full_name'\">\r\n                                    {{ activity?.business_partner_contact?.bp_full_name || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'start_date'\">\r\n                                    {{ activity?.start_date ? (activity?.start_date | date: 'MM-dd-yyyy hh:mm a') : '-'\r\n                                    }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'end_date'\">\r\n                                    {{ activity?.end_date ? (activity?.end_date | date: 'MM-dd-yyyy hh:mm a') : '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'createdAt'\">\r\n                                    {{ activity?.createdAt ? (activity?.createdAt | date: 'MM-dd-yyyy hh:mm a') : '-' }}\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'business_partner_organizer.bp_full_name'\">\r\n                                    {{ activity?.business_partner_organizer?.bp_full_name || '-' }}\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'phone_call_category'\">\r\n                                    {{ getLabelFromDropdown('activityCategory', activity?.phone_call_category) || '-' }}\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'activity_status'\">\r\n                                    {{ getLabelFromDropdown('activityStatus', activity?.activity_status) || '-' }}\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'priority'\">\r\n                                    {{ getLabelFromDropdown('activityPriority', activity?.priority) || '-' }}\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'phone'\">\r\n                                    {{ activity.business_partner?.addresses?.[0]?.phone || '-' }}\r\n                                </ng-container>\r\n                                <ng-container *ngSwitchCase=\"'document_type'\">\r\n                                    {{ getLabelFromDropdown('activityDocumentType', activity?.document_type) || '-' }}\r\n                                </ng-container>\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"12\" class=\"border-round-left-lg pl-3\">\r\n                        No activities found.\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"12\" class=\"border-round-left-lg pl-3\">\r\n                        Loading activities data. Please wait...\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n<app-activities-form [account_id]=\"bp_id\"></app-activities-form>"], "mappings": "AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAGzC,SAASC,uBAAuB,QAAQ,qEAAqE;;;;;;;;;;;;;;;ICoBjFC,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;IAQ3DD,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;;IAP3ED,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,aAAqF;IAAhCN,EAAA,CAAAO,UAAA,mBAAAC,sFAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAC,qEAAA,gBACkF,IAAAC,qEAAA,gBAEvB;IAEnEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;IAG7BhB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;;;;;;IAjB7ChB,EADJ,CAAAM,cAAA,SAAI,aAC+E;IAA7DN,EAAA,CAAAO,UAAA,mBAAAmB,uEAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,SAAS,CAAC;IAAA,EAAC;IAC7Cf,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,gBACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAU,sDAAA,gBACkF,IAAAC,sDAAA,gBAEvB;IAEnE7B,EADI,CAAAqB,YAAA,EAAM,EACL;IAELrB,EAAA,CAAAkB,UAAA,IAAAY,iEAAA,2BAAkD;IAWtD9B,EAAA,CAAAqB,YAAA,EAAK;;;;IAlBWrB,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,eAA6B;IAG7BzB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,eAA6B;IAIXzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IA2BpC/B,EAAA,CAAAK,uBAAA,GAAgD;IAGxCL,EAFJ,CAAAM,cAAA,cAC2C,cAChB;IACnBN,EAAA,CAAAiB,MAAA,GAGJ;;IACJjB,EADI,CAAAqB,YAAA,EAAM,EACJ;;;;;;IAPDrB,EAAA,CAAAsB,SAAA,EAAsD;IAAtDtB,EAAA,CAAAgC,qBAAA,aAAA7B,MAAA,CAAA8B,SAAA,CAAAC,WAAA,kBAAAA,WAAA,CAAAC,UAAA,kBAAAD,WAAA,CAAAC,UAAA,CAAAC,IAAA,EAAsD;IAGnDpC,EAAA,CAAAsB,SAAA,GAGJ;IAHItB,EAAA,CAAAuB,kBAAA,OAAAW,WAAA,kBAAAA,WAAA,CAAAC,UAAA,kBAAAD,WAAA,CAAAC,UAAA,CAAAC,IAAA,IAAApC,EAAA,CAAAqC,WAAA,OAAAlC,MAAA,CAAA8B,SAAA,CAAAC,WAAA,CAAAC,UAAA,CAAAC,IAAA,aAAAF,WAAA,CAAAC,UAAA,CAAAC,IAAA,CAAAE,MAAA,+BAGJ;;;;;IAIRtC,EAAA,CAAAK,uBAAA,GAAsE;IAClEL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAW,WAAA,kBAAAA,WAAA,CAAAK,wBAAA,kBAAAL,WAAA,CAAAK,wBAAA,CAAAC,YAAA,cACJ;;;;;IAEAxC,EAAA,CAAAK,uBAAA,GAA2C;IACvCL,EAAA,CAAAiB,MAAA,GAEJ;;;;;;IAFIjB,EAAA,CAAAsB,SAAA,EAEJ;IAFItB,EAAA,CAAAuB,kBAAA,OAAAW,WAAA,kBAAAA,WAAA,CAAAO,UAAA,IAAAzC,EAAA,CAAA0C,WAAA,OAAAR,WAAA,kBAAAA,WAAA,CAAAO,UAAA,mCAEJ;;;;;IAEAzC,EAAA,CAAAK,uBAAA,GAAyC;IACrCL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAW,WAAA,kBAAAA,WAAA,CAAAS,QAAA,IAAA3C,EAAA,CAAA0C,WAAA,OAAAR,WAAA,kBAAAA,WAAA,CAAAS,QAAA,mCACJ;;;;;IAEA3C,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAW,WAAA,kBAAAA,WAAA,CAAAU,SAAA,IAAA5C,EAAA,CAAA0C,WAAA,OAAAR,WAAA,kBAAAA,WAAA,CAAAU,SAAA,mCACJ;;;;;IACA5C,EAAA,CAAAK,uBAAA,GAAwE;IACpEL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAW,WAAA,kBAAAA,WAAA,CAAAW,0BAAA,kBAAAX,WAAA,CAAAW,0BAAA,CAAAL,YAAA,cACJ;;;;;IACAxC,EAAA,CAAAK,uBAAA,GAAoD;IAChDL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAApB,MAAA,CAAA2C,oBAAA,qBAAAZ,WAAA,kBAAAA,WAAA,CAAAa,mBAAA,cACJ;;;;;IACA/C,EAAA,CAAAK,uBAAA,GAAgD;IAC5CL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAApB,MAAA,CAAA2C,oBAAA,mBAAAZ,WAAA,kBAAAA,WAAA,CAAAc,eAAA,cACJ;;;;;IACAhD,EAAA,CAAAK,uBAAA,GAAyC;IACrCL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAApB,MAAA,CAAA2C,oBAAA,qBAAAZ,WAAA,kBAAAA,WAAA,CAAAe,QAAA,cACJ;;;;;IACAjD,EAAA,CAAAK,uBAAA,GAAsC;IAClCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAW,WAAA,CAAAgB,gBAAA,kBAAAhB,WAAA,CAAAgB,gBAAA,CAAAC,SAAA,kBAAAjB,WAAA,CAAAgB,gBAAA,CAAAC,SAAA,qBAAAjB,WAAA,CAAAgB,gBAAA,CAAAC,SAAA,IAAAC,KAAA,cACJ;;;;;IACApD,EAAA,CAAAK,uBAAA,GAA8C;IAC1CL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAApB,MAAA,CAAA2C,oBAAA,yBAAAZ,WAAA,kBAAAA,WAAA,CAAAmB,aAAA,cACJ;;;;;IA/CZrD,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IA2CjCL,EA1CA,CAAAkB,UAAA,IAAAoC,iFAAA,2BAAgD,IAAAC,iFAAA,2BAWsB,IAAAC,iFAAA,2BAI3B,IAAAC,iFAAA,2BAKF,IAAAC,iFAAA,2BAIC,IAAAC,iFAAA,2BAG8B,IAAAC,iFAAA,2BAGpB,KAAAC,kFAAA,2BAGJ,KAAAC,kFAAA,2BAGP,KAAAC,kFAAA,2BAGH,KAAAC,kFAAA,2BAGQ;;IAItDhE,EAAA,CAAAqB,YAAA,EAAK;;;;;IA/CarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAA+D,MAAA,CAAAjD,KAAA,CAAsB;IACjBhB,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAAE,UAAA,mCAA+B;IAW/BF,EAAA,CAAAsB,SAAA,EAAqD;IAArDtB,EAAA,CAAAE,UAAA,yDAAqD;IAIrDF,EAAA,CAAAsB,SAAA,EAA0B;IAA1BtB,EAAA,CAAAE,UAAA,8BAA0B;IAK1BF,EAAA,CAAAsB,SAAA,EAAwB;IAAxBtB,EAAA,CAAAE,UAAA,4BAAwB;IAIxBF,EAAA,CAAAsB,SAAA,EAAyB;IAAzBtB,EAAA,CAAAE,UAAA,6BAAyB;IAGzBF,EAAA,CAAAsB,SAAA,EAAuD;IAAvDtB,EAAA,CAAAE,UAAA,2DAAuD;IAGvDF,EAAA,CAAAsB,SAAA,EAAmC;IAAnCtB,EAAA,CAAAE,UAAA,uCAAmC;IAGnCF,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAAE,UAAA,mCAA+B;IAG/BF,EAAA,CAAAsB,SAAA,EAAwB;IAAxBtB,EAAA,CAAAE,UAAA,4BAAwB;IAGxBF,EAAA,CAAAsB,SAAA,EAAqB;IAArBtB,EAAA,CAAAE,UAAA,yBAAqB;IAGrBF,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,iCAA6B;;;;;IAnDhDF,EAHZ,CAAAM,cAAA,aAA2B,aAC6E,cACrB,YAEJ;IAC/DN,EAAA,CAAAiB,MAAA,GACJ;IAERjB,EAFQ,CAAAqB,YAAA,EAAI,EACF,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAgD,kEAAA,6BAAkD;IAmDtDlE,EAAA,CAAAqB,YAAA,EAAK;;;;;IAzDUrB,EAAA,CAAAsB,SAAA,GAA2E;IAA3EtB,EAAA,CAAAE,UAAA,yCAAAgC,WAAA,kBAAAA,WAAA,CAAAiC,WAAA,iBAAAnE,EAAA,CAAAoE,aAAA,CAA2E;IAE1EpE,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAW,WAAA,kBAAAA,WAAA,CAAAmC,OAAA,cACJ;IAGsBrE,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IAwDhD/B,EADJ,CAAAM,cAAA,SAAI,aACmD;IAC/CN,EAAA,CAAAiB,MAAA,6BACJ;IACJjB,EADI,CAAAqB,YAAA,EAAK,EACJ;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aACmD;IAC/CN,EAAA,CAAAiB,MAAA,gDACJ;IACJjB,EADI,CAAAqB,YAAA,EAAK,EACJ;;;ADxGrB,OAAM,MAAOiD,2BAA2B;EAatCC,YACUC,iBAAoC,EACpCC,eAAgC,EAChCC,MAAc,EACdC,KAAqB;IAHrB,KAAAH,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IAhBP,KAAAC,YAAY,GAAG,IAAI/E,OAAO,EAAQ;IAEnC,KAAAgF,iBAAiB,GAAU,EAAE;IAC7B,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,UAAU,GAAW,EAAE;IAEvB,KAAAC,SAAS,GAA0B;MACxCC,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBC,gBAAgB,EAAE;KACnB;IASO,KAAAC,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAErE,KAAK,EAAE,iBAAiB;MAAEQ,MAAM,EAAE;IAAO,CAAE,EAC7C;MAAER,KAAK,EAAE,uCAAuC;MAAEQ,MAAM,EAAE;IAAiB,CAAE,EAC7E;MAAER,KAAK,EAAE,YAAY;MAAEQ,MAAM,EAAE;IAAiB,CAAE,EAClD;MAAER,KAAK,EAAE,UAAU;MAAEQ,MAAM,EAAE;IAAe,CAAE,EAC9C;MAAER,KAAK,EAAE,WAAW;MAAEQ,MAAM,EAAE;IAAY,CAAE,EAC5C;MAAER,KAAK,EAAE,yCAAyC;MAAEQ,MAAM,EAAE;IAAW,CAAE,EACzE;MAAER,KAAK,EAAE,qBAAqB;MAAEQ,MAAM,EAAE;IAAU,CAAE,EACpD;MAAER,KAAK,EAAE,iBAAiB;MAAEQ,MAAM,EAAE;IAAQ,CAAE,EAC9C;MAAER,KAAK,EAAE,UAAU;MAAEQ,MAAM,EAAE;IAAU,CAAE,EACzC;MAAER,KAAK,EAAE,OAAO;MAAEQ,MAAM,EAAE;IAAO,CAAE,EACnC;MAAER,KAAK,EAAE,eAAe;MAAEQ,MAAM,EAAE;IAAe,CAAE,CACpD;IAED,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAArB,SAAS,GAAW,CAAC;EAnBjB;EAqBJW,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACS,SAAS,KAAKT,KAAK,EAAE;MAC5B,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACqB,SAAS,GAAGT,KAAK;MACtB,IAAI,CAACZ,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAACyE,iBAAiB,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACnC,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAEvE,KAAK,CAAC;MAC9C,MAAM2E,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAExE,KAAK,CAAC;MAE9C,IAAI4E,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAIH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAIF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAG;MAEhE,OAAO,IAAI,CAACvF,SAAS,GAAGwF,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAE9E,KAAa;IACvC,IAAI,CAAC8E,IAAI,IAAI,CAAC9E,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAAC+E,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAAC9E,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAACgF,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,CACvB,kBAAkB,EAClB,kCAAkC,CACnC;IACD,IAAI,CAACA,oBAAoB,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;IAClE,IAAI,CAACA,oBAAoB,CACvB,sBAAsB,EACtB,kCAAkC,CACnC;IACD,IAAI,CAACA,oBAAoB,CAAC,kBAAkB,EAAE,uBAAuB,CAAC;IACtE,IAAI,CAAC5B,eAAe,CAAC6B,OAAO,CACzBC,IAAI,CAACzG,SAAS,CAAC,IAAI,CAAC8E,YAAY,CAAC,CAAC,CAClC4B,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAC3B,KAAK,GAAG2B,QAAQ,CAACC,aAAa;QACnC,IAAI,CAAC3B,UAAU,GAAG0B,QAAQ,CAAC1B,UAAU;QACrC,IAAI,IAAI,CAACD,KAAK,EAAE;UACd,IAAI,CAACN,iBAAiB,CACnBmC,WAAW,CAAC,IAAI,CAAC7B,KAAK,CAAC,CACvByB,IAAI,CAACzG,SAAS,CAAC,IAAI,CAAC8E,YAAY,CAAC,CAAC,CAClC4B,SAAS,CAAC;YACTI,IAAI,EAAGC,gBAAqB,IAAI;cAC9B,IAAIC,KAAK,CAACC,OAAO,CAACF,gBAAgB,CAACf,IAAI,CAAC,EAAE;gBACxC,MAAMkB,aAAa,GAAGH,gBAAgB,CAACf,IAAI,CAACmB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;gBACxD,IAAI,CAACpC,iBAAiB,GAAGmC,aAAa,CAACE,GAAG,CACvCC,QAAa,IAAI;kBAChB;kBACA,MAAMhF,UAAU,GAAGgF,QAAQ,CAACC,KAAK,EAAEC,IAAI,CACpCjF,IAAS,IAAKA,IAAI,CAACkF,cAAc,KAAK,IAAI,CAC5C;kBAED,OAAO;oBACL,GAAGH,QAAQ;oBACXhF,UAAU,EAAEA,UAAU,IAAI;mBAC3B;gBACH,CAAC,CACF;cACH,CAAC,MAAM;gBACL,IAAI,CAAC0C,iBAAiB,GAAG,EAAE;cAC7B;YACF,CAAC;YACD0C,KAAK,EAAGA,KAAU,IAAI;cACpBC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;YACpD;WACD,CAAC;QACN;MACF;IACF,CAAC,CAAC;IAEJ,IAAI,CAACnC,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEA,IAAItD,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACqD,gBAAgB;EAC9B;EAEA,IAAIrD,eAAeA,CAAC0F,GAAU;IAC5B,IAAI,CAACrC,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAACqC,MAAM,CAACC,GAAG,IAAIF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACpE;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAAC3C,gBAAgB,CAAC0C,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAAC5C,gBAAgB,CAAC6C,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC5C,gBAAgB,CAAC6C,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;EAEA9F,SAASA,CAACkG,IAAY;IACpB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CF,IAAI,CAACG,SAAS,GAAGJ,IAAI;IACrB,OAAOC,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACK,SAAS,IAAI,EAAE;EACjD;EAEApC,oBAAoBA,CAACqC,MAAc,EAAEC,IAAY;IAC/C,IAAI,CAACnE,iBAAiB,CACnBoE,0BAA0B,CAACD,IAAI,CAAC,CAChCnC,SAAS,CAAEqC,GAAQ,IAAI;MACtB,IAAI,CAAC7D,SAAS,CAAC0D,MAAM,CAAC,GACpBG,GAAG,EAAE/C,IAAI,EAAEoB,GAAG,CAAE4B,IAAS,KAAM;QAC7BC,KAAK,EAAED,IAAI,CAACE,WAAW;QACvBC,KAAK,EAAEH,IAAI,CAACI;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEApG,oBAAoBA,CAACqG,WAAmB,EAAEF,KAAa;IACrD,MAAMG,IAAI,GAAG,IAAI,CAACpE,SAAS,CAACmE,WAAW,CAAC,EAAE9B,IAAI,CAC3CgC,GAAG,IAAKA,GAAG,CAACJ,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOG,IAAI,EAAEL,KAAK,IAAIE,KAAK;EAC7B;EAEAK,kBAAkBA,CAAA;IAChB,IAAI,CAACC,cAAc,CAACC,UAAU,CAAC,OAAO,CAAC;EACzC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC7E,YAAY,CAACgC,IAAI,EAAE;IACxB,IAAI,CAAChC,YAAY,CAAC8E,QAAQ,EAAE;EAC9B;;;uBA7KWpF,2BAA2B,EAAAtE,EAAA,CAAA2J,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAA7J,EAAA,CAAA2J,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA/J,EAAA,CAAA2J,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAjK,EAAA,CAAA2J,iBAAA,CAAAK,EAAA,CAAAE,cAAA;IAAA;EAAA;;;YAA3B5F,2BAA2B;MAAA6F,SAAA;MAAAC,SAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAE3BvK,uBAAuB;;;;;;;;;;;;UCjB5BC,EAFR,CAAAM,cAAA,aAAuD,aAC2C,YAC3C;UAAAN,EAAA,CAAAiB,MAAA,iBAAU;UAAAjB,EAAA,CAAAqB,YAAA,EAAK;UAE1DrB,EADJ,CAAAM,cAAA,aAAmD,kBAE2C;UAAjCN,EAAA,CAAAO,UAAA,mBAAAiK,+DAAA;YAAA,OAASD,GAAA,CAAAjB,kBAAA,EAAoB;UAAA,EAAC;UADvFtJ,EAAA,CAAAqB,YAAA,EAC0F;UAE1FrB,EAAA,CAAAM,cAAA,uBAE+I;UAF/GN,EAAA,CAAAyK,gBAAA,2BAAAC,4EAAAC,MAAA;YAAA3K,EAAA,CAAA4K,kBAAA,CAAAL,GAAA,CAAAxI,eAAA,EAAA4I,MAAA,MAAAJ,GAAA,CAAAxI,eAAA,GAAA4I,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAKrE3K,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAGFrB,EADJ,CAAAM,cAAA,aAAuB,iBAG0B;UAAzCN,EAAA,CAAAO,UAAA,0BAAAsK,qEAAAF,MAAA;YAAA,OAAgBJ,GAAA,CAAA1C,eAAA,CAAA8C,MAAA,CAAuB;UAAA,EAAC;UAmGxC3K,EAjGA,CAAAkB,UAAA,IAAA4J,kDAAA,yBAAgC,KAAAC,mDAAA,yBA0BiC,KAAAC,mDAAA,0BAgE3B,KAAAC,mDAAA,0BAOD;UASjDjL,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;UACNrB,EAAA,CAAAC,SAAA,+BAAgE;;;UAzHhDD,EAAA,CAAAsB,SAAA,GAAmC;UAACtB,EAApC,CAAAE,UAAA,oCAAmC,iBAAiB;UAEzCF,EAAA,CAAAsB,SAAA,EAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAAqK,GAAA,CAAAlF,IAAA,CAAgB;UAACrF,EAAA,CAAAkL,gBAAA,YAAAX,GAAA,CAAAxI,eAAA,CAA6B;UAEzD/B,EAAA,CAAAE,UAAA,2IAA0I;UAMzIF,EAAA,CAAAsB,SAAA,GAA2B;UACuCtB,EADlE,CAAAE,UAAA,UAAAqK,GAAA,CAAA1F,iBAAA,CAA2B,WAAwB,mBAAiC,oBAC5C,4BAAqD;UA8GzF7E,EAAA,CAAAsB,SAAA,GAAoB;UAApBtB,EAAA,CAAAE,UAAA,eAAAqK,GAAA,CAAAzF,KAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}