{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from './core/authentication/auth.guard';\nimport { contentResolver } from './core/content-resolver';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routerOptions = {\n  anchorScrolling: 'enabled',\n  scrollPositionRestoration: 'top',\n  enableTracing: true,\n  useHash: true\n};\nconst routes = [{\n  path: 'store',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./store/store.module').then(m => m.StoreModule),\n  resolve: {\n    commonContent: contentResolver\n  },\n  data: {\n    slug: 'common'\n  }\n}, {\n  path: 'backoffice',\n  // canActivate: [AuthGuard],\n  loadChildren: () => import('./backoffice/backoffice.module').then(m => m.BackofficeModule),\n  resolve: {\n    commonContent: contentResolver\n  },\n  data: {\n    slug: 'common'\n  }\n}, {\n  path: \"auth\",\n  loadChildren: () => import(\"./session/session.module\").then(mod => mod.SessionModule),\n  resolve: {\n    commonContent: contentResolver\n  },\n  data: {\n    slug: 'common'\n  }\n}, {\n  path: '',\n  redirectTo: 'store',\n  pathMatch: 'full'\n}, {\n  path: '**',\n  redirectTo: 'store'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes, routerOptions), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "contentResolver", "routerOptions", "anchorScrolling", "scrollPositionRestoration", "enableTracing", "useHash", "routes", "path", "canActivate", "loadChildren", "then", "m", "StoreModule", "resolve", "commonContent", "data", "slug", "BackofficeModule", "mod", "SessionModule", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { ExtraOptions, RouterModule, Routes } from '@angular/router';\r\nimport { AuthGuard } from './core/authentication/auth.guard';\r\nimport { contentResolver } from './core/content-resolver';\r\n\r\nconst routerOptions: ExtraOptions = {\r\n  anchorScrolling: 'enabled',\r\n  scrollPositionRestoration: 'top',\r\n  enableTracing: true,\r\n  useHash: true\r\n};\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'store',\r\n    canActivate: [AuthGuard],\r\n    loadChildren: () =>\r\n      import('./store/store.module').then((m) => m.StoreModule),\r\n    resolve: {\r\n      commonContent: contentResolver,\r\n    },\r\n    data: {\r\n      slug: 'common',\r\n    },\r\n  },\r\n  {\r\n    path: 'backoffice',\r\n    // canActivate: [AuthGuard],\r\n    loadChildren: () =>\r\n      import('./backoffice/backoffice.module').then((m) => m.BackofficeModule),\r\n    resolve: {\r\n      commonContent: contentResolver,\r\n    },\r\n    data: {\r\n      slug: 'common',\r\n    },\r\n  },\r\n  {\r\n    path: \"auth\",\r\n    loadChildren: () =>\r\n      import(\"./session/session.module\").then((mod) => mod.SessionModule),\r\n    resolve: {\r\n      commonContent: contentResolver,\r\n    },\r\n    data: {\r\n      slug: 'common',\r\n    },\r\n  },\r\n  { path: '', redirectTo: 'store', pathMatch: 'full' },\r\n  { path: '**', redirectTo: 'store' },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes, routerOptions)],\r\n  exports: [RouterModule],\r\n})\r\nexport class AppRoutingModule {}\r\n"], "mappings": "AACA,SAAuBA,YAAY,QAAgB,iBAAiB;AACpE,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,eAAe,QAAQ,yBAAyB;;;AAEzD,MAAMC,aAAa,GAAiB;EAClCC,eAAe,EAAE,SAAS;EAC1BC,yBAAyB,EAAE,KAAK;EAChCC,aAAa,EAAE,IAAI;EACnBC,OAAO,EAAE;CACV;AAED,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,OAAO;EACbC,WAAW,EAAE,CAACT,SAAS,CAAC;EACxBU,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC;EAC3DC,OAAO,EAAE;IACPC,aAAa,EAAEd;GAChB;EACDe,IAAI,EAAE;IACJC,IAAI,EAAE;;CAET,EACD;EACET,IAAI,EAAE,YAAY;EAClB;EACAE,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACM,gBAAgB,CAAC;EAC1EJ,OAAO,EAAE;IACPC,aAAa,EAAEd;GAChB;EACDe,IAAI,EAAE;IACJC,IAAI,EAAE;;CAET,EACD;EACET,IAAI,EAAE,MAAM;EACZE,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEQ,GAAG,IAAKA,GAAG,CAACC,aAAa,CAAC;EACrEN,OAAO,EAAE;IACPC,aAAa,EAAEd;GAChB;EACDe,IAAI,EAAE;IACJC,IAAI,EAAE;;CAET,EACD;EAAET,IAAI,EAAE,EAAE;EAAEa,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE;AAAM,CAAE,EACpD;EAAEd,IAAI,EAAE,IAAI;EAAEa,UAAU,EAAE;AAAO,CAAE,CACpC;AAMD,OAAM,MAAOE,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBxB,YAAY,CAACyB,OAAO,CAACjB,MAAM,EAAEL,aAAa,CAAC,EAC3CH,YAAY;IAAA;EAAA;;;2EAEXwB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAA3B,YAAA;IAAA4B,OAAA,GAFjB5B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}