{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ImportRoutingModule } from './import-routing.module';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { TabMenuModule } from 'primeng/tabmenu';\nimport { TableModule } from 'primeng/table';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmationService, MessageService } from 'primeng/api';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { FormsModule } from '@angular/forms';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport let ImportModule = /*#__PURE__*/(() => {\n  class ImportModule {\n    static {\n      this.ɵfac = function ImportModule_Factory(t) {\n        return new (t || ImportModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ImportModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [ConfirmationService, MessageService],\n        imports: [CommonModule, FormsModule, ConfirmDialogModule, TabMenuModule, TableModule, ProgressBarModule, DropdownModule, ToastModule, BreadcrumbModule, ImportRoutingModule, MultiSelectModule]\n      });\n    }\n  }\n  return ImportModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}