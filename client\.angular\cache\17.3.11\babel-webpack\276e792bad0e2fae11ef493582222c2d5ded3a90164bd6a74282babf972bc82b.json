{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Occitan, lengadocian dialecte [oc-lnc]\n//! author : <PERSON> : https://github.com/Quenty31\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var ocLnc = moment.defineLocale('oc-lnc', {\n    months: {\n      standalone: 'genièr_febrièr_març_abril_mai_junh_julhet_agost_setembre_octòbre_novembre_decembre'.split('_'),\n      format: \"de genièr_de febrièr_de març_d'abril_de mai_de junh_de julhet_d'agost_de setembre_d'octòbre_de novembre_de decembre\".split('_'),\n      isFormat: /D[oD]?(\\s)+MMMM/\n    },\n    monthsShort: 'gen._febr._març_abr._mai_junh_julh._ago._set._oct._nov._dec.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'dimenge_diluns_dimars_dimècres_dijòus_divendres_dissabte'.split('_'),\n    weekdaysShort: 'dg._dl._dm._dc._dj._dv._ds.'.split('_'),\n    weekdaysMin: 'dg_dl_dm_dc_dj_dv_ds'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM [de] YYYY',\n      ll: 'D MMM YYYY',\n      LLL: 'D MMMM [de] YYYY [a] H:mm',\n      lll: 'D MMM YYYY, H:mm',\n      LLLL: 'dddd D MMMM [de] YYYY [a] H:mm',\n      llll: 'ddd D MMM YYYY, H:mm'\n    },\n    calendar: {\n      sameDay: '[uèi a] LT',\n      nextDay: '[deman a] LT',\n      nextWeek: 'dddd [a] LT',\n      lastDay: '[ièr a] LT',\n      lastWeek: 'dddd [passat a] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: \"d'aquí %s\",\n      past: 'fa %s',\n      s: 'unas segondas',\n      ss: '%d segondas',\n      m: 'una minuta',\n      mm: '%d minutas',\n      h: 'una ora',\n      hh: '%d oras',\n      d: 'un jorn',\n      dd: '%d jorns',\n      M: 'un mes',\n      MM: '%d meses',\n      y: 'un an',\n      yy: '%d ans'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(r|n|t|è|a)/,\n    ordinal: function (number, period) {\n      var output = number === 1 ? 'r' : number === 2 ? 'n' : number === 3 ? 'r' : number === 4 ? 't' : 'è';\n      if (period === 'w' || period === 'W') {\n        output = 'a';\n      }\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4\n    }\n  });\n  return ocLnc;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "ocLnc", "defineLocale", "months", "standalone", "split", "format", "isFormat", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "ll", "LLL", "lll", "LLLL", "llll", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "period", "output", "week", "dow", "doy"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/moment/locale/oc-lnc.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Occitan, lengadocian dialecte [oc-lnc]\n//! author : <PERSON> : https://github.com/Quenty31\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var ocLnc = moment.defineLocale('oc-lnc', {\n        months: {\n            standalone:\n                'genièr_febrièr_març_abril_mai_junh_julhet_agost_setembre_octòbre_novembre_decembre'.split(\n                    '_'\n                ),\n            format: \"de genièr_de febrièr_de març_d'abril_de mai_de junh_de julhet_d'agost_de setembre_d'octòbre_de novembre_de decembre\".split(\n                '_'\n            ),\n            isFormat: /D[oD]?(\\s)+MMMM/,\n        },\n        monthsShort:\n            'gen._febr._març_abr._mai_junh_julh._ago._set._oct._nov._dec.'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays: 'dimenge_diluns_dimars_dimècres_dijòus_divendres_dissabte'.split(\n            '_'\n        ),\n        weekdaysShort: 'dg._dl._dm._dc._dj._dv._ds.'.split('_'),\n        weekdaysMin: 'dg_dl_dm_dc_dj_dv_ds'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM [de] YYYY',\n            ll: 'D MMM YYYY',\n            LLL: 'D MMMM [de] YYYY [a] H:mm',\n            lll: 'D MMM YYYY, H:mm',\n            LLLL: 'dddd D MMMM [de] YYYY [a] H:mm',\n            llll: 'ddd D MMM YYYY, H:mm',\n        },\n        calendar: {\n            sameDay: '[uèi a] LT',\n            nextDay: '[deman a] LT',\n            nextWeek: 'dddd [a] LT',\n            lastDay: '[ièr a] LT',\n            lastWeek: 'dddd [passat a] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: \"d'aquí %s\",\n            past: 'fa %s',\n            s: 'unas segondas',\n            ss: '%d segondas',\n            m: 'una minuta',\n            mm: '%d minutas',\n            h: 'una ora',\n            hh: '%d oras',\n            d: 'un jorn',\n            dd: '%d jorns',\n            M: 'un mes',\n            MM: '%d meses',\n            y: 'un an',\n            yy: '%d ans',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(r|n|t|è|a)/,\n        ordinal: function (number, period) {\n            var output =\n                number === 1\n                    ? 'r'\n                    : number === 2\n                      ? 'n'\n                      : number === 3\n                        ? 'r'\n                        : number === 4\n                          ? 't'\n                          : 'è';\n            if (period === 'w' || period === 'W') {\n                output = 'a';\n            }\n            return number + output;\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4,\n        },\n    });\n\n    return ocLnc;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,KAAK,GAAGD,MAAM,CAACE,YAAY,CAAC,QAAQ,EAAE;IACtCC,MAAM,EAAE;MACJC,UAAU,EACN,oFAAoF,CAACC,KAAK,CACtF,GACJ,CAAC;MACLC,MAAM,EAAE,qHAAqH,CAACD,KAAK,CAC/H,GACJ,CAAC;MACDE,QAAQ,EAAE;IACd,CAAC;IACDC,WAAW,EACP,8DAA8D,CAACH,KAAK,CAChE,GACJ,CAAC;IACLI,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,0DAA0D,CAACL,KAAK,CACtE,GACJ,CAAC;IACDM,aAAa,EAAE,6BAA6B,CAACN,KAAK,CAAC,GAAG,CAAC;IACvDO,WAAW,EAAE,sBAAsB,CAACP,KAAK,CAAC,GAAG,CAAC;IAC9CQ,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,MAAM;MACVC,GAAG,EAAE,SAAS;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,kBAAkB;MACtBC,EAAE,EAAE,YAAY;MAChBC,GAAG,EAAE,2BAA2B;MAChCC,GAAG,EAAE,kBAAkB;MACvBC,IAAI,EAAE,gCAAgC;MACtCC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,YAAY;MACrBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,aAAa;MACvBC,OAAO,EAAE,YAAY;MACrBC,QAAQ,EAAE,oBAAoB;MAC9BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,OAAO;MACbC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,oBAAoB;IAC5CC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAEC,MAAM,EAAE;MAC/B,IAAIC,MAAM,GACNF,MAAM,KAAK,CAAC,GACN,GAAG,GACHA,MAAM,KAAK,CAAC,GACV,GAAG,GACHA,MAAM,KAAK,CAAC,GACV,GAAG,GACHA,MAAM,KAAK,CAAC,GACV,GAAG,GACH,GAAG;MACnB,IAAIC,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,EAAE;QAClCC,MAAM,GAAG,GAAG;MAChB;MACA,OAAOF,MAAM,GAAGE,MAAM;IAC1B,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE;IACT;EACJ,CAAC,CAAC;EAEF,OAAOpD,KAAK;AAEhB,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}