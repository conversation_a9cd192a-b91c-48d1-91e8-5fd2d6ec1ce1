{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport { ServiceTicketsListingRoutingModule } from './service-tickets-listing-routing.module';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { MessageService } from 'primeng/api';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport * as i0 from \"@angular/core\";\nexport let ServiceTicketsListingModule = /*#__PURE__*/(() => {\n  class ServiceTicketsListingModule {\n    static {\n      this.ɵfac = function ServiceTicketsListingModule_Factory(t) {\n        return new (t || ServiceTicketsListingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ServiceTicketsListingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [MessageService],\n        imports: [CommonModule, SharedModule, ServiceTicketsListingRoutingModule, BreadcrumbModule, DropdownModule, TableModule, FormsModule, CalendarModule, ButtonModule, ProgressSpinnerModule, TabViewModule, AutoCompleteModule, InputTextModule, MultiSelectModule]\n      });\n    }\n  }\n  return ServiceTicketsListingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}