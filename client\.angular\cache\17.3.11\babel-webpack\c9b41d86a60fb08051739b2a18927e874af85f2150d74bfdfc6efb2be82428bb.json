{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./opportunities.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/breadcrumb\";\nimport * as i9 from \"primeng/checkbox\";\nimport * as i10 from \"primeng/multiselect\";\nconst _c0 = [\"dt1\"];\nfunction OpportunitiesComponent_ng_template_19_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction OpportunitiesComponent_ng_template_19_i_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n}\nfunction OpportunitiesComponent_ng_template_19_ng_container_8_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction OpportunitiesComponent_ng_template_19_ng_container_8_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 28);\n  }\n}\nfunction OpportunitiesComponent_ng_template_19_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 29);\n    i0.ɵɵlistener(\"click\", function OpportunitiesComponent_ng_template_19_ng_container_8_Template_th_click_1_listener() {\n      const col_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.customSort(col_r6.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, OpportunitiesComponent_ng_template_19_ng_container_8_i_4_Template, 1, 1, \"i\", 24)(5, OpportunitiesComponent_ng_template_19_ng_container_8_i_5_Template, 1, 0, \"i\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r6.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r6.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField === col_r6.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField !== col_r6.field);\n  }\n}\nfunction OpportunitiesComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 21);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 22);\n    i0.ɵɵlistener(\"click\", function OpportunitiesComponent_ng_template_19_Template_th_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.customSort(\"opportunity_id\"));\n    });\n    i0.ɵɵelementStart(4, \"div\", 23);\n    i0.ɵɵtext(5, \" ID \");\n    i0.ɵɵtemplate(6, OpportunitiesComponent_ng_template_19_i_6_Template, 1, 1, \"i\", 24)(7, OpportunitiesComponent_ng_template_19_i_7_Template, 1, 0, \"i\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, OpportunitiesComponent_ng_template_19_ng_container_8_Template, 6, 4, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField === \"opportunity_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.sortField !== \"opportunity_id\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedColumns);\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 36);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/opportunities/\" + opportunity_r7.opportunity_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.name) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.business_partner == null ? null : opportunity_r7.business_partner.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.business_partner_owner == null ? null : opportunity_r7.business_partner_owner.bp_full_name) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.expected_revenue_start_date) ? i0.ɵɵpipeBind2(2, 1, opportunity_r7.expected_revenue_start_date, \"MM/dd/yyyy\") : \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.expected_revenue_end_date) ? i0.ɵɵpipeBind2(2, 1, opportunity_r7.expected_revenue_end_date, \"MM/dd/yyyy\") : \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.updatedAt) ? i0.ɵɵpipeBind2(2, 1, opportunity_r7.updatedAt, \"MM/dd/yyyy hh:mm a\") : \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.last_changed_by) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.expected_revenue_amount) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLabelFromDropdown(\"opportunityStatus\", opportunity_r7 == null ? null : opportunity_r7.life_cycle_status_code) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (opportunity_r7 == null ? null : opportunity_r7.probability_percent) || \"-\", \" \");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"p-checkbox\", 37);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"ngModel\", opportunity_r7.need_help)(\"disabled\", true);\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 34);\n    i0.ɵɵtemplate(3, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_3_Template, 3, 2, \"ng-container\", 35)(4, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_4_Template, 2, 1, \"ng-container\", 35)(5, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_5_Template, 2, 1, \"ng-container\", 35)(6, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_6_Template, 3, 4, \"ng-container\", 35)(7, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_7_Template, 3, 4, \"ng-container\", 35)(8, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_8_Template, 3, 4, \"ng-container\", 35)(9, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_9_Template, 2, 1, \"ng-container\", 35)(10, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_10_Template, 2, 1, \"ng-container\", 35)(11, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_11_Template, 2, 1, \"ng-container\", 35)(12, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_12_Template, 2, 1, \"ng-container\", 35)(13, OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_13_Template, 2, 3, \"ng-container\", 35);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r8.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"business_partner_owner.bp_full_name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"expected_revenue_start_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"expected_revenue_end_date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedAt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"last_changed_by\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"expected_revenue_amount\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"life_cycle_status_code\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"probability_percent\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"need_help\");\n  }\n}\nfunction OpportunitiesComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 30)(1, \"td\", 31);\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, OpportunitiesComponent_ng_template_20_ng_container_5_Template, 14, 12, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const opportunity_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", opportunity_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", \"/store/opportunities/\" + opportunity_r7.opportunity_id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", opportunity_r7 == null ? null : opportunity_r7.opportunity_id, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedColumns);\n  }\n}\nfunction OpportunitiesComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 38);\n    i0.ɵɵtext(2, \"No opportunities found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction OpportunitiesComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 38);\n    i0.ɵɵtext(2, \"Loading opportunities data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class OpportunitiesComponent {\n  constructor(opportunitiesservice, router) {\n    this.opportunitiesservice = opportunitiesservice;\n    this.router = router;\n    this.unsubscribe$ = new Subject();\n    this.opportunities = [];\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n    this.dropdowns = {\n      opportunityStatus: []\n    };\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'name',\n      header: 'Name'\n    }, {\n      field: 'business_partner.bp_full_name',\n      header: 'Account'\n    }, {\n      field: 'business_partner_owner.bp_full_name',\n      header: 'Owner'\n    }, {\n      field: 'expected_revenue_start_date',\n      header: 'Start Date'\n    }, {\n      field: 'expected_revenue_end_date',\n      header: 'Close Date'\n    }, {\n      field: 'updatedAt',\n      header: 'Last Updated Date'\n    }, {\n      field: 'last_changed_by',\n      header: 'Last Updated By'\n    }, {\n      field: 'expected_revenue_amount',\n      header: 'Expected Value'\n    }, {\n      field: 'life_cycle_status_code',\n      header: 'Status'\n    }, {\n      field: 'probability_percent',\n      header: 'Probability'\n    }, {\n      field: 'need_help',\n      header: 'Need Help'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.opportunities.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\n    this.breadcrumbitems = [{\n      label: 'Opportunities',\n      routerLink: ['/store/opportunities']\n    }];\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.Actions = [{\n      name: 'All',\n      code: 'ALL'\n    }, {\n      name: 'My Opportunities',\n      code: 'MO'\n    }];\n    this.selectedActions = {\n      name: 'All',\n      code: 'ALL'\n    };\n    this._selectedColumns = this.cols;\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  loadOpportunityDropDown(target, type) {\n    this.opportunitiesservice.getOpportunityDropdownOptions(type).subscribe(res => {\n      this.dropdowns[target] = res?.data?.map(attr => ({\n        label: attr.description,\n        value: attr.code\n      })) ?? [];\n    });\n  }\n  getLabelFromDropdown(dropdownKey, value) {\n    const item = this.dropdowns[dropdownKey]?.find(opt => opt.value === value);\n    return item?.label || value;\n  }\n  loadOpportunities(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    const sortField = event.sortField;\n    const sortOrder = event.sortOrder;\n    const filter = this.selectedActions?.code;\n    this.opportunitiesservice.getOpportunities(page, pageSize, sortField, sortOrder, this.globalSearchTerm, filter).subscribe({\n      next: response => {\n        this.opportunities = (response?.data || []).map(item => ({\n          ...item,\n          need_help: !!item.need_help\n        }));\n        this.totalRecords = response?.meta?.pagination.total;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching opportunities', error);\n        this.loading = false;\n      }\n    });\n  }\n  onActionChange() {\n    // Re-trigger the lazy load with current dt1 state\n    const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\n      first: 0,\n      rows: 15\n    };\n    this.loadOpportunities(dt1State);\n  }\n  onGlobalFilter(table, event) {\n    this.loadOpportunities({\n      first: 0,\n      rows: 10\n    });\n  }\n  signup() {\n    this.router.navigate(['/store/opportunities/create']);\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function OpportunitiesComponent_Factory(t) {\n      return new (t || OpportunitiesComponent)(i0.ɵɵdirectiveInject(i1.OpportunitiesService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OpportunitiesComponent,\n      selectors: [[\"app-opportunities\"]],\n      viewQuery: function OpportunitiesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dt1 = _t.first);\n        }\n      },\n      decls: 23,\n      vars: 18,\n      consts: [[\"filter\", \"\"], [\"dt1\", \"\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-start\", \"justify-content-between\", \"flex-column\", \"gap-2\", \"md:flex-row\", \"md:align-items-center\"], [1, \"breadcrumb-sec\"], [3, \"model\", \"home\", \"styleClass\"], [1, \"flex\", \"align-items-start\", \"gap-3\", \"md:flex-row\", \"md:align-items-center\", \"flex-wrap\", \"w-full\", \"md:w-auto\"], [1, \"h-search-box\", \"w-full\", \"sm:w-24rem\"], [1, \"p-input-icon-right\", \"w-full\", \"md:w-24rem\"], [\"type\", \"text\", \"placeholder\", \"Search Opportunity\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"sm:w-24rem\", \"h-3rem\", \"px-3\", \"border-round-3xl\", \"border-1\", \"surface-border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"pi\", \"pi-search\", 2, \"right\", \"16px\"], [\"optionLabel\", \"name\", \"placeholder\", \"Filter\", 1, \"w-full\", \"sm:w-13rem\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"styleClass\"], [\"type\", \"button\", 1, \"h-3rem\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-round-3xl\", \"w-8rem\", \"justify-content-center\", \"gap-2\", \"font-semibold\", \"border-none\", \"ml-auto\", \"sm:ml-0\", 3, \"click\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onLazyLoad\", \"onColReorder\", \"value\", \"rows\", \"loading\", \"paginator\", \"totalRecords\", \"lazy\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\", \"table-checkbox\"], [\"pFrozenColumn\", \"\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", \"pl-3\", \"w-2rem\", \"text-center\"], [3, \"value\"], [\"pFrozenColumn\", \"\", 1, \"text-orange-600\", \"cursor-pointer\", \"font-medium\", \"underline\", 3, \"routerLink\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [1, \"text-blue-600\", \"cursor-pointer\", \"font-medium\", \"underline\", 3, \"routerLink\"], [3, \"binary\", \"ngModel\", \"disabled\"], [\"colspan\", \"13\", 1, \"border-round-left-lg\"]],\n      template: function OpportunitiesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n          i0.ɵɵelement(3, \"p-breadcrumb\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7)(6, \"span\", 8)(7, \"input\", 9, 0);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function OpportunitiesComponent_Template_input_ngModelChange_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.globalSearchTerm, $event) || (ctx.globalSearchTerm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"input\", function OpportunitiesComponent_Template_input_input_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const dt1_r2 = i0.ɵɵreference(18);\n            return i0.ɵɵresetView(ctx.onGlobalFilter(dt1_r2, $event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"i\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"p-dropdown\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function OpportunitiesComponent_Template_p_dropdown_ngModelChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedActions, $event) || (ctx.selectedActions = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onChange\", function OpportunitiesComponent_Template_p_dropdown_onChange_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onActionChange());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function OpportunitiesComponent_Template_button_click_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.signup());\n          });\n          i0.ɵɵelementStart(12, \"span\", 13);\n          i0.ɵɵtext(13, \"box_edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" Create \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p-multiSelect\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function OpportunitiesComponent_Template_p_multiSelect_ngModelChange_15_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 15)(17, \"p-table\", 16, 1);\n          i0.ɵɵlistener(\"onLazyLoad\", function OpportunitiesComponent_Template_p_table_onLazyLoad_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadOpportunities($event));\n          })(\"onColReorder\", function OpportunitiesComponent_Template_p_table_onColReorder_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onColumnReorder($event));\n          });\n          i0.ɵɵtemplate(19, OpportunitiesComponent_ng_template_19_Template, 9, 3, \"ng-template\", 17)(20, OpportunitiesComponent_ng_template_20_Template, 6, 4, \"ng-template\", 18)(21, OpportunitiesComponent_ng_template_21_Template, 3, 0, \"ng-template\", 19)(22, OpportunitiesComponent_ng_template_22_Template, 3, 0, \"ng-template\", 20);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.globalSearchTerm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"options\", ctx.Actions);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedActions);\n          i0.ɵɵproperty(\"styleClass\", \"w-full sm:w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.opportunities)(\"rows\", 14)(\"loading\", ctx.loading)(\"paginator\", true)(\"totalRecords\", ctx.totalRecords)(\"lazy\", true)(\"scrollable\", true)(\"reorderableColumns\", true);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgSwitch, i3.NgSwitchCase, i2.RouterLink, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.Table, i6.PrimeTemplate, i5.SortableColumn, i5.FrozenColumn, i5.ReorderableColumn, i5.TableCheckbox, i5.TableHeaderCheckbox, i7.Dropdown, i8.Breadcrumb, i9.Checkbox, i10.MultiSelect, i3.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r3", "sortOrder", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "OpportunitiesComponent_ng_template_19_ng_container_8_Template_th_click_1_listener", "col_r6", "ɵɵrestoreView", "_r5", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "OpportunitiesComponent_ng_template_19_ng_container_8_i_4_Template", "OpportunitiesComponent_ng_template_19_ng_container_8_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "OpportunitiesComponent_ng_template_19_Template_th_click_3_listener", "_r3", "OpportunitiesComponent_ng_template_19_i_6_Template", "OpportunitiesComponent_ng_template_19_i_7_Template", "OpportunitiesComponent_ng_template_19_ng_container_8_Template", "selectedColumns", "opportunity_r7", "opportunity_id", "name", "business_partner", "bp_full_name", "business_partner_owner", "expected_revenue_start_date", "ɵɵpipeBind2", "expected_revenue_end_date", "updatedAt", "last_changed_by", "expected_revenue_amount", "getLabelFromDropdown", "life_cycle_status_code", "probability_percent", "need_help", "OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_3_Template", "OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_4_Template", "OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_5_Template", "OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_6_Template", "OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_7_Template", "OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_8_Template", "OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_9_Template", "OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_10_Template", "OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_11_Template", "OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_12_Template", "OpportunitiesComponent_ng_template_20_ng_container_5_ng_container_13_Template", "col_r8", "OpportunitiesComponent_ng_template_20_ng_container_5_Template", "OpportunitiesComponent", "constructor", "opportunitiesservice", "router", "unsubscribe$", "opportunities", "totalRecords", "loading", "globalSearchTerm", "dropdowns", "opportunityStatus", "_selectedColumns", "cols", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "loadOpportunityDropDown", "breadcrumbitems", "label", "routerLink", "home", "icon", "Actions", "code", "selectedActions", "val", "filter", "col", "includes", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "target", "type", "getOpportunityDropdownOptions", "subscribe", "res", "map", "attr", "description", "value", "dropdownKey", "item", "find", "opt", "loadOpportunities", "page", "first", "rows", "pageSize", "getOpportunities", "next", "response", "meta", "pagination", "total", "error", "console", "onActionChange", "dt1State", "dt1", "createLazyLoadMetadata", "onGlobalFilter", "table", "signup", "navigate", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "OpportunitiesService", "i2", "Router", "selectors", "viewQuery", "OpportunitiesComponent_Query", "rf", "ctx", "ɵɵtwoWayListener", "OpportunitiesComponent_Template_input_ngModelChange_7_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "OpportunitiesComponent_Template_input_input_7_listener", "dt1_r2", "ɵɵreference", "OpportunitiesComponent_Template_p_dropdown_ngModelChange_10_listener", "OpportunitiesComponent_Template_p_dropdown_onChange_10_listener", "OpportunitiesComponent_Template_button_click_11_listener", "OpportunitiesComponent_Template_p_multiSelect_ngModelChange_15_listener", "OpportunitiesComponent_Template_p_table_onLazyLoad_17_listener", "OpportunitiesComponent_Template_p_table_onColReorder_17_listener", "OpportunitiesComponent_ng_template_19_Template", "OpportunitiesComponent_ng_template_20_Template", "OpportunitiesComponent_ng_template_21_Template", "OpportunitiesComponent_ng_template_22_Template", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\opportunities\\opportunities.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Subject } from 'rxjs';\r\nimport { OpportunitiesService } from './opportunities.service';\r\nimport { Table } from 'primeng/table';\r\nimport { Router } from '@angular/router';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n@Component({\r\n  selector: 'app-opportunities',\r\n  templateUrl: './opportunities.component.html',\r\n  styleUrl: './opportunities.component.scss',\r\n})\r\nexport class OpportunitiesComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  @ViewChild('dt1') dt1!: Table;\r\n  public breadcrumbitems: MenuItem[] | any;\r\n  public home: MenuItem | any;\r\n  public opportunities: any[] = [];\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n  public Actions: Actions[] | undefined;\r\n  public selectedActions: Actions | undefined;\r\n\r\n  public dropdowns: Record<string, any[]> = {\r\n    opportunityStatus: [],\r\n  };\r\n\r\n  constructor(\r\n    private opportunitiesservice: OpportunitiesService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'name', header: 'Name' },\r\n    { field: 'business_partner.bp_full_name', header: 'Account' },\r\n    { field: 'business_partner_owner.bp_full_name', header: 'Owner' },\r\n    { field: 'expected_revenue_start_date', header: 'Start Date' },\r\n    { field: 'expected_revenue_end_date', header: 'Close Date' },\r\n    { field: 'updatedAt', header: 'Last Updated Date' },\r\n    { field: 'last_changed_by', header: 'Last Updated By' },\r\n    { field: 'expected_revenue_amount', header: 'Expected Value' },\r\n    { field: 'life_cycle_status_code', header: 'Status' },\r\n    { field: 'probability_percent', header: 'Probability' },\r\n    { field: 'need_help', header: 'Need Help' },\r\n  ];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.opportunities.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.loadOpportunityDropDown('opportunityStatus', 'CRM_OPPORTUNITY_STATUS');\r\n    this.breadcrumbitems = [\r\n      { label: 'Opportunities', routerLink: ['/store/opportunities'] },\r\n    ];\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n    this.Actions = [\r\n      { name: 'All', code: 'ALL' },\r\n      { name: 'My Opportunities', code: 'MO' },\r\n    ];\r\n\r\n    this.selectedActions = { name: 'All', code: 'ALL' };\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter((col) => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n  loadOpportunityDropDown(target: string, type: string): void {\r\n    this.opportunitiesservice\r\n      .getOpportunityDropdownOptions(type)\r\n      .subscribe((res: any) => {\r\n        this.dropdowns[target] =\r\n          res?.data?.map((attr: any) => ({\r\n            label: attr.description,\r\n            value: attr.code,\r\n          })) ?? [];\r\n      });\r\n  }\r\n\r\n  getLabelFromDropdown(dropdownKey: string, value: string): string {\r\n    const item = this.dropdowns[dropdownKey]?.find(\r\n      (opt) => opt.value === value\r\n    );\r\n    return item?.label || value;\r\n  }\r\n\r\n  loadOpportunities(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n    const filter = this.selectedActions?.code;\r\n\r\n    this.opportunitiesservice\r\n      .getOpportunities(\r\n        page,\r\n        pageSize,\r\n        sortField,\r\n        sortOrder,\r\n        this.globalSearchTerm,\r\n        filter\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.opportunities = (response?.data || []).map((item: any) => ({\r\n            ...item,\r\n            need_help: !!item.need_help,\r\n          }));\r\n          this.totalRecords = response?.meta?.pagination.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching opportunities', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onActionChange() {\r\n    // Re-trigger the lazy load with current dt1 state\r\n    const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {\r\n      first: 0,\r\n      rows: 15,\r\n    };\r\n    this.loadOpportunities(dt1State);\r\n  }\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadOpportunities({ first: 0, rows: 10 });\r\n  }\r\n\r\n  signup() {\r\n    this.router.navigate(['/store/opportunities/create']);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-start justify-content-between flex-column gap-2 md:flex-row md:align-items-center\">\r\n        <div class=\"breadcrumb-sec\">\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <div class=\"flex align-items-start gap-3 md:flex-row md:align-items-center flex-wrap w-full md:w-auto\">\r\n            <!-- Search Box -->\r\n            <div class=\"h-search-box w-full sm:w-24rem\">\r\n                <span class=\"p-input-icon-right w-full md:w-24rem\">\r\n                    <input type=\"text\"  #filter [(ngModel)]=\"globalSearchTerm\" (input)=\"onGlobalFilter(dt1, $event)\"\r\n                        placeholder=\"Search Opportunity\"\r\n                        class=\"p-inputtext p-component p-element w-full sm:w-24rem h-3rem px-3 border-round-3xl border-1 surface-border\">\r\n                    <i class=\"pi pi-search\" style=\"right: 16px;\"></i>\r\n                </span>\r\n            </div>\r\n\r\n            <p-dropdown [options]=\"Actions\" [(ngModel)]=\"selectedActions\" (onChange)=\"onActionChange()\"\r\n                optionLabel=\"name\" placeholder=\"Filter\" class=\"w-full sm:w-13rem\"\r\n                [styleClass]=\"'w-full sm:w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" />\r\n            <button type=\"button\" (click)=\"signup()\"\r\n                class=\"h-3rem p-element p-ripple p-button p-component border-round-3xl w-8rem justify-content-center gap-2 font-semibold border-none ml-auto sm:ml-0\">\r\n                <span class=\"material-symbols-rounded text-2xl\">box_edit</span> Create\r\n            </button>\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n        <p-table #dt1 [value]=\"opportunities\" dataKey=\"id\" [rows]=\"14\" (onLazyLoad)=\"loadOpportunities($event)\"\r\n            [loading]=\"loading\" [paginator]=\"true\" [totalRecords]=\"totalRecords\" [lazy]=\"true\" responsiveLayout=\"scroll\"\r\n            [scrollable]=\"true\" class=\"scrollable-table\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center table-checkbox\">\r\n                        <p-tableHeaderCheckbox />\r\n                    </th>\r\n                    <th pFrozenColumn (click)=\"customSort('opportunity_id')\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            ID\r\n                            <i *ngIf=\"sortField === 'opportunity_id'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'opportunity_id'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-opportunity let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg pl-3 w-2rem text-center\">\r\n                        <p-tableCheckbox [value]=\"opportunity\" />\r\n                    </td>\r\n                    <td pFrozenColumn class=\"text-orange-600 cursor-pointer font-medium underline\"\r\n                        [routerLink]=\"'/store/opportunities/' + opportunity.opportunity_id\">\r\n                        {{ opportunity?.opportunity_id }}\r\n                    </td>\r\n\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'name'\">\r\n                                    <span class=\"text-blue-600 cursor-pointer font-medium underline\"\r\n                                        [routerLink]=\"'/store/opportunities/' + opportunity.opportunity_id\">\r\n                                        {{ opportunity?.name || '-' }}\r\n                                    </span>\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'business_partner.bp_full_name'\">\r\n                                    {{ opportunity?.business_partner?.bp_full_name || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'business_partner_owner.bp_full_name'\">\r\n                                    {{ opportunity?.business_partner_owner?.bp_full_name || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'expected_revenue_start_date'\">\r\n                                    {{ opportunity?.expected_revenue_start_date ?\r\n                                    (opportunity.expected_revenue_start_date | date: 'MM/dd/yyyy') : '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'expected_revenue_end_date'\">\r\n                                    {{ opportunity?.expected_revenue_end_date ? (opportunity.expected_revenue_end_date |\r\n                                    date: 'MM/dd/yyyy') : '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'updatedAt'\">\r\n                                    {{ opportunity?.updatedAt ? (opportunity.updatedAt | date: 'MM/dd/yyyy hh:mm a') :\r\n                                    '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'last_changed_by'\">\r\n                                    {{ opportunity?.last_changed_by || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'expected_revenue_amount'\">\r\n                                    {{ opportunity?.expected_revenue_amount || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'life_cycle_status_code'\">\r\n                                    {{ getLabelFromDropdown('opportunityStatus', opportunity?.life_cycle_status_code) ||\r\n                                    '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'probability_percent'\">\r\n                                    {{ opportunity?.probability_percent || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'need_help'\">\r\n                                    <p-checkbox [binary]=\"true\" [ngModel]=\"opportunity.need_help\"\r\n                                        [disabled]=\"true\"></p-checkbox>\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"13\" class=\"border-round-left-lg\">No opportunities found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"13\" class=\"border-round-left-lg\">Loading opportunities data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,OAAO,QAAQ,MAAM;;;;;;;;;;;;;;;IC2CFC,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAAsE;;;;;IAOlED,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;;IAP3ED,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,aAAqF;IAAhCN,EAAA,CAAAO,UAAA,mBAAAC,kFAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAC,iEAAA,gBACkF,IAAAC,iEAAA,gBAEvB;IAEnEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;IAG7BhB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;;;;;;IAnB7ChB,EADJ,CAAAM,cAAA,SAAI,aACsF;IAClFN,EAAA,CAAAC,SAAA,4BAAyB;IAC7BD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAM,cAAA,aAAyD;IAAvCN,EAAA,CAAAO,UAAA,mBAAAmB,mEAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,gBAAgB,CAAC;IAAA,EAAC;IACpDf,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,WACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAU,kDAAA,gBACkF,IAAAC,kDAAA,gBAEhB;IAE1E7B,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAY,6DAAA,2BAAkD;IAWtD9B,EAAA,CAAAqB,YAAA,EAAK;;;;IAjBWrB,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,sBAAoC;IAGpCzB,EAAA,CAAAsB,SAAA,EAAoC;IAApCtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,sBAAoC;IAGlBzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IA2BpC/B,EAAA,CAAAK,uBAAA,GAAqC;IACjCL,EAAA,CAAAM,cAAA,eACwE;IACpEN,EAAA,CAAAiB,MAAA,GACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;;IAFHrB,EAAA,CAAAsB,SAAA,EAAmE;IAAnEtB,EAAA,CAAAE,UAAA,yCAAA8B,cAAA,CAAAC,cAAA,CAAmE;IACnEjC,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,cAAA,kBAAAA,cAAA,CAAAE,IAAA,cACJ;;;;;IAGJlC,EAAA,CAAAK,uBAAA,GAA8D;IAC1DL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,cAAA,kBAAAA,cAAA,CAAAG,gBAAA,kBAAAH,cAAA,CAAAG,gBAAA,CAAAC,YAAA,cACJ;;;;;IAEApC,EAAA,CAAAK,uBAAA,GAAoE;IAChEL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,cAAA,kBAAAA,cAAA,CAAAK,sBAAA,kBAAAL,cAAA,CAAAK,sBAAA,CAAAD,YAAA,cACJ;;;;;IAEApC,EAAA,CAAAK,uBAAA,GAA4D;IACxDL,EAAA,CAAAiB,MAAA,GAEJ;;;;;;IAFIjB,EAAA,CAAAsB,SAAA,EAEJ;IAFItB,EAAA,CAAAuB,kBAAA,OAAAS,cAAA,kBAAAA,cAAA,CAAAM,2BAAA,IAAAtC,EAAA,CAAAuC,WAAA,OAAAP,cAAA,CAAAM,2BAAA,2BAEJ;;;;;IAEAtC,EAAA,CAAAK,uBAAA,GAA0D;IACtDL,EAAA,CAAAiB,MAAA,GAEJ;;;;;;IAFIjB,EAAA,CAAAsB,SAAA,EAEJ;IAFItB,EAAA,CAAAuB,kBAAA,OAAAS,cAAA,kBAAAA,cAAA,CAAAQ,yBAAA,IAAAxC,EAAA,CAAAuC,WAAA,OAAAP,cAAA,CAAAQ,yBAAA,2BAEJ;;;;;IAEAxC,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAiB,MAAA,GAEJ;;;;;;IAFIjB,EAAA,CAAAsB,SAAA,EAEJ;IAFItB,EAAA,CAAAuB,kBAAA,OAAAS,cAAA,kBAAAA,cAAA,CAAAS,SAAA,IAAAzC,EAAA,CAAAuC,WAAA,OAAAP,cAAA,CAAAS,SAAA,mCAEJ;;;;;IAEAzC,EAAA,CAAAK,uBAAA,GAAgD;IAC5CL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,cAAA,kBAAAA,cAAA,CAAAU,eAAA,cACJ;;;;;IAEA1C,EAAA,CAAAK,uBAAA,GAAwD;IACpDL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,cAAA,kBAAAA,cAAA,CAAAW,uBAAA,cACJ;;;;;IAEA3C,EAAA,CAAAK,uBAAA,GAAuD;IACnDL,EAAA,CAAAiB,MAAA,GAEJ;;;;;;IAFIjB,EAAA,CAAAsB,SAAA,EAEJ;IAFItB,EAAA,CAAAuB,kBAAA,MAAApB,MAAA,CAAAyC,oBAAA,sBAAAZ,cAAA,kBAAAA,cAAA,CAAAa,sBAAA,cAEJ;;;;;IAEA7C,EAAA,CAAAK,uBAAA,GAAoD;IAChDL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,OAAAS,cAAA,kBAAAA,cAAA,CAAAc,mBAAA,cACJ;;;;;IAEA9C,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAC,SAAA,qBACmC;;;;;IADvBD,EAAA,CAAAsB,SAAA,EAAe;IACvBtB,EADQ,CAAAE,UAAA,gBAAe,YAAA8B,cAAA,CAAAe,SAAA,CAAkC,kBACxC;;;;;IApDrC/C,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IAgDjCL,EA/CA,CAAAkB,UAAA,IAAA8B,4EAAA,2BAAqC,IAAAC,4EAAA,2BAOyB,IAAAC,4EAAA,2BAIM,IAAAC,4EAAA,2BAIR,IAAAC,4EAAA,2BAKF,IAAAC,4EAAA,2BAKhB,IAAAC,4EAAA,2BAKM,KAAAC,6EAAA,2BAIQ,KAAAC,6EAAA,2BAID,KAAAC,6EAAA,2BAKH,KAAAC,6EAAA,2BAIV;;IAMlD1D,EAAA,CAAAqB,YAAA,EAAK;;;;;IAtDarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAAyD,MAAA,CAAA3C,KAAA,CAAsB;IACjBhB,EAAA,CAAAsB,SAAA,EAAoB;IAApBtB,EAAA,CAAAE,UAAA,wBAAoB;IAOpBF,EAAA,CAAAsB,SAAA,EAA6C;IAA7CtB,EAAA,CAAAE,UAAA,iDAA6C;IAI7CF,EAAA,CAAAsB,SAAA,EAAmD;IAAnDtB,EAAA,CAAAE,UAAA,uDAAmD;IAInDF,EAAA,CAAAsB,SAAA,EAA2C;IAA3CtB,EAAA,CAAAE,UAAA,+CAA2C;IAK3CF,EAAA,CAAAsB,SAAA,EAAyC;IAAzCtB,EAAA,CAAAE,UAAA,6CAAyC;IAKzCF,EAAA,CAAAsB,SAAA,EAAyB;IAAzBtB,EAAA,CAAAE,UAAA,6BAAyB;IAKzBF,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAAE,UAAA,mCAA+B;IAI/BF,EAAA,CAAAsB,SAAA,EAAuC;IAAvCtB,EAAA,CAAAE,UAAA,2CAAuC;IAIvCF,EAAA,CAAAsB,SAAA,EAAsC;IAAtCtB,EAAA,CAAAE,UAAA,0CAAsC;IAKtCF,EAAA,CAAAsB,SAAA,EAAmC;IAAnCtB,EAAA,CAAAE,UAAA,uCAAmC;IAInCF,EAAA,CAAAsB,SAAA,EAAyB;IAAzBtB,EAAA,CAAAE,UAAA,6BAAyB;;;;;IA1DpDF,EADJ,CAAAM,cAAA,aAA2B,aACgD;IACnEN,EAAA,CAAAC,SAAA,0BAAyC;IAC7CD,EAAA,CAAAqB,YAAA,EAAK;IACLrB,EAAA,CAAAM,cAAA,aACwE;IACpEN,EAAA,CAAAiB,MAAA,GACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;IAELrB,EAAA,CAAAkB,UAAA,IAAA0C,6DAAA,6BAAkD;IA0DtD5D,EAAA,CAAAqB,YAAA,EAAK;;;;;IAjEoBrB,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAAE,UAAA,UAAA8B,cAAA,CAAqB;IAGtChC,EAAA,CAAAsB,SAAA,EAAmE;IAAnEtB,EAAA,CAAAE,UAAA,yCAAA8B,cAAA,CAAAC,cAAA,CAAmE;IACnEjC,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAS,cAAA,kBAAAA,cAAA,CAAAC,cAAA,MACJ;IAE8BjC,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IA+DhD/B,EADJ,CAAAM,cAAA,SAAI,aAC8C;IAAAN,EAAA,CAAAiB,MAAA,8BAAuB;IACzEjB,EADyE,CAAAqB,YAAA,EAAK,EACzE;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aAC8C;IAAAN,EAAA,CAAAiB,MAAA,+CAAwC;IAC1FjB,EAD0F,CAAAqB,YAAA,EAAK,EAC1F;;;AD5HrB,OAAM,MAAOwC,sBAAsB;EAgBjCC,YACUC,oBAA0C,EAC1CC,MAAc;IADd,KAAAD,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,MAAM,GAANA,MAAM;IAjBR,KAAAC,YAAY,GAAG,IAAIlE,OAAO,EAAQ;IAInC,KAAAmE,aAAa,GAAU,EAAE;IACzB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,gBAAgB,GAAW,EAAE;IAI7B,KAAAC,SAAS,GAA0B;MACxCC,iBAAiB,EAAE;KACpB;IAOO,KAAAC,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAEzD,KAAK,EAAE,MAAM;MAAEQ,MAAM,EAAE;IAAM,CAAE,EACjC;MAAER,KAAK,EAAE,+BAA+B;MAAEQ,MAAM,EAAE;IAAS,CAAE,EAC7D;MAAER,KAAK,EAAE,qCAAqC;MAAEQ,MAAM,EAAE;IAAO,CAAE,EACjE;MAAER,KAAK,EAAE,6BAA6B;MAAEQ,MAAM,EAAE;IAAY,CAAE,EAC9D;MAAER,KAAK,EAAE,2BAA2B;MAAEQ,MAAM,EAAE;IAAY,CAAE,EAC5D;MAAER,KAAK,EAAE,WAAW;MAAEQ,MAAM,EAAE;IAAmB,CAAE,EACnD;MAAER,KAAK,EAAE,iBAAiB;MAAEQ,MAAM,EAAE;IAAiB,CAAE,EACvD;MAAER,KAAK,EAAE,yBAAyB;MAAEQ,MAAM,EAAE;IAAgB,CAAE,EAC9D;MAAER,KAAK,EAAE,wBAAwB;MAAEQ,MAAM,EAAE;IAAQ,CAAE,EACrD;MAAER,KAAK,EAAE,qBAAqB;MAAEQ,MAAM,EAAE;IAAa,CAAE,EACvD;MAAER,KAAK,EAAE,WAAW;MAAEQ,MAAM,EAAE;IAAW,CAAE,CAC5C;IAED,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAArB,SAAS,GAAW,CAAC;EAnBlB;EAqBHW,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACS,SAAS,KAAKT,KAAK,EAAE;MAC5B,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACqB,SAAS,GAAGT,KAAK;MACtB,IAAI,CAACZ,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAAC8D,aAAa,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/B,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAE3D,KAAK,CAAC;MAC9C,MAAM+D,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAE5D,KAAK,CAAC;MAE9C,IAAIgE,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAGH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;MAE5D,OAAO,IAAI,CAAC3E,SAAS,GAAG4E,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAElE,KAAa;IACvC,IAAI,CAACkE,IAAI,IAAI,CAAClE,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAACmE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAAClE,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAACoE,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAACC,uBAAuB,CAAC,mBAAmB,EAAE,wBAAwB,CAAC;IAC3E,IAAI,CAACC,eAAe,GAAG,CACrB;MAAEC,KAAK,EAAE,eAAe;MAAEC,UAAU,EAAE,CAAC,sBAAsB;IAAC,CAAE,CACjE;IACD,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAEF,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAC7D,IAAI,CAACG,OAAO,GAAG,CACb;MAAE7D,IAAI,EAAE,KAAK;MAAE8D,IAAI,EAAE;IAAK,CAAE,EAC5B;MAAE9D,IAAI,EAAE,kBAAkB;MAAE8D,IAAI,EAAE;IAAI,CAAE,CACzC;IAED,IAAI,CAACC,eAAe,GAAG;MAAE/D,IAAI,EAAE,KAAK;MAAE8D,IAAI,EAAE;IAAK,CAAE;IAEnD,IAAI,CAACxB,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEA,IAAI1C,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACyC,gBAAgB;EAC9B;EAEA,IAAIzC,eAAeA,CAACmE,GAAU;IAC5B,IAAI,CAAC1B,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAAC0B,MAAM,CAAEC,GAAG,IAAKF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACtE;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAAChC,gBAAgB,CAAC+B,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAACjC,gBAAgB,CAACkC,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAACjC,gBAAgB,CAACkC,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;EAEAf,uBAAuBA,CAACmB,MAAc,EAAEC,IAAY;IAClD,IAAI,CAAC9C,oBAAoB,CACtB+C,6BAA6B,CAACD,IAAI,CAAC,CACnCE,SAAS,CAAEC,GAAQ,IAAI;MACtB,IAAI,CAAC1C,SAAS,CAACsC,MAAM,CAAC,GACpBI,GAAG,EAAE9B,IAAI,EAAE+B,GAAG,CAAEC,IAAS,KAAM;QAC7BvB,KAAK,EAAEuB,IAAI,CAACC,WAAW;QACvBC,KAAK,EAAEF,IAAI,CAAClB;OACb,CAAC,CAAC,IAAI,EAAE;IACb,CAAC,CAAC;EACN;EAEApD,oBAAoBA,CAACyE,WAAmB,EAAED,KAAa;IACrD,MAAME,IAAI,GAAG,IAAI,CAAChD,SAAS,CAAC+C,WAAW,CAAC,EAAEE,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAACJ,KAAK,KAAKA,KAAK,CAC7B;IACD,OAAOE,IAAI,EAAE3B,KAAK,IAAIyB,KAAK;EAC7B;EAEAK,iBAAiBA,CAAClB,KAAU;IAC1B,IAAI,CAACnC,OAAO,GAAG,IAAI;IACnB,MAAMsD,IAAI,GAAGnB,KAAK,CAACoB,KAAK,GAAGpB,KAAK,CAACqB,IAAI,GAAG,CAAC;IACzC,MAAMC,QAAQ,GAAGtB,KAAK,CAACqB,IAAI;IAC3B,MAAMnG,SAAS,GAAG8E,KAAK,CAAC9E,SAAS;IACjC,MAAMrB,SAAS,GAAGmG,KAAK,CAACnG,SAAS;IACjC,MAAM+F,MAAM,GAAG,IAAI,CAACF,eAAe,EAAED,IAAI;IAEzC,IAAI,CAACjC,oBAAoB,CACtB+D,gBAAgB,CACfJ,IAAI,EACJG,QAAQ,EACRpG,SAAS,EACTrB,SAAS,EACT,IAAI,CAACiE,gBAAgB,EACrB8B,MAAM,CACP,CACAY,SAAS,CAAC;MACTgB,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAC9D,aAAa,GAAG,CAAC8D,QAAQ,EAAE9C,IAAI,IAAI,EAAE,EAAE+B,GAAG,CAAEK,IAAS,KAAM;UAC9D,GAAGA,IAAI;UACPvE,SAAS,EAAE,CAAC,CAACuE,IAAI,CAACvE;SACnB,CAAC,CAAC;QACH,IAAI,CAACoB,YAAY,GAAG6D,QAAQ,EAAEC,IAAI,EAAEC,UAAU,CAACC,KAAK;QACpD,IAAI,CAAC/D,OAAO,GAAG,KAAK;MACtB,CAAC;MACDgE,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,IAAI,CAAChE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEAkE,cAAcA,CAAA;IACZ;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,GAAG,EAAEC,sBAAsB,GAAE,CAAE,IAAI;MACvDd,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;KACP;IACD,IAAI,CAACH,iBAAiB,CAACc,QAAQ,CAAC;EAClC;EAEAG,cAAcA,CAACC,KAAY,EAAEpC,KAAY;IACvC,IAAI,CAACkB,iBAAiB,CAAC;MAAEE,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAChD;EAEAgB,MAAMA,CAAA;IACJ,IAAI,CAAC5E,MAAM,CAAC6E,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;EACvD;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC7E,YAAY,CAAC8D,IAAI,EAAE;IACxB,IAAI,CAAC9D,YAAY,CAAC8E,QAAQ,EAAE;EAC9B;;;uBAjLWlF,sBAAsB,EAAA7D,EAAA,CAAAgJ,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAlJ,EAAA,CAAAgJ,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAtBvF,sBAAsB;MAAAwF,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UClB3BxJ,EAFR,CAAAM,cAAA,aAA8D,aACsE,aAChG;UACxBN,EAAA,CAAAC,SAAA,sBAA+F;UACnGD,EAAA,CAAAqB,YAAA,EAAM;UAKMrB,EAJZ,CAAAM,cAAA,aAAuG,aAEvD,cACW,kBAGsE;UAFzFN,EAAA,CAAA0J,gBAAA,2BAAAC,+DAAAC,MAAA;YAAA5J,EAAA,CAAAU,aAAA,CAAAmJ,GAAA;YAAA7J,EAAA,CAAA8J,kBAAA,CAAAL,GAAA,CAAApF,gBAAA,EAAAuF,MAAA,MAAAH,GAAA,CAAApF,gBAAA,GAAAuF,MAAA;YAAA,OAAA5J,EAAA,CAAAc,WAAA,CAAA8I,MAAA;UAAA,EAA8B;UAAC5J,EAAA,CAAAO,UAAA,mBAAAwJ,uDAAAH,MAAA;YAAA5J,EAAA,CAAAU,aAAA,CAAAmJ,GAAA;YAAA,MAAAG,MAAA,GAAAhK,EAAA,CAAAiK,WAAA;YAAA,OAAAjK,EAAA,CAAAc,WAAA,CAAS2I,GAAA,CAAAf,cAAA,CAAAsB,MAAA,EAAAJ,MAAA,CAA2B;UAAA,EAAC;UAAhG5J,EAAA,CAAAqB,YAAA,EAEqH;UACrHrB,EAAA,CAAAC,SAAA,YAAiD;UAEzDD,EADI,CAAAqB,YAAA,EAAO,EACL;UAENrB,EAAA,CAAAM,cAAA,sBAEmH;UAFnFN,EAAA,CAAA0J,gBAAA,2BAAAQ,qEAAAN,MAAA;YAAA5J,EAAA,CAAAU,aAAA,CAAAmJ,GAAA;YAAA7J,EAAA,CAAA8J,kBAAA,CAAAL,GAAA,CAAAxD,eAAA,EAAA2D,MAAA,MAAAH,GAAA,CAAAxD,eAAA,GAAA2D,MAAA;YAAA,OAAA5J,EAAA,CAAAc,WAAA,CAAA8I,MAAA;UAAA,EAA6B;UAAC5J,EAAA,CAAAO,UAAA,sBAAA4J,gEAAA;YAAAnK,EAAA,CAAAU,aAAA,CAAAmJ,GAAA;YAAA,OAAA7J,EAAA,CAAAc,WAAA,CAAY2I,GAAA,CAAAnB,cAAA,EAAgB;UAAA,EAAC;UAA3FtI,EAAA,CAAAqB,YAAA,EAEmH;UACnHrB,EAAA,CAAAM,cAAA,kBAC0J;UADpIN,EAAA,CAAAO,UAAA,mBAAA6J,yDAAA;YAAApK,EAAA,CAAAU,aAAA,CAAAmJ,GAAA;YAAA,OAAA7J,EAAA,CAAAc,WAAA,CAAS2I,GAAA,CAAAb,MAAA,EAAQ;UAAA,EAAC;UAEpC5I,EAAA,CAAAM,cAAA,gBAAgD;UAAAN,EAAA,CAAAiB,MAAA,gBAAQ;UAAAjB,EAAA,CAAAqB,YAAA,EAAO;UAACrB,EAAA,CAAAiB,MAAA,gBACpE;UAAAjB,EAAA,CAAAqB,YAAA,EAAS;UAETrB,EAAA,CAAAM,cAAA,yBAE+I;UAF/GN,EAAA,CAAA0J,gBAAA,2BAAAW,wEAAAT,MAAA;YAAA5J,EAAA,CAAAU,aAAA,CAAAmJ,GAAA;YAAA7J,EAAA,CAAA8J,kBAAA,CAAAL,GAAA,CAAA1H,eAAA,EAAA6H,MAAA,MAAAH,GAAA,CAAA1H,eAAA,GAAA6H,MAAA;YAAA,OAAA5J,EAAA,CAAAc,WAAA,CAAA8I,MAAA;UAAA,EAA6B;UAKrE5J,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAGFrB,EADJ,CAAAM,cAAA,eAAuB,sBAI0B;UAAzCN,EAH2D,CAAAO,UAAA,wBAAA+J,+DAAAV,MAAA;YAAA5J,EAAA,CAAAU,aAAA,CAAAmJ,GAAA;YAAA,OAAA7J,EAAA,CAAAc,WAAA,CAAc2I,GAAA,CAAAhC,iBAAA,CAAAmC,MAAA,CAAyB;UAAA,EAAC,0BAAAW,iEAAAX,MAAA;YAAA5J,EAAA,CAAAU,aAAA,CAAAmJ,GAAA;YAAA,OAAA7J,EAAA,CAAAc,WAAA,CAGnF2I,GAAA,CAAAnD,eAAA,CAAAsD,MAAA,CAAuB;UAAA,EAAC;UA0GxC5J,EAxGA,CAAAkB,UAAA,KAAAsJ,8CAAA,0BAAgC,KAAAC,8CAAA,0BA4BoC,KAAAC,8CAAA,0BAuE9B,KAAAC,8CAAA,0BAKD;UAOjD3K,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;;;UAjJoBrB,EAAA,CAAAsB,SAAA,GAAyB;UAAetB,EAAxC,CAAAE,UAAA,UAAAuJ,GAAA,CAAA/D,eAAA,CAAyB,SAAA+D,GAAA,CAAA5D,IAAA,CAAc,uCAAuC;UAMxD7F,EAAA,CAAAsB,SAAA,GAA8B;UAA9BtB,EAAA,CAAA4K,gBAAA,YAAAnB,GAAA,CAAApF,gBAAA,CAA8B;UAOtDrE,EAAA,CAAAsB,SAAA,GAAmB;UAAnBtB,EAAA,CAAAE,UAAA,YAAAuJ,GAAA,CAAA1D,OAAA,CAAmB;UAAC/F,EAAA,CAAA4K,gBAAA,YAAAnB,GAAA,CAAAxD,eAAA,CAA6B;UAEzDjG,EAAA,CAAAE,UAAA,6GAA4G;UAMjGF,EAAA,CAAAsB,SAAA,GAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAAuJ,GAAA,CAAAhF,IAAA,CAAgB;UAACzE,EAAA,CAAA4K,gBAAA,YAAAnB,GAAA,CAAA1H,eAAA,CAA6B;UAEzD/B,EAAA,CAAAE,UAAA,2IAA0I;UAMpIF,EAAA,CAAAsB,SAAA,GAAuB;UAEYtB,EAFnC,CAAAE,UAAA,UAAAuJ,GAAA,CAAAvF,aAAA,CAAuB,YAAyB,YAAAuF,GAAA,CAAArF,OAAA,CACvC,mBAAmB,iBAAAqF,GAAA,CAAAtF,YAAA,CAA8B,cAAc,oBAC/D,4BAAqD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}