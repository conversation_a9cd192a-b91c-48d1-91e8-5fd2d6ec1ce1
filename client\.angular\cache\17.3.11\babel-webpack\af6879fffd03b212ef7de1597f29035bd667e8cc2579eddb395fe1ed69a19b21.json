{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Belarusian [be]\n//! author : <PERSON> : https://github.com/demidov91\n//! author: Praleska: http://praleska.pro/\n//! Author : <PERSON><PERSON><PERSON> : https://github.com/Oire\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function plural(word, num) {\n    var forms = word.split('_');\n    return num % 10 === 1 && num % 100 !== 11 ? forms[0] : num % 10 >= 2 && num % 10 <= 4 && (num % 100 < 10 || num % 100 >= 20) ? forms[1] : forms[2];\n  }\n  function relativeTimeWithPlural(number, withoutSuffix, key) {\n    var format = {\n      ss: withoutSuffix ? 'секунда_секунды_секунд' : 'секунду_секунды_секунд',\n      mm: withoutSuffix ? 'хвіліна_хвіліны_хвілін' : 'хвіліну_хвіліны_хвілін',\n      hh: withoutSuffix ? 'гадзіна_гадзіны_гадзін' : 'гадзіну_гадзіны_гадзін',\n      dd: 'дзень_дні_дзён',\n      MM: 'месяц_месяцы_месяцаў',\n      yy: 'год_гады_гадоў'\n    };\n    if (key === 'm') {\n      return withoutSuffix ? 'хвіліна' : 'хвіліну';\n    } else if (key === 'h') {\n      return withoutSuffix ? 'гадзіна' : 'гадзіну';\n    } else {\n      return number + ' ' + plural(format[key], +number);\n    }\n  }\n  var be = moment.defineLocale('be', {\n    months: {\n      format: 'студзеня_лютага_сакавіка_красавіка_траўня_чэрвеня_ліпеня_жніўня_верасня_кастрычніка_лістапада_снежня'.split('_'),\n      standalone: 'студзень_люты_сакавік_красавік_травень_чэрвень_ліпень_жнівень_верасень_кастрычнік_лістапад_снежань'.split('_')\n    },\n    monthsShort: 'студ_лют_сак_крас_трав_чэрв_ліп_жнів_вер_каст_ліст_снеж'.split('_'),\n    weekdays: {\n      format: 'нядзелю_панядзелак_аўторак_сераду_чацвер_пятніцу_суботу'.split('_'),\n      standalone: 'нядзеля_панядзелак_аўторак_серада_чацвер_пятніца_субота'.split('_'),\n      isFormat: /\\[ ?[Ууў] ?(?:мінулую|наступную)? ?\\] ?dddd/\n    },\n    weekdaysShort: 'нд_пн_ат_ср_чц_пт_сб'.split('_'),\n    weekdaysMin: 'нд_пн_ат_ср_чц_пт_сб'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY г.',\n      LLL: 'D MMMM YYYY г., HH:mm',\n      LLLL: 'dddd, D MMMM YYYY г., HH:mm'\n    },\n    calendar: {\n      sameDay: '[Сёння ў] LT',\n      nextDay: '[Заўтра ў] LT',\n      lastDay: '[Учора ў] LT',\n      nextWeek: function () {\n        return '[У] dddd [ў] LT';\n      },\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n          case 3:\n          case 5:\n          case 6:\n            return '[У мінулую] dddd [ў] LT';\n          case 1:\n          case 2:\n          case 4:\n            return '[У мінулы] dddd [ў] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'праз %s',\n      past: '%s таму',\n      s: 'некалькі секунд',\n      m: relativeTimeWithPlural,\n      mm: relativeTimeWithPlural,\n      h: relativeTimeWithPlural,\n      hh: relativeTimeWithPlural,\n      d: 'дзень',\n      dd: relativeTimeWithPlural,\n      M: 'месяц',\n      MM: relativeTimeWithPlural,\n      y: 'год',\n      yy: relativeTimeWithPlural\n    },\n    meridiemParse: /ночы|раніцы|дня|вечара/,\n    isPM: function (input) {\n      return /^(дня|вечара)$/.test(input);\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'ночы';\n      } else if (hour < 12) {\n        return 'раніцы';\n      } else if (hour < 17) {\n        return 'дня';\n      } else {\n        return 'вечара';\n      }\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(і|ы|га)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'M':\n        case 'd':\n        case 'DDD':\n        case 'w':\n        case 'W':\n          return (number % 10 === 2 || number % 10 === 3) && number % 100 !== 12 && number % 100 !== 13 ? number + '-і' : number + '-ы';\n        case 'D':\n          return number + '-га';\n        default:\n          return number;\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return be;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "plural", "word", "num", "forms", "split", "relativeTimeWithPlural", "number", "withoutSuffix", "key", "format", "ss", "mm", "hh", "dd", "MM", "yy", "be", "defineLocale", "months", "standalone", "monthsShort", "weekdays", "isFormat", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "lastDay", "nextWeek", "lastWeek", "day", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "m", "h", "d", "M", "y", "meridiemParse", "isPM", "input", "test", "meridiem", "hour", "minute", "isLower", "dayOfMonthOrdinalParse", "ordinal", "period", "week", "dow", "doy"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/moment/locale/be.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Belarusian [be]\n//! author : <PERSON> : https://github.com/demidov91\n//! author: Praleska: http://praleska.pro/\n//! Author : <PERSON><PERSON><PERSON> : https://github.com/Oire\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function plural(word, num) {\n        var forms = word.split('_');\n        return num % 10 === 1 && num % 100 !== 11\n            ? forms[0]\n            : num % 10 >= 2 && num % 10 <= 4 && (num % 100 < 10 || num % 100 >= 20)\n              ? forms[1]\n              : forms[2];\n    }\n    function relativeTimeWithPlural(number, withoutSuffix, key) {\n        var format = {\n            ss: withoutSuffix ? 'секунда_секунды_секунд' : 'секунду_секунды_секунд',\n            mm: withoutSuffix ? 'хвіліна_хвіліны_хвілін' : 'хвіліну_хвіліны_хвілін',\n            hh: withoutSuffix ? 'гадзіна_гадзіны_гадзін' : 'гадзіну_гадзіны_гадзін',\n            dd: 'дзень_дні_дзён',\n            MM: 'месяц_месяцы_месяцаў',\n            yy: 'год_гады_гадоў',\n        };\n        if (key === 'm') {\n            return withoutSuffix ? 'хвіліна' : 'хвіліну';\n        } else if (key === 'h') {\n            return withoutSuffix ? 'гадзіна' : 'гадзіну';\n        } else {\n            return number + ' ' + plural(format[key], +number);\n        }\n    }\n\n    var be = moment.defineLocale('be', {\n        months: {\n            format: 'студзеня_лютага_сакавіка_красавіка_траўня_чэрвеня_ліпеня_жніўня_верасня_кастрычніка_лістапада_снежня'.split(\n                '_'\n            ),\n            standalone:\n                'студзень_люты_сакавік_красавік_травень_чэрвень_ліпень_жнівень_верасень_кастрычнік_лістапад_снежань'.split(\n                    '_'\n                ),\n        },\n        monthsShort:\n            'студ_лют_сак_крас_трав_чэрв_ліп_жнів_вер_каст_ліст_снеж'.split('_'),\n        weekdays: {\n            format: 'нядзелю_панядзелак_аўторак_сераду_чацвер_пятніцу_суботу'.split(\n                '_'\n            ),\n            standalone:\n                'нядзеля_панядзелак_аўторак_серада_чацвер_пятніца_субота'.split(\n                    '_'\n                ),\n            isFormat: /\\[ ?[Ууў] ?(?:мінулую|наступную)? ?\\] ?dddd/,\n        },\n        weekdaysShort: 'нд_пн_ат_ср_чц_пт_сб'.split('_'),\n        weekdaysMin: 'нд_пн_ат_ср_чц_пт_сб'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY г.',\n            LLL: 'D MMMM YYYY г., HH:mm',\n            LLLL: 'dddd, D MMMM YYYY г., HH:mm',\n        },\n        calendar: {\n            sameDay: '[Сёння ў] LT',\n            nextDay: '[Заўтра ў] LT',\n            lastDay: '[Учора ў] LT',\n            nextWeek: function () {\n                return '[У] dddd [ў] LT';\n            },\n            lastWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                    case 3:\n                    case 5:\n                    case 6:\n                        return '[У мінулую] dddd [ў] LT';\n                    case 1:\n                    case 2:\n                    case 4:\n                        return '[У мінулы] dddd [ў] LT';\n                }\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'праз %s',\n            past: '%s таму',\n            s: 'некалькі секунд',\n            m: relativeTimeWithPlural,\n            mm: relativeTimeWithPlural,\n            h: relativeTimeWithPlural,\n            hh: relativeTimeWithPlural,\n            d: 'дзень',\n            dd: relativeTimeWithPlural,\n            M: 'месяц',\n            MM: relativeTimeWithPlural,\n            y: 'год',\n            yy: relativeTimeWithPlural,\n        },\n        meridiemParse: /ночы|раніцы|дня|вечара/,\n        isPM: function (input) {\n            return /^(дня|вечара)$/.test(input);\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 4) {\n                return 'ночы';\n            } else if (hour < 12) {\n                return 'раніцы';\n            } else if (hour < 17) {\n                return 'дня';\n            } else {\n                return 'вечара';\n            }\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}-(і|ы|га)/,\n        ordinal: function (number, period) {\n            switch (period) {\n                case 'M':\n                case 'd':\n                case 'DDD':\n                case 'w':\n                case 'W':\n                    return (number % 10 === 2 || number % 10 === 3) &&\n                        number % 100 !== 12 &&\n                        number % 100 !== 13\n                        ? number + '-і'\n                        : number + '-ы';\n                case 'D':\n                    return number + '-га';\n                default:\n                    return number;\n            }\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return be;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,SAASC,MAAMA,CAACC,IAAI,EAAEC,GAAG,EAAE;IACvB,IAAIC,KAAK,GAAGF,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;IAC3B,OAAOF,GAAG,GAAG,EAAE,KAAK,CAAC,IAAIA,GAAG,GAAG,GAAG,KAAK,EAAE,GACnCC,KAAK,CAAC,CAAC,CAAC,GACRD,GAAG,GAAG,EAAE,IAAI,CAAC,IAAIA,GAAG,GAAG,EAAE,IAAI,CAAC,KAAKA,GAAG,GAAG,GAAG,GAAG,EAAE,IAAIA,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC,GACnEC,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC;EACpB;EACA,SAASE,sBAAsBA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAE;IACxD,IAAIC,MAAM,GAAG;MACTC,EAAE,EAAEH,aAAa,GAAG,wBAAwB,GAAG,wBAAwB;MACvEI,EAAE,EAAEJ,aAAa,GAAG,wBAAwB,GAAG,wBAAwB;MACvEK,EAAE,EAAEL,aAAa,GAAG,wBAAwB,GAAG,wBAAwB;MACvEM,EAAE,EAAE,gBAAgB;MACpBC,EAAE,EAAE,sBAAsB;MAC1BC,EAAE,EAAE;IACR,CAAC;IACD,IAAIP,GAAG,KAAK,GAAG,EAAE;MACb,OAAOD,aAAa,GAAG,SAAS,GAAG,SAAS;IAChD,CAAC,MAAM,IAAIC,GAAG,KAAK,GAAG,EAAE;MACpB,OAAOD,aAAa,GAAG,SAAS,GAAG,SAAS;IAChD,CAAC,MAAM;MACH,OAAOD,MAAM,GAAG,GAAG,GAAGN,MAAM,CAACS,MAAM,CAACD,GAAG,CAAC,EAAE,CAACF,MAAM,CAAC;IACtD;EACJ;EAEA,IAAIU,EAAE,GAAGjB,MAAM,CAACkB,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE;MACJT,MAAM,EAAE,sGAAsG,CAACL,KAAK,CAChH,GACJ,CAAC;MACDe,UAAU,EACN,oGAAoG,CAACf,KAAK,CACtG,GACJ;IACR,CAAC;IACDgB,WAAW,EACP,yDAAyD,CAAChB,KAAK,CAAC,GAAG,CAAC;IACxEiB,QAAQ,EAAE;MACNZ,MAAM,EAAE,yDAAyD,CAACL,KAAK,CACnE,GACJ,CAAC;MACDe,UAAU,EACN,yDAAyD,CAACf,KAAK,CAC3D,GACJ,CAAC;MACLkB,QAAQ,EAAE;IACd,CAAC;IACDC,aAAa,EAAE,sBAAsB,CAACnB,KAAK,CAAC,GAAG,CAAC;IAChDoB,WAAW,EAAE,sBAAsB,CAACpB,KAAK,CAAC,GAAG,CAAC;IAC9CqB,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,gBAAgB;MACpBC,GAAG,EAAE,uBAAuB;MAC5BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,cAAc;MACvBC,OAAO,EAAE,eAAe;MACxBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,OAAO,iBAAiB;MAC5B,CAAC;MACDC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,yBAAyB;UACpC,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,wBAAwB;QACvC;MACJ,CAAC;MACDC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,SAAS;MACfC,CAAC,EAAE,iBAAiB;MACpBC,CAAC,EAAEvC,sBAAsB;MACzBM,EAAE,EAAEN,sBAAsB;MAC1BwC,CAAC,EAAExC,sBAAsB;MACzBO,EAAE,EAAEP,sBAAsB;MAC1ByC,CAAC,EAAE,OAAO;MACVjC,EAAE,EAAER,sBAAsB;MAC1B0C,CAAC,EAAE,OAAO;MACVjC,EAAE,EAAET,sBAAsB;MAC1B2C,CAAC,EAAE,KAAK;MACRjC,EAAE,EAAEV;IACR,CAAC;IACD4C,aAAa,EAAE,wBAAwB;IACvCC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,gBAAgB,CAACC,IAAI,CAACD,KAAK,CAAC;IACvC,CAAC;IACDE,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,MAAM;MACjB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,QAAQ;MACnB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,KAAK;MAChB,CAAC,MAAM;QACH,OAAO,QAAQ;MACnB;IACJ,CAAC;IACDG,sBAAsB,EAAE,kBAAkB;IAC1CC,OAAO,EAAE,SAAAA,CAAUpD,MAAM,EAAEqD,MAAM,EAAE;MAC/B,QAAQA,MAAM;QACV,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,KAAK;QACV,KAAK,GAAG;QACR,KAAK,GAAG;UACJ,OAAO,CAACrD,MAAM,GAAG,EAAE,KAAK,CAAC,IAAIA,MAAM,GAAG,EAAE,KAAK,CAAC,KAC1CA,MAAM,GAAG,GAAG,KAAK,EAAE,IACnBA,MAAM,GAAG,GAAG,KAAK,EAAE,GACjBA,MAAM,GAAG,IAAI,GACbA,MAAM,GAAG,IAAI;QACvB,KAAK,GAAG;UACJ,OAAOA,MAAM,GAAG,KAAK;QACzB;UACI,OAAOA,MAAM;MACrB;IACJ,CAAC;IACDsD,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO9C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}